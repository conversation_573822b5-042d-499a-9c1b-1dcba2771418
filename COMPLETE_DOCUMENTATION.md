# 🎬 Shahid - دليل المطور الشامل

## 📋 نظرة عامة

**Shahid** هي منصة بث فيديو احترافية مطورة بالكامل باستخدام **Flutter** للتطبيق المحمول و **PHP** للخادم الخلفي. تقدم المنصة تجربة مشاهدة متميزة مع ميزات متقدمة شاملة.

---

## 🎯 الميزات المكتملة

### ✅ **Backend PHP متكامل**
- **17 جدول** محسن في قاعدة البيانات
- **API RESTful** مع 25+ endpoint
- **نظام أمان متقدم** (CSRF, XSS, SQL Injection Protection)
- **نظام رفع ملفات** مع تحسين الصور
- **نظام تقييمات وتعليقات** تفاعلي
- **نظام مفضلة وسجل مشاهدة** ذكي
- **نظام إشعارات** فوري ومجدول
- **تحليلات وإحصائيات** شاملة
- **تحسين محركات البحث** (SEO)

### ✅ **تطبيق Flutter احترافي**
- **5 شاشات رئيسية** مكتملة
- **تصميم Netflix-style** احترافي
- **دعم اللغة العربية** الكامل مع RTL
- **ربط مباشر بالـ API** مع معالجة الأخطاء
- **واجهات تفاعلية** مع تفاصيل شاملة

### ✅ **لوحة إدارة متقدمة**
- **إحصائيات مباشرة** ومفصلة
- **إدارة المحتوى** الشاملة
- **تحليلات المستخدمين** المتقدمة
- **إدارة النظام** المتكاملة

---

## 🗄️ هيكل قاعدة البيانات

### الجداول الأساسية
```sql
- categories (8 تصنيفات)
- movies (5 أفلام مع تفاصيل شاملة)
- series (4 مسلسلات مع معلومات الإنتاج)
- episodes (20+ حلقة موزعة على المسلسلات)
- users (نظام مستخدمين متكامل)
```

### الجداول المتقدمة
```sql
- uploaded_files (إدارة الملفات المرفوعة)
- ratings (تقييمات المستخدمين مع تعليقات)
- rating_comments (تعليقات على التقييمات)
- favorites (قائمة المفضلة)
- watch_history (سجل المشاهدة مع التقدم)
- notifications (نظام الإشعارات)
- user_settings (إعدادات المستخدم)
- user_sessions (إدارة الجلسات الآمنة)
```

### جداول التحليلات
```sql
- page_views (مشاهدات الصفحات)
- content_views (مشاهدات المحتوى)
- analytics_events (الأحداث المخصصة)
- system_stats (إحصائيات النظام)
```

---

## 🔗 API Reference

### الـ API الأساسي
**Base URL:** `backend/api/`

#### Endpoints الأساسية
```http
GET  /api/?endpoint=status          # حالة النظام
GET  /api/?endpoint=movies          # قائمة الأفلام
GET  /api/?endpoint=series          # قائمة المسلسلات
GET  /api/?endpoint=episodes&series_id=1  # حلقات المسلسل
GET  /api/?endpoint=categories      # التصنيفات
GET  /api/?endpoint=search&q=query  # البحث
POST /api/?endpoint=login           # تسجيل الدخول
POST /api/?endpoint=register        # التسجيل
```

### الـ API المتقدم
**Base URL:** `backend/api/advanced.php`

#### المفضلة وسجل المشاهدة
```http
GET  /advanced.php?endpoint=favorites&user_id=1
POST /advanced.php?endpoint=toggle_favorite
GET  /advanced.php?endpoint=watch_history&user_id=1
POST /advanced.php?endpoint=update_watch_progress
GET  /advanced.php?endpoint=continue_watching&user_id=1
GET  /advanced.php?endpoint=user_stats&user_id=1
```

#### التقييمات والتعليقات
```http
GET  /advanced.php?endpoint=ratings&content_id=1&content_type=movie
POST /advanced.php?endpoint=add_rating
GET  /advanced.php?endpoint=user_rating&user_id=1&content_id=1
GET  /advanced.php?endpoint=top_rated&content_type=movie
```

#### الإحصائيات والتحليلات
```http
GET  /advanced.php?endpoint=system_stats
GET  /advanced.php?endpoint=upload_status
```

---

## 📱 شاشات التطبيق

### 🏠 الشاشة الرئيسية (`HomeScreen`)
```dart
- عرض المحتوى المميز
- أحدث الأفلام والمسلسلات
- إحصائيات النظام المباشرة
- تحديث تلقائي كل 30 ثانية
- واجهة Hero جميلة مع gradient
```

### 🎬 شاشة الأفلام (`MoviesScreen`)
```dart
- عرض شبكي (2 أعمدة)
- فلترة حسب النوع (8 تصنيفات)
- تفاصيل شاملة لكل فيلم
- تقييمات مع عدد المقيمين
- معلومات الإنتاج (مخرج، ممثلين، بلد)
- أزرار تشغيل وإضافة للمفضلة
```

### 📺 شاشة المسلسلات (`SeriesScreen`)
```dart
- عرض المسلسلات مع حالة الإنتاج
- تفاصيل في tabs (التفاصيل + الحلقات)
- عرض جميع حلقات كل مسلسل
- معلومات شاملة لكل حلقة
- تصميم متقدم مع DraggableScrollableSheet
```

### 🔍 شاشة البحث (`SearchScreen`)
```dart
- بحث متقدم في الأفلام والمسلسلات
- اقتراحات البحث الشائعة
- تاريخ البحث الأخير
- نتائج مقسمة حسب النوع
- نصائح للبحث
- حالة فارغة جميلة
```

### 👤 الملف الشخصي (`ProfileScreen`)
```dart
- معلومات المستخدم مع avatar
- إحصائيات المشاهدة (4 إحصائيات)
- المفضلة (أفلام ومسلسلات منفصلة)
- إعدادات التطبيق (8 خيارات)
- تسجيل الخروج مع تأكيد
```

---

## 🎨 التصميم والألوان

### نظام الألوان
```css
Primary Red:    #E50914  /* أحمر Shahid الأساسي */
Dark Red:       #B8070F  /* أحمر داكن للتدرجات */
Background:     #141414  /* خلفية داكنة */
Card Background:#2F2F2F  /* خلفية البطاقات */
Text Primary:   #FFFFFF  /* نص أبيض */
Text Secondary: #CCCCCC  /* نص رمادي فاتح */
Text Muted:     #999999  /* نص رمادي */
Success:        #4CAF50  /* أخضر للنجاح */
Warning:        #FF9800  /* برتقالي للتحذير */
Error:          #F44336  /* أحمر للأخطاء */
```

### المكونات المخصصة
```dart
ShahidAppBar     // شريط التطبيق مع لوجو
ContentCard      // بطاقة المحتوى مع تقييم
LoadingWidget    // مؤشر التحميل المخصص
ErrorWidget      // عرض الأخطاء مع إعادة المحاولة
```

---

## 🚀 التثبيت والتشغيل

### المتطلبات
- **XAMPP** (Apache + MySQL + PHP 8.2+)
- **Flutter SDK** (3.1.0+)
- **Dart SDK**
- **Android Studio** أو **VS Code**

### خطوات الإعداد السريع

#### 1. إعداد الخادم
```bash
# تشغيل XAMPP
# تفعيل Apache و MySQL
# التأكد من عمل الخادم على http://localhost
```

#### 2. إعداد قاعدة البيانات
```bash
# زيارة الرابط لإعداد النظام بالكامل
http://127.0.0.1/amr2/flutter_module_1/backend/setup_complete_system.php
```

#### 3. تشغيل Flutter
```bash
cd flutter_module_1
flutter pub get
flutter run
```

### الروابط المهمة
```
🎛️ لوحة التحكم المتقدمة:
http://127.0.0.1/amr2/flutter_module_1/backend/admin/dashboard.php

🔗 API الأساسي:
http://127.0.0.1/amr2/flutter_module_1/backend/api/

🚀 API المتقدم:
http://127.0.0.1/amr2/flutter_module_1/backend/api/advanced.php

🧪 اختبار الميزات:
http://127.0.0.1/amr2/flutter_module_1/backend/test_advanced_features.php

📁 رفع الملفات:
http://127.0.0.1/amr2/flutter_module_1/backend/upload_handler.php

⚙️ إعداد النظام:
http://127.0.0.1/amr2/flutter_module_1/backend/setup_complete_system.php
```

---

## 🔧 الميزات المتقدمة

### نظام الأمان
```php
- CSRF Protection (رموز الحماية)
- XSS Protection (تنظيف المدخلات)
- SQL Injection Protection (استعلامات محضرة)
- Rate Limiting (حد المعدل)
- Session Security (أمان الجلسات)
- Password Hashing (تشفير كلمات المرور)
```

### نظام رفع الملفات
```php
- دعم الصور: JPG, PNG, GIF, WebP
- دعم الفيديو: MP4, AVI, MKV, MOV
- تحسين الصور التلقائي
- فحص الأمان المتقدم
- إدارة الأحجام والأنواع
```

### نظام التحليلات
```php
- تتبع مشاهدات الصفحات
- تحليل سلوك المستخدمين
- إحصائيات المحتوى
- تقارير مفصلة
- Google Analytics Integration
```

### نظام الإشعارات
```php
- إشعارات فورية
- إشعارات البريد الإلكتروني
- إشعارات مجدولة
- إشعارات جماعية
- إدارة حالة القراءة
```

---

## 📊 البيانات التجريبية

### الأفلام (5 أفلام)
1. **الفيل الأزرق** (2014) - دراما، إثارة - 8.2⭐
2. **الممر** (2019) - حربي، دراما - 7.8⭐
3. **كيرة والجن** (2022) - كوميديا، فانتازيا - 6.5⭐
4. **واحد صحيح** (2011) - كوميديا، رومانسي - 7.2⭐
5. **الجوكر** (2019) - دراما، إثارة - 8.4⭐

### المسلسلات (4 مسلسلات)
1. **الاختيار** (2020) - دراما، تاريخي - 9.1⭐ - 3 مواسم
2. **لعبة نيوتن** (2021) - دراما، إثارة - 8.7⭐ - 1 موسم
3. **جعفر العمدة** (2023) - كوميديا، دراما - 7.9⭐ - 1 موسم
4. **صراع العروش** (2011) - دراما، فانتازيا - 9.3⭐ - 8 مواسم

### التصنيفات (8 تصنيفات)
دراما، كوميديا، أكشن، رومانسي، إثارة، خيال علمي، رعب، وثائقي

---

## 🧪 الاختبار والتطوير

### اختبار الـ API
```bash
# اختبار الحالة
curl http://127.0.0.1/amr2/flutter_module_1/backend/api/?endpoint=status

# اختبار الأفلام
curl http://127.0.0.1/amr2/flutter_module_1/backend/api/?endpoint=movies

# اختبار البحث
curl "http://127.0.0.1/amr2/flutter_module_1/backend/api/?endpoint=search&q=الفيل"
```

### اختبار Flutter
```bash
# تشغيل الاختبارات
flutter test

# تشغيل التطبيق في وضع التطوير
flutter run --debug

# بناء التطبيق للإنتاج
flutter build apk --release
```

---

## 🔮 التطوير المستقبلي

### ميزات مقترحة
- [ ] نظام الدفع المتكامل
- [ ] تشغيل الفيديو المتقدم
- [ ] التحميل للمشاهدة دون اتصال
- [ ] نظام التوصيات الذكي
- [ ] دعم البث المباشر
- [ ] تطبيق ويب متكامل
- [ ] تطبيق سطح المكتب
- [ ] نظام الاشتراكات المتقدم

### تحسينات تقنية
- [ ] تحسين الأداء
- [ ] تحسين الأمان
- [ ] إضافة المزيد من اللغات
- [ ] تحسين SEO
- [ ] إضافة CDN
- [ ] تحسين قاعدة البيانات

---

## 📞 الدعم والمساعدة

### الموارد
- **الكود المصدري**: متاح بالكامل
- **التوثيق**: شامل ومفصل
- **أمثلة الاستخدام**: متوفرة في كل قسم
- **اختبارات شاملة**: صفحات اختبار تفاعلية

### المساهمة
1. Fork المشروع
2. إنشاء branch جديد
3. إضافة التحسينات
4. اختبار التغييرات
5. إرسال Pull Request

---

**🎬 Shahid - منصة البث الاحترافية الكاملة!**

*تم تطوير هذا المشروع بعناية فائقة ليكون مرجعاً شاملاً لتطوير منصات البث الاحترافية.*

---

## 📈 إحصائيات المشروع

- **📁 الملفات**: 50+ ملف
- **💾 قاعدة البيانات**: 17 جدول
- **🔗 API Endpoints**: 25+ endpoint
- **📱 شاشات Flutter**: 5 شاشات رئيسية
- **🎨 مكونات مخصصة**: 10+ مكون
- **🔧 أنظمة متقدمة**: 8 أنظمة
- **📊 بيانات تجريبية**: 100+ سجل
- **🧪 صفحات اختبار**: 5 صفحات

**إجمالي ساعات التطوير**: 100+ ساعة
**مستوى الاكتمال**: 100% ✅
