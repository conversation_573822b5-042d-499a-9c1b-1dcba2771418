<?php
/**
 * Test API Endpoints
 * Quick test to verify API is working
 */

echo "<h1>🔗 API Test Dashboard</h1>";

echo "<h2>📡 API Status:</h2>";

$baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);

$endpoints = [
    'GET /api/' => 'API Status',
    'GET /api/status' => 'Detailed Status',
    'GET /api/movies' => 'List Movies',
    'GET /api/series' => 'List Series',
    'GET /api/search?q=test' => 'Search Content',
    'POST /api/auth/login' => 'User Login',
    'POST /api/auth/register' => 'User Registration'
];

echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Endpoint</th><th>Description</th><th>Test</th><th>Status</th></tr>";

foreach ($endpoints as $endpoint => $description) {
    $parts = explode(' ', $endpoint);
    $method = $parts[0];
    $path = $parts[1];
    
    $testUrl = $baseUrl . $path;
    $status = 'Unknown';
    $statusColor = '#666';
    
    // Test the endpoint
    try {
        $context = stream_context_create([
            'http' => [
                'method' => $method,
                'header' => 'Content-Type: application/json',
                'timeout' => 5
            ]
        ]);
        
        $response = @file_get_contents($testUrl, false, $context);
        
        if ($response !== false) {
            $data = json_decode($response, true);
            if ($data && isset($data['success'])) {
                $status = $data['success'] ? 'Working ✅' : 'Error ❌';
                $statusColor = $data['success'] ? '#4CAF50' : '#f44336';
            } else {
                $status = 'Invalid Response ⚠️';
                $statusColor = '#ff9800';
            }
        } else {
            $status = 'Failed ❌';
            $statusColor = '#f44336';
        }
    } catch (Exception $e) {
        $status = 'Error ❌';
        $statusColor = '#f44336';
    }
    
    echo "<tr>";
    echo "<td><strong>$endpoint</strong></td>";
    echo "<td>$description</td>";
    echo "<td><a href='$testUrl' target='_blank' style='color: #E50914;'>Test</a></td>";
    echo "<td style='color: $statusColor;'>$status</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>🧪 Quick Tests:</h2>";

// Test API status
echo "<h3>1. API Status Test:</h3>";
try {
    $statusUrl = $baseUrl . '/status';
    $response = @file_get_contents($statusUrl);
    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "<p>✅ <strong>API Status:</strong> Working</p>";
            if (isset($data['data']['database'])) {
                $db = $data['data']['database'];
                echo "<p>📊 <strong>Database:</strong> " . ($db['connected'] ? 'Connected' : 'Disconnected') . "</p>";
                if ($db['connected']) {
                    echo "<p>🎬 <strong>Movies:</strong> " . ($db['movies'] ?? 0) . "</p>";
                    echo "<p>📺 <strong>Series:</strong> " . ($db['series'] ?? 0) . "</p>";
                    echo "<p>👥 <strong>Users:</strong> " . ($db['users'] ?? 0) . "</p>";
                }
            }
        } else {
            echo "<p>❌ <strong>API Status:</strong> Error</p>";
        }
    } else {
        echo "<p>❌ <strong>API Status:</strong> No Response</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ <strong>API Status:</strong> Exception - " . $e->getMessage() . "</p>";
}

// Test movies endpoint
echo "<h3>2. Movies Endpoint Test:</h3>";
try {
    $moviesUrl = $baseUrl . '/movies?limit=3';
    $response = @file_get_contents($moviesUrl);
    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            $movies = $data['data']['movies'] ?? [];
            echo "<p>✅ <strong>Movies Endpoint:</strong> Working (" . count($movies) . " movies)</p>";
            if (!empty($movies)) {
                echo "<ul>";
                foreach (array_slice($movies, 0, 3) as $movie) {
                    echo "<li>" . htmlspecialchars($movie['title']) . " (" . ($movie['year'] ?? 'N/A') . ")</li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<p>❌ <strong>Movies Endpoint:</strong> Error</p>";
        }
    } else {
        echo "<p>❌ <strong>Movies Endpoint:</strong> No Response</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ <strong>Movies Endpoint:</strong> Exception - " . $e->getMessage() . "</p>";
}

// Test search endpoint
echo "<h3>3. Search Endpoint Test:</h3>";
try {
    $searchUrl = $baseUrl . '/search?q=test';
    $response = @file_get_contents($searchUrl);
    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            $results = $data['data']['results'] ?? [];
            echo "<p>✅ <strong>Search Endpoint:</strong> Working (" . count($results) . " results)</p>";
        } else {
            echo "<p>❌ <strong>Search Endpoint:</strong> Error</p>";
        }
    } else {
        echo "<p>❌ <strong>Search Endpoint:</strong> No Response</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ <strong>Search Endpoint:</strong> Exception - " . $e->getMessage() . "</p>";
}

echo "<h2>📋 API Documentation:</h2>";
echo "<div style='background: #f5f5f5; padding: 20px; border-radius: 5px;'>";
echo "<h3>Available Endpoints:</h3>";
echo "<pre>";
echo "GET  /api/status           - API status and database info
GET  /api/movies           - List all movies (paginated)
GET  /api/movies/{id}      - Get specific movie details
GET  /api/series           - List all series (paginated)
GET  /api/series/{id}      - Get specific series with episodes
GET  /api/search?q={query} - Search movies and series
POST /api/auth/login       - User login (email, password)
POST /api/auth/register    - User registration (name, email, password)
GET  /api/user/profile     - User profile (requires auth)";
echo "</pre>";

echo "<h3>Example Usage:</h3>";
echo "<pre>";
echo "// Get movies
curl -X GET '$baseUrl/movies?page=1&limit=10'

// Search content
curl -X GET '$baseUrl/search?q=matrix'

// Login user
curl -X POST '$baseUrl/auth/login' \\
  -H 'Content-Type: application/json' \\
  -d '{\"email\":\"<EMAIL>\",\"password\":\"password123\"}'

// Register user
curl -X POST '$baseUrl/auth/register' \\
  -H 'Content-Type: application/json' \\
  -d '{\"name\":\"John Doe\",\"email\":\"<EMAIL>\",\"password\":\"password123\"}'";
echo "</pre>";
echo "</div>";

echo "<h2>🔗 Quick Links:</h2>";
echo "<p>";
echo "<a href='$baseUrl/status' target='_blank' style='margin-right: 10px; color: #E50914;'>📊 API Status</a>";
echo "<a href='$baseUrl/movies' target='_blank' style='margin-right: 10px; color: #E50914;'>🎬 Movies</a>";
echo "<a href='$baseUrl/series' target='_blank' style='margin-right: 10px; color: #E50914;'>📺 Series</a>";
echo "<a href='$baseUrl/search?q=test' target='_blank' style='margin-right: 10px; color: #E50914;'>🔍 Search</a>";
echo "<a href='../index_simple.php' target='_blank' style='margin-right: 10px; color: #E50914;'>🏠 Homepage</a>";
echo "<a href='../test_homepage.php' target='_blank' style='margin-right: 10px; color: #E50914;'>🧪 Test Homepage</a>";
echo "</p>";

echo "<h2>🛠️ Troubleshooting:</h2>";
echo "<ul>";
echo "<li><strong>If API returns errors:</strong> Check database connection and installation</li>";
echo "<li><strong>If endpoints don't work:</strong> Check .htaccess configuration</li>";
echo "<li><strong>If CORS issues:</strong> Check browser console and CORS headers</li>";
echo "<li><strong>If 404 errors:</strong> Ensure URL rewriting is enabled</li>";
echo "</ul>";

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }
h1 { color: #E50914; border-bottom: 3px solid #E50914; padding-bottom: 10px; }
h2 { color: #333; margin-top: 30px; border-left: 4px solid #E50914; padding-left: 15px; }
h3 { color: #555; margin-top: 20px; }
p { margin: 10px 0; padding: 8px; background: white; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
table { background: white; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin: 20px 0; }
th { background: #E50914; color: white; }
td { padding: 8px; }
a { color: #E50914; text-decoration: none; font-weight: bold; }
a:hover { text-decoration: underline; }
pre { background: #f1f1f1; padding: 15px; border-radius: 5px; overflow-x: auto; }
ul { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
li { margin: 8px 0; }
</style>";
?>
