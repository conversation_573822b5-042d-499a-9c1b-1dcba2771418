/* Shahid Video Player Stylesheet */

:root {
    --player-bg: #000000;
    --player-overlay: rgba(0, 0, 0, 0.7);
    --player-controls: rgba(0, 0, 0, 0.8);
    --player-primary: #e50914;
    --player-text: #ffffff;
    --player-text-muted: #cccccc;
    --player-border: rgba(255, 255, 255, 0.2);
    --player-transition: all 0.3s ease;
    --player-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

/* Player Body */
.player-body {
    margin: 0;
    padding: 0;
    background: var(--player-bg);
    font-family: 'Cairo', sans-serif;
    overflow: hidden;
    height: 100vh;
}

.player-body.fullscreen-mode {
    cursor: none;
}

.player-body.hide-cursor {
    cursor: none;
}

/* Player Container */
.player-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    background: var(--player-bg);
    display: flex;
    flex-direction: column;
}

/* Top Bar */
.player-topbar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(to bottom, var(--player-overlay), transparent);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--player-transition);
}

.player-topbar.hidden {
    opacity: 0;
    pointer-events: none;
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.back-btn {
    color: var(--player-text);
    text-decoration: none;
    font-weight: 600;
    transition: var(--player-transition);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--player-text);
}

.movie-info {
    color: var(--player-text);
}

.movie-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin: 0;
    color: var(--player-text);
}

.movie-meta {
    font-size: 0.9rem;
    color: var(--player-text-muted);
}

.settings-btn,
.fullscreen-btn {
    background: none;
    border: none;
    color: var(--player-text);
    font-size: 1.2rem;
    padding: 0.75rem;
    border-radius: 50%;
    transition: var(--player-transition);
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.settings-btn:hover,
.fullscreen-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--player-text);
}

/* Video Wrapper */
.video-wrapper {
    position: relative;
    flex: 1;
    background: var(--player-bg);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Video.js Customization */
.video-js {
    width: 100% !important;
    height: 100% !important;
    background: var(--player-bg);
}

.video-js .vjs-big-play-button {
    background: var(--player-primary);
    border: none;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    font-size: 2rem;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: var(--player-transition);
}

.video-js .vjs-big-play-button:hover {
    background: #b20710;
    transform: translate(-50%, -50%) scale(1.1);
}

.video-js .vjs-control-bar {
    background: var(--player-controls);
    backdrop-filter: blur(10px);
    height: 60px;
    padding: 0 1rem;
}

.video-js .vjs-play-control,
.video-js .vjs-volume-control,
.video-js .vjs-time-control,
.video-js .vjs-fullscreen-control {
    color: var(--player-text);
}

.video-js .vjs-play-control:hover,
.video-js .vjs-volume-control:hover,
.video-js .vjs-fullscreen-control:hover {
    color: var(--player-primary);
}

.video-js .vjs-progress-control {
    height: 6px;
    margin: 0 1rem;
}

.video-js .vjs-progress-holder {
    background: rgba(255, 255, 255, 0.3);
    height: 6px;
    border-radius: 3px;
}

.video-js .vjs-play-progress {
    background: var(--player-primary);
    border-radius: 3px;
}

.video-js .vjs-load-progress {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.loading-spinner {
    text-align: center;
    color: var(--player-text);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--player-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Overlay */
.error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.error-content {
    text-align: center;
    color: var(--player-text);
    max-width: 400px;
    padding: 2rem;
}

.error-content i {
    font-size: 4rem;
    color: #dc3545;
    margin-bottom: 1rem;
}

.error-content h4 {
    margin-bottom: 1rem;
    color: var(--player-text);
}

.error-content p {
    color: var(--player-text-muted);
    margin-bottom: 2rem;
}

/* Skip Intro Button */
.skip-intro-btn {
    position: absolute;
    bottom: 100px;
    right: 2rem;
    background: var(--player-controls);
    color: var(--player-text);
    border: 1px solid var(--player-border);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    transition: var(--player-transition);
    backdrop-filter: blur(10px);
    z-index: 998;
}

.skip-intro-btn:hover {
    background: var(--player-primary);
    border-color: var(--player-primary);
    color: var(--player-text);
}

/* Next Episode Button */
.next-episode-btn {
    position: absolute;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--player-primary);
    color: var(--player-text);
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--player-transition);
    z-index: 998;
    box-shadow: var(--player-shadow);
}

.next-episode-btn:hover {
    background: #b20710;
    transform: translateX(-50%) scale(1.05);
}

/* Bottom Controls */
.player-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, var(--player-overlay));
    padding: 1rem 2rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    transition: var(--player-transition);
}

.player-controls.hidden {
    opacity: 0;
    pointer-events: none;
}

.controls-left,
.controls-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.controls-center {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.player-controls .btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--player-text);
    border: 1px solid var(--player-border);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: var(--player-transition);
    backdrop-filter: blur(10px);
}

.player-controls .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--player-text);
}

.playback-speed {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--player-text);
    font-size: 0.9rem;
}

.playback-speed select {
    background: rgba(255, 255, 255, 0.1);
    color: var(--player-text);
    border: 1px solid var(--player-border);
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.playback-speed select:focus {
    outline: none;
    border-color: var(--player-primary);
}

/* Resume Notification */
.resume-notification {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--player-controls);
    color: var(--player-text);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    backdrop-filter: blur(20px);
    border: 1px solid var(--player-border);
    z-index: 999;
    box-shadow: var(--player-shadow);
}

.resume-notification p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.resume-notification .btn {
    margin: 0 0.5rem;
}

/* Seek Indicator */
.seek-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--player-controls);
    color: var(--player-text);
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    backdrop-filter: blur(20px);
    border: 1px solid var(--player-border);
    z-index: 998;
    animation: fadeInOut 1s ease;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
}

/* Modal Customization */
.modal-content {
    background: var(--player-controls);
    border: 1px solid var(--player-border);
    backdrop-filter: blur(20px);
}

.modal-header,
.modal-footer {
    border-color: var(--player-border);
}

.modal-title {
    color: var(--player-text);
}

.modal-body {
    color: var(--player-text);
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-label {
    display: block;
    color: var(--player-text);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-select,
.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--player-border);
    color: var(--player-text);
    backdrop-filter: blur(10px);
}

.form-select:focus,
.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--player-primary);
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    color: var(--player-text);
}

.form-select option {
    background: var(--player-bg);
    color: var(--player-text);
}

.form-check-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--player-border);
}

.form-check-input:checked {
    background: var(--player-primary);
    border-color: var(--player-primary);
}

.form-check-label {
    color: var(--player-text);
}

/* Share Options */
.share-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn-social {
    padding: 0.75rem;
    border-radius: 10px;
    font-weight: 600;
    transition: var(--player-transition);
}

.btn-facebook {
    background: #4267B2;
    border-color: #4267B2;
    color: white;
}

.btn-twitter {
    background: #1DA1F2;
    border-color: #1DA1F2;
    color: white;
}

.btn-whatsapp {
    background: #25D366;
    border-color: #25D366;
    color: white;
}

.btn-telegram {
    background: #0088cc;
    border-color: #0088cc;
    color: white;
}

.share-link {
    margin-top: 1rem;
}

.share-link .form-label {
    color: var(--player-text);
    font-weight: 600;
}

/* Toast Notifications */
.toast-notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    z-index: 1060;
    display: none;
    box-shadow: var(--player-shadow);
    backdrop-filter: blur(20px);
}

.toast-success {
    background: rgba(40, 167, 69, 0.9);
}

.toast-error {
    background: rgba(220, 53, 69, 0.9);
}

.toast-warning {
    background: rgba(255, 193, 7, 0.9);
    color: #212529;
}

.toast-info {
    background: rgba(23, 162, 184, 0.9);
}

/* Responsive Design */
@media (max-width: 768px) {
    .player-topbar {
        padding: 1rem;
    }
    
    .movie-title {
        font-size: 1rem;
    }
    
    .movie-meta {
        font-size: 0.8rem;
    }
    
    .player-controls {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .controls-left,
    .controls-right,
    .controls-center {
        justify-content: center;
    }
    
    .skip-intro-btn,
    .next-episode-btn {
        bottom: 120px;
        font-size: 0.9rem;
        padding: 0.6rem 1.2rem;
    }
    
    .share-options {
        grid-template-columns: 1fr;
    }
    
    .toast-notification {
        top: 1rem;
        right: 1rem;
        left: 1rem;
    }
}

@media (max-width: 480px) {
    .player-topbar {
        padding: 0.75rem;
    }
    
    .topbar-left {
        gap: 0.5rem;
    }
    
    .movie-title {
        font-size: 0.9rem;
    }
    
    .back-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
    
    .settings-btn,
    .fullscreen-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .video-js .vjs-big-play-button {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .skip-intro-btn {
        right: 1rem;
        bottom: 100px;
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .player-topbar,
    .player-controls {
        background: rgba(0, 0, 0, 0.95);
    }
    
    .btn {
        border: 2px solid var(--player-text);
    }
    
    .modal-content {
        background: var(--player-bg);
        border: 2px solid var(--player-text);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .video-js .vjs-big-play-button:hover {
        transform: translate(-50%, -50%);
    }
    
    .next-episode-btn:hover {
        transform: translateX(-50%);
    }
}
