<?php
/**
 * Test API Endpoints - Shahid Platform
 * Quick test to verify all API endpoints work
 */

echo "<h1>🔧 اختبار API Endpoints - Shahid Platform</h1>";

$baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
$apiBaseUrl = $baseUrl . '/api';

// Test endpoints
$endpoints = [
    '' => 'الصفحة الرئيسية للـ API',
    'status' => 'حالة النظام',
    'movies' => 'قائمة الأفلام',
    'series' => 'قائمة المسلسلات',
    'search?q=test' => 'البحث',
    'index_simple.php' => 'API البسيط مباشرة',
    'index_simple.php?endpoint=status' => 'حالة النظام (مع معامل)',
    'index_simple.php?endpoint=movies' => 'الأفلام (مع معامل)'
];

echo "<h2>🔗 اختبار جميع Endpoints:</h2>";

foreach ($endpoints as $endpoint => $description) {
    $url = $apiBaseUrl . '/' . $endpoint;
    
    echo "<h3>🧪 اختبار: $description</h3>";
    echo "<p><strong>URL:</strong> <a href='$url' target='_blank'>$url</a></p>";
    
    try {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10,
                'header' => [
                    'Content-Type: application/json',
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                ]
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            $error = error_get_last();
            echo "<div class='error'>❌ فشل في الوصول: " . htmlspecialchars($error['message'] ?? 'Unknown error') . "</div>";
        } else {
            $responseLength = strlen($response);
            
            // Try to decode as JSON
            $data = json_decode($response, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                if (isset($data['success'])) {
                    if ($data['success']) {
                        echo "<div class='success'>✅ يعمل بشكل صحيح - JSON صالح</div>";
                        
                        // Show some data info
                        if (isset($data['data'])) {
                            if (is_array($data['data'])) {
                                echo "<p>📊 عدد العناصر: " . count($data['data']) . "</p>";
                            }
                        }
                        
                        if (isset($data['message'])) {
                            echo "<p>💬 الرسالة: " . htmlspecialchars($data['message']) . "</p>";
                        }
                        
                    } else {
                        echo "<div class='warning'>⚠️ JSON صالح لكن success = false</div>";
                        if (isset($data['error'])) {
                            echo "<p>❌ الخطأ: " . htmlspecialchars($data['error']) . "</p>";
                        }
                    }
                } else {
                    echo "<div class='info'>ℹ️ JSON صالح بدون حقل success</div>";
                }
            } else {
                // Not JSON, check if HTML
                if (strpos($response, '<!DOCTYPE html>') !== false || strpos($response, '<html>') !== false) {
                    echo "<div class='info'>📄 استجابة HTML (حجم: $responseLength بايت)</div>";
                    
                    // Check for errors in HTML
                    if (strpos($response, 'Fatal error') !== false) {
                        echo "<div class='error'>❌ يحتوي على Fatal Error</div>";
                    } elseif (strpos($response, 'Warning') !== false) {
                        echo "<div class='warning'>⚠️ يحتوي على تحذيرات PHP</div>";
                    } elseif (strpos($response, 'Forbidden') !== false) {
                        echo "<div class='error'>❌ يحتوي على رسالة Forbidden</div>";
                    } else {
                        echo "<div class='success'>✅ صفحة HTML تعمل</div>";
                    }
                } else {
                    echo "<div class='warning'>⚠️ استجابة غير JSON/HTML (حجم: $responseLength بايت)</div>";
                }
            }
            
            // Show response preview
            if ($responseLength > 0 && $responseLength < 1000) {
                echo "<details><summary>عرض الاستجابة الكاملة</summary>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
                echo "</details>";
            } elseif ($responseLength > 0) {
                $preview = substr($response, 0, 300);
                echo "<details><summary>عرض أول 300 حرف من الاستجابة</summary>";
                echo "<pre>" . htmlspecialchars($preview) . "...</pre>";
                echo "</details>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في الاختبار: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "<hr>";
}

// Test direct PHP file access
echo "<h2>📄 اختبار الوصول المباشر للملفات:</h2>";

$files = [
    'test_api.php' => 'صفحة اختبار API',
    'index_simple.php' => 'API البسيط',
    'index.html' => 'صفحة HTML'
];

foreach ($files as $file => $description) {
    $url = $apiBaseUrl . '/' . $file;
    
    echo "<h4>📄 $description:</h4>";
    echo "<p><strong>URL:</strong> <a href='$url' target='_blank'>$url</a></p>";
    
    if (file_exists(__DIR__ . '/api/' . $file)) {
        echo "<div class='success'>✅ الملف موجود</div>";
        
        // Test access
        $response = @file_get_contents($url);
        if ($response !== false) {
            echo "<div class='success'>✅ يمكن الوصول إليه</div>";
        } else {
            echo "<div class='error'>❌ لا يمكن الوصول إليه</div>";
        }
    } else {
        echo "<div class='error'>❌ الملف غير موجود</div>";
    }
    
    echo "<hr>";
}

// Recommendations
echo "<h2>💡 التوصيات:</h2>";

echo "<div class='info'>";
echo "<h3>🔧 إذا كانت الـ endpoints لا تعمل:</h3>";
echo "<ol>";
echo "<li><strong>تحقق من .htaccess:</strong> تأكد من وجود قواعد إعادة التوجيه</li>";
echo "<li><strong>تحقق من mod_rewrite:</strong> تأكد من تفعيله في Apache</li>";
echo "<li><strong>تحقق من قاعدة البيانات:</strong> تأكد من وجود الجداول والبيانات</li>";
echo "<li><strong>تحقق من الأخطاء:</strong> راجع error logs في Apache</li>";
echo "</ol>";
echo "</div>";

echo "<div class='success'>";
echo "<h3>✅ الحلول المطبقة:</h3>";
echo "<ul>";
echo "<li>تحديث معالجة المسارات في index_simple.php</li>";
echo "<li>إضافة معامل endpoint في .htaccess</li>";
echo "<li>إضافة صفحة HTML احتياطية</li>";
echo "<li>تحسين معالجة الأخطاء</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔗 الروابط المباشرة:</h3>";
echo "<p><a href='api/' target='_blank' class='btn btn-primary'>🔗 صفحة API الرئيسية</a></p>";
echo "<p><a href='api/test_api.php' target='_blank' class='btn btn-info'>🧪 صفحة اختبار API</a></p>";
echo "<p><a href='api/status' target='_blank' class='btn btn-success'>📊 حالة النظام</a></p>";
echo "<p><a href='api/movies' target='_blank' class='btn btn-warning'>🎬 قائمة الأفلام</a></p>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 40px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1 {
    color: #E50914;
    border-bottom: 3px solid #E50914;
    padding-bottom: 10px;
    margin-bottom: 30px;
}

h2 {
    color: #333;
    margin-top: 30px;
    margin-bottom: 15px;
    border-left: 4px solid #E50914;
    padding-left: 15px;
}

h3, h4 {
    color: #555;
    margin-top: 20px;
    margin-bottom: 10px;
}

.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: all 0.3s;
}

.btn-primary { background: #E50914; color: white; }
.btn-info { background: #17a2b8; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-warning { background: #ffc107; color: black; }

.btn:hover {
    opacity: 0.8;
    text-decoration: none;
}

hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 20px 0;
}

details {
    margin: 10px 0;
}

summary {
    cursor: pointer;
    font-weight: bold;
    color: #E50914;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 0.9em;
    border: 1px solid #ddd;
    max-height: 300px;
    overflow-y: auto;
}

p {
    margin: 10px 0;
}

ol, ul {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}

a {
    color: #E50914;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
