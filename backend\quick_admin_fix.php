<?php
/**
 * Shahid Platform - Quick Admin Fix
 * Fast admin account creation/repair
 */

header('Content-Type: application/json');

try {
    // إعدادات قاعدة البيانات
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $database = 'shahid_platform';
    
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من وجود جدول المستخدمين
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        // إنشاء جدول المستخدمين
        $pdo->exec("
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role ENUM('user', 'admin') DEFAULT 'user',
                subscription_type ENUM('free', 'basic', 'premium') DEFAULT 'free',
                subscription_end DATETIME NULL,
                profile_image VARCHAR(255) NULL,
                status ENUM('active', 'banned', 'deleted') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
    }
    
    // التحقق من وجود حساب المدير
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin' AND email = '<EMAIL>'");
    $stmt->execute();
    $adminExists = $stmt->fetchColumn() > 0;
    
    if (!$adminExists) {
        // حذف أي حساب مدير قديم
        $pdo->exec("DELETE FROM users WHERE email = '<EMAIL>'");
        
        // إنشاء حساب المدير الجديد
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (name, email, password, role, subscription_type, subscription_end, status, created_at) 
            VALUES (?, ?, ?, 'admin', 'premium', DATE_ADD(NOW(), INTERVAL 10 YEAR), 'active', NOW())
        ");
        
        $stmt->execute([
            'مدير النظام',
            '<EMAIL>',
            $adminPassword
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إنشاء حساب المدير بنجاح',
            'admin_email' => '<EMAIL>',
            'admin_password' => 'admin123',
            'action' => 'created'
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'message' => 'حساب المدير موجود بالفعل',
            'admin_email' => '<EMAIL>',
            'admin_password' => 'admin123',
            'action' => 'exists'
        ]);
    }
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'message' => 'خطأ في قاعدة البيانات',
        'solution' => 'تأكد من تشغيل MySQL في XAMPP'
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'message' => 'خطأ عام في النظام'
    ]);
}
?>
