<?php
/**
 * Shahid Series API
 * Professional Video Streaming Platform
 */

require_once '../core/Database.php';
require_once '../core/Security.php';
require_once '../core/ApiController.php';
require_once '../models/Series.php';
require_once '../models/Episode.php';

class SeriesAPI extends ApiController {
    
    public function handleRequest() {
        $route = trim($_SERVER['REQUEST_URI'], '/');
        $segments = explode('/', $route);
        
        // Remove 'api/series' from segments
        $segments = array_slice($segments, 2);
        
        $action = $segments[0] ?? null;
        $id = $segments[1] ?? null;
        
        switch ($action) {
            case null:
            case '':
                $this->getAllSeries();
                break;
            case 'featured':
                $this->getFeaturedSeries();
                break;
            case 'popular':
                $this->getPopularSeries();
                break;
            case 'latest':
                $this->getLatestSeries();
                break;
            case 'genres':
                $this->getGenres();
                break;
            default:
                if (is_numeric($action)) {
                    $this->getSeriesById($action);
                } else {
                    $this->sendError('Invalid series endpoint', 404);
                }
        }
    }
    
    private function getAllSeries() {
        if (!$this->validateMethod(['GET'])) return;
        
        $pagination = $this->getPaginationParams();
        $genre = $_GET['genre'] ?? '';
        $year = $_GET['year'] ?? '';
        $sort = $_GET['sort'] ?? 'latest';
        $search = $_GET['search'] ?? '';
        
        $seriesModel = new Series();
        
        $filters = [
            'genre' => $genre,
            'year' => $year,
            'search' => $search
        ];
        
        $series = $seriesModel->getSeriesForAPI($pagination['page'], $pagination['limit'], $filters, $sort);
        $total = $seriesModel->countSeries($filters);
        
        $this->sendPaginatedResponse($series, $total, $pagination['page'], $pagination['limit']);
    }
    
    private function getSeriesById($id) {
        if (!$this->validateMethod(['GET'])) return;
        
        $seriesModel = new Series();
        $series = $seriesModel->getSeriesDetailsForAPI($id);
        
        if (!$series) {
            $this->sendError('Series not found', 404);
            return;
        }
        
        // Get episodes grouped by season
        $episodes = $seriesModel->getEpisodesGroupedBySeason($id);
        $series['seasons'] = $episodes;
        
        // Get related series
        $relatedSeries = $seriesModel->getRelatedSeries($id, 6);
        $series['related'] = $relatedSeries;
        
        // Increment view count
        $seriesModel->incrementViews($id);
        
        $this->sendSuccess($series);
    }
    
    private function getFeaturedSeries() {
        if (!$this->validateMethod(['GET'])) return;
        
        $limit = min(20, intval($_GET['limit'] ?? 10));
        $seriesModel = new Series();
        $series = $seriesModel->getFeaturedSeries($limit);
        
        $this->sendSuccess($series);
    }
    
    private function getPopularSeries() {
        if (!$this->validateMethod(['GET'])) return;
        
        $limit = min(20, intval($_GET['limit'] ?? 10));
        $seriesModel = new Series();
        $series = $seriesModel->getPopularSeries($limit);
        
        $this->sendSuccess($series);
    }
    
    private function getLatestSeries() {
        if (!$this->validateMethod(['GET'])) return;
        
        $limit = min(20, intval($_GET['limit'] ?? 10));
        $seriesModel = new Series();
        $series = $seriesModel->getLatestSeries($limit);
        
        $this->sendSuccess($series);
    }
    
    private function getGenres() {
        if (!$this->validateMethod(['GET'])) return;
        
        $seriesModel = new Series();
        $genres = $seriesModel->getAllGenres();
        
        $this->sendSuccess($genres);
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$api = new SeriesAPI();
$api->handleRequest();
?>
