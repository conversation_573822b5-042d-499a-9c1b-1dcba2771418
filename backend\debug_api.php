<?php
/**
 * Debug API Access - Shahid Platform
 * Diagnose the 403 Forbidden issue
 */

echo "<h1>🔍 تشخيص مشكلة API - Shahid Platform</h1>";

$baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
$apiBaseUrl = $baseUrl . '/api';

// Test basic file access
echo "<h2>📁 اختبار الوصول للملفات الأساسية:</h2>";

$basicFiles = [
    'simple_test.php' => 'ملف اختبار بسيط',
    'index.html' => 'صفحة HTML',
    'api.php' => 'API الجديد',
    'test_api.php' => 'صفحة اختبار API'
];

foreach ($basicFiles as $file => $description) {
    $url = $apiBaseUrl . '/' . $file;
    
    echo "<h3>📄 اختبار: $description</h3>";
    echo "<p><strong>URL:</strong> <a href='$url' target='_blank'>$url</a></p>";
    
    // Check if file exists locally
    $localPath = __DIR__ . '/api/' . $file;
    if (file_exists($localPath)) {
        echo "<div class='success'>✅ الملف موجود محلياً</div>";
        
        $size = filesize($localPath);
        $perms = substr(sprintf('%o', fileperms($localPath)), -4);
        echo "<p>📊 الحجم: $size بايت | الصلاحيات: $perms</p>";
        
        // Test HTTP access
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 5,
                'header' => 'User-Agent: Mozilla/5.0'
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            $error = error_get_last();
            $errorMsg = $error['message'] ?? 'Unknown error';
            
            if (strpos($errorMsg, '403') !== false) {
                echo "<div class='error'>❌ خطأ 403 Forbidden</div>";
            } elseif (strpos($errorMsg, '404') !== false) {
                echo "<div class='error'>❌ خطأ 404 Not Found</div>";
            } elseif (strpos($errorMsg, '500') !== false) {
                echo "<div class='error'>❌ خطأ 500 Internal Server Error</div>";
            } else {
                echo "<div class='error'>❌ خطأ: " . htmlspecialchars($errorMsg) . "</div>";
            }
        } else {
            echo "<div class='success'>✅ يمكن الوصول إليه عبر HTTP</div>";
            echo "<p>📏 حجم الاستجابة: " . strlen($response) . " بايت</p>";
            
            if (strlen($response) < 500) {
                echo "<details><summary>عرض الاستجابة</summary>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
                echo "</details>";
            }
        }
        
    } else {
        echo "<div class='error'>❌ الملف غير موجود محلياً</div>";
    }
    
    echo "<hr>";
}

// Test directory access
echo "<h2>📂 اختبار الوصول للمجلد:</h2>";

$dirUrl = $apiBaseUrl . '/';
echo "<p><strong>URL المجلد:</strong> <a href='$dirUrl' target='_blank'>$dirUrl</a></p>";

$response = @file_get_contents($dirUrl);
if ($response === false) {
    $error = error_get_last();
    echo "<div class='error'>❌ لا يمكن الوصول للمجلد: " . htmlspecialchars($error['message'] ?? 'Unknown error') . "</div>";
} else {
    echo "<div class='success'>✅ يمكن الوصول للمجلد</div>";
    
    if (strpos($response, 'Index of') !== false) {
        echo "<div class='info'>📋 يعرض فهرس المجلد</div>";
    } elseif (strpos($response, '<!DOCTYPE html>') !== false) {
        echo "<div class='info'>📄 يعرض صفحة HTML</div>";
    }
}

// Check .htaccess
echo "<h2>⚙️ فحص ملف .htaccess:</h2>";

$htaccessPath = __DIR__ . '/api/.htaccess';
if (file_exists($htaccessPath)) {
    echo "<div class='success'>✅ ملف .htaccess موجود</div>";
    
    $content = file_get_contents($htaccessPath);
    $size = strlen($content);
    echo "<p>📏 حجم الملف: $size بايت</p>";
    
    echo "<details><summary>عرض محتوى .htaccess</summary>";
    echo "<pre>" . htmlspecialchars($content) . "</pre>";
    echo "</details>";
    
} else {
    echo "<div class='warning'>⚠️ ملف .htaccess غير موجود</div>";
}

// Check server info
echo "<h2>🖥️ معلومات الخادم:</h2>";

echo "<div class='info'>";
echo "<p><strong>نظام التشغيل:</strong> " . PHP_OS . "</p>";
echo "<p><strong>إصدار PHP:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>خادم الويب:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد') . "</p>";
echo "<p><strong>المجلد الحالي:</strong> " . __DIR__ . "</p>";
echo "<p><strong>مجلد API:</strong> " . __DIR__ . '/api' . "</p>";
echo "</div>";

// Check Apache modules (if available)
echo "<h2>🔧 فحص وحدات Apache:</h2>";

if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    $importantModules = ['mod_rewrite', 'mod_dir', 'mod_authz_core'];
    
    foreach ($importantModules as $module) {
        if (in_array($module, $modules)) {
            echo "<div class='success'>✅ $module مفعل</div>";
        } else {
            echo "<div class='error'>❌ $module غير مفعل</div>";
        }
    }
} else {
    echo "<div class='warning'>⚠️ لا يمكن فحص وحدات Apache (دالة apache_get_modules غير متاحة)</div>";
}

// Recommendations
echo "<h2>💡 التوصيات لحل المشكلة:</h2>";

echo "<div class='info'>";
echo "<h3>🔧 خطوات الحل:</h3>";
echo "<ol>";
echo "<li><strong>تحقق من XAMPP:</strong> تأكد من تشغيل Apache بشكل صحيح</li>";
echo "<li><strong>أعد تشغيل Apache:</strong> من XAMPP Control Panel</li>";
echo "<li><strong>تحقق من httpd.conf:</strong> تأكد من السماح بـ .htaccess في مجلد htdocs</li>";
echo "<li><strong>تحقق من صلاحيات المجلدات:</strong> في Windows، تأكد من عدم وجود قيود</li>";
echo "<li><strong>جرب بدون .htaccess:</strong> احذف أو أعد تسمية .htaccess مؤقتاً</li>";
echo "</ol>";
echo "</div>";

echo "<div class='warning'>";
echo "<h3>⚠️ إذا استمرت المشكلة:</h3>";
echo "<ul>";
echo "<li>جرب الوصول المباشر للملفات: <code>api/simple_test.php</code></li>";
echo "<li>تحقق من error logs في XAMPP</li>";
echo "<li>تأكد من عدم وجود برامج حماية تحجب الوصول</li>";
echo "<li>جرب تشغيل XAMPP كمدير (Run as Administrator)</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔗 روابط الاختبار المباشر:</h3>";
echo "<p><a href='api/simple_test.php' target='_blank' class='btn btn-primary'>🧪 ملف الاختبار البسيط</a></p>";
echo "<p><a href='api/index.html' target='_blank' class='btn btn-info'>📄 صفحة HTML</a></p>";
echo "<p><a href='api/' target='_blank' class='btn btn-warning'>📂 مجلد API</a></p>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 40px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1 {
    color: #E50914;
    border-bottom: 3px solid #E50914;
    padding-bottom: 10px;
    margin-bottom: 30px;
}

h2 {
    color: #333;
    margin-top: 30px;
    margin-bottom: 15px;
    border-left: 4px solid #E50914;
    padding-left: 15px;
}

h3 {
    color: #555;
    margin-top: 20px;
    margin-bottom: 10px;
}

.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: all 0.3s;
}

.btn-primary { background: #E50914; color: white; }
.btn-info { background: #17a2b8; color: white; }
.btn-warning { background: #ffc107; color: black; }

.btn:hover {
    opacity: 0.8;
    text-decoration: none;
}

hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 20px 0;
}

details {
    margin: 10px 0;
}

summary {
    cursor: pointer;
    font-weight: bold;
    color: #E50914;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 0.9em;
    border: 1px solid #ddd;
    max-height: 300px;
    overflow-y: auto;
}

code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

p {
    margin: 10px 0;
}

ol, ul {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}

a {
    color: #E50914;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
