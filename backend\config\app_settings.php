<?php
/**
 * إعدادات التطبيق المتقدمة - Shahid Platform Advanced Settings
 * ملف شامل لجميع إعدادات النظام القابلة للتخصيص
 */

// منع الوصول المباشر
if (!defined('SHAHID_PLATFORM')) {
    die('Access Denied');
}

// تحميل الثوابت والدوال المساعدة
require_once __DIR__ . '/constants.php';
require_once __DIR__ . '/helpers.php';

/**
 * فئة إعدادات التطبيق الشاملة
 */
class AppSettings {
    
    private static $settings = [];
    private static $loaded = false;
    
    /**
     * تحميل الإعدادات من قاعدة البيانات
     */
    public static function load() {
        if (self::$loaded) {
            return;
        }
        
        try {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ]
            );
            
            $stmt = $pdo->query("SELECT setting_key, setting_value, setting_type FROM system_settings");
            $dbSettings = $stmt->fetchAll();
            
            foreach ($dbSettings as $setting) {
                self::$settings[$setting['setting_key']] = self::castValue(
                    $setting['setting_value'], 
                    $setting['setting_type']
                );
            }
            
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات، استخدم الإعدادات الافتراضية
            self::loadDefaults();
        }
        
        self::$loaded = true;
    }
    
    /**
     * تحميل الإعدادات الافتراضية
     */
    private static function loadDefaults() {
        self::$settings = [
            // إعدادات عامة
            'site_name' => APP_NAME,
            'site_description' => APP_DESCRIPTION,
            'site_url' => APP_URL,
            'site_email' => APP_EMAIL,
            'site_logo' => '/assets/images/logo.png',
            'site_favicon' => '/assets/images/favicon.ico',
            
            // إعدادات اللغة والمنطقة
            'default_language' => 'ar',
            'supported_languages' => ['ar', 'en', 'fr'],
            'default_timezone' => DEFAULT_TIMEZONE,
            'date_format' => DISPLAY_DATE_FORMAT,
            'time_format' => DISPLAY_TIME_FORMAT,
            'currency' => DEFAULT_CURRENCY,
            'currency_symbol' => CURRENCY_SYMBOL,
            
            // إعدادات المحتوى
            'movies_per_page' => MOVIES_PER_PAGE,
            'series_per_page' => SERIES_PER_PAGE,
            'episodes_per_page' => EPISODES_PER_PAGE,
            'search_min_length' => SEARCH_MIN_LENGTH,
            'search_max_results' => SEARCH_MAX_RESULTS,
            'content_cache_time' => 3600,
            'enable_content_rating' => true,
            'enable_content_comments' => true,
            'enable_content_sharing' => true,
            
            // إعدادات المستخدمين
            'allow_registration' => true,
            'require_email_verification' => true,
            'password_min_length' => PASSWORD_MIN_LENGTH,
            'max_login_attempts' => MAX_LOGIN_ATTEMPTS,
            'login_lockout_time' => LOGIN_LOCKOUT_TIME,
            'session_lifetime' => SESSION_LIFETIME,
            'remember_me_time' => REMEMBER_ME_TIME,
            
            // إعدادات رفع الملفات
            'max_file_size' => MAX_FILE_SIZE,
            'allowed_image_types' => ALLOWED_IMAGE_TYPES,
            'allowed_video_types' => ALLOWED_VIDEO_TYPES,
            'allowed_document_types' => ALLOWED_DOCUMENT_TYPES,
            'image_max_width' => IMAGE_MAX_WIDTH,
            'image_max_height' => IMAGE_MAX_HEIGHT,
            'image_quality' => IMAGE_QUALITY,
            'enable_image_watermark' => false,
            'watermark_text' => 'Shahid Platform',
            
            // إعدادات البريد الإلكتروني
            'mail_enabled' => true,
            'mail_host' => MAIL_HOST,
            'mail_port' => MAIL_PORT,
            'mail_username' => MAIL_USERNAME,
            'mail_password' => MAIL_PASSWORD,
            'mail_from_email' => MAIL_FROM_EMAIL,
            'mail_from_name' => MAIL_FROM_NAME,
            'mail_encryption' => MAIL_ENCRYPTION,
            
            // إعدادات API
            'api_enabled' => true,
            'api_rate_limit' => API_RATE_LIMIT,
            'api_rate_window' => API_RATE_WINDOW,
            'api_key_required' => false,
            'api_cors_enabled' => true,
            'api_cors_origins' => ['*'],
            'api_documentation_enabled' => true,
            
            // إعدادات الأمان
            'security_enabled' => true,
            'csrf_protection' => true,
            'xss_protection' => true,
            'sql_injection_protection' => true,
            'rate_limiting_enabled' => true,
            'ip_whitelist_enabled' => false,
            'ip_whitelist' => ['127.0.0.1', '::1'],
            'ip_blacklist_enabled' => false,
            'ip_blacklist' => [],
            'two_factor_auth_enabled' => false,
            'password_complexity_required' => true,
            
            // إعدادات التخزين المؤقت
            'cache_enabled' => CACHE_ENABLED,
            'cache_lifetime' => CACHE_LIFETIME,
            'cache_driver' => 'file', // file, redis, memcached
            'cache_prefix' => 'shahid_',
            
            // إعدادات قاعدة البيانات
            'db_query_cache' => true,
            'db_slow_query_log' => true,
            'db_slow_query_threshold' => 0.1,
            'db_connection_pool' => false,
            'db_backup_enabled' => true,
            'db_backup_frequency' => 'daily',
            'db_backup_retention' => 30,
            
            // إعدادات الأداء
            'performance_monitoring' => PERFORMANCE_MONITORING,
            'slow_query_threshold' => SLOW_QUERY_THRESHOLD,
            'memory_limit_warning' => MEMORY_LIMIT_WARNING,
            'disk_space_warning' => DISK_SPACE_WARNING,
            'enable_gzip_compression' => true,
            'enable_browser_caching' => true,
            'cdn_enabled' => false,
            'cdn_url' => '',
            
            // إعدادات التحليلات
            'analytics_enabled' => ANALYTICS_ENABLED,
            'google_analytics_id' => GOOGLE_ANALYTICS_ID,
            'analytics_data_retention' => ANALYTICS_DATA_RETENTION,
            'track_user_behavior' => true,
            'track_content_views' => true,
            'track_search_queries' => true,
            
            // إعدادات الإشعارات
            'notifications_enabled' => true,
            'email_notifications' => true,
            'push_notifications' => false,
            'sms_notifications' => false,
            'notification_queue_enabled' => true,
            'notification_retry_attempts' => 3,
            
            // إعدادات SEO
            'seo_enabled' => true,
            'seo_title_max_length' => SEO_TITLE_MAX_LENGTH,
            'seo_description_max_length' => SEO_DESCRIPTION_MAX_LENGTH,
            'seo_keywords_max_count' => SEO_KEYWORDS_MAX_COUNT,
            'sitemap_enabled' => true,
            'sitemap_max_urls' => SITEMAP_MAX_URLS,
            'robots_txt_enabled' => true,
            
            // إعدادات النسخ الاحتياطي
            'backup_enabled' => true,
            'backup_frequency' => 'daily',
            'backup_max_count' => BACKUP_MAX_COUNT,
            'backup_compression' => BACKUP_COMPRESSION,
            'backup_include_files' => BACKUP_INCLUDE_FILES,
            'backup_include_database' => BACKUP_INCLUDE_DATABASE,
            'backup_encryption' => false,
            'backup_remote_storage' => false,
            
            // إعدادات التطوير
            'debug_mode' => DEBUG_MODE,
            'error_reporting_level' => ERROR_REPORTING_LEVEL,
            'display_errors' => DISPLAY_ERRORS,
            'log_errors' => LOG_ERRORS,
            'log_queries' => LOG_QUERIES,
            'profiling_enabled' => false,
            
            // إعدادات الإنتاج
            'production_mode' => PRODUCTION_MODE,
            'minify_css' => MINIFY_CSS,
            'minify_js' => MINIFY_JS,
            'gzip_compression' => GZIP_COMPRESSION,
            'error_pages_custom' => true,
            
            // إعدادات الصيانة
            'maintenance_mode' => MAINTENANCE_MODE,
            'maintenance_message' => MAINTENANCE_MESSAGE,
            'maintenance_allowed_ips' => MAINTENANCE_ALLOWED_IPS,
            'maintenance_start_time' => null,
            'maintenance_end_time' => null,
            
            // إعدادات الاشتراكات
            'subscriptions_enabled' => true,
            'free_trial_enabled' => true,
            'free_trial_duration' => 7,
            'payment_gateways' => ['stripe', 'paypal'],
            'subscription_plans' => SUBSCRIPTION_PLANS,
            
            // إعدادات المحتوى المتقدمة
            'content_moderation' => true,
            'auto_content_approval' => false,
            'content_encryption' => false,
            'drm_protection' => false,
            'watermark_videos' => false,
            'subtitle_support' => true,
            'multi_audio_support' => true,
            
            // إعدادات التخصيص
            'theme_customization' => true,
            'custom_css_enabled' => false,
            'custom_js_enabled' => false,
            'logo_customization' => true,
            'color_scheme_customization' => true,
            
            // إعدادات التكامل
            'social_login_enabled' => false,
            'facebook_login' => false,
            'google_login' => false,
            'twitter_login' => false,
            'third_party_apis' => [],
            'webhooks_enabled' => false,
            
            // إعدادات متنوعة
            'contact_email' => '<EMAIL>',
            'support_email' => '<EMAIL>',
            'privacy_policy_url' => '/privacy-policy',
            'terms_of_service_url' => '/terms-of-service',
            'help_center_url' => '/help',
            'status_page_url' => '/status',
        ];
    }
    
    /**
     * الحصول على قيمة إعداد
     */
    public static function get($key, $default = null) {
        self::load();
        return isset(self::$settings[$key]) ? self::$settings[$key] : $default;
    }
    
    /**
     * تعيين قيمة إعداد
     */
    public static function set($key, $value, $type = 'string') {
        self::load();
        self::$settings[$key] = $value;
        
        // حفظ في قاعدة البيانات
        try {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
                DB_USER,
                DB_PASS
            );
            
            $stmt = $pdo->prepare("
                INSERT INTO system_settings (setting_key, setting_value, setting_type) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                setting_type = VALUES(setting_type),
                updated_at = NOW()
            ");
            
            $stmt->execute([$key, self::valueToString($value), $type]);
            
        } catch (Exception $e) {
            log_message("Failed to save setting $key: " . $e->getMessage(), 'ERROR');
        }
    }
    
    /**
     * الحصول على جميع الإعدادات
     */
    public static function getAll() {
        self::load();
        return self::$settings;
    }
    
    /**
     * الحصول على إعدادات مجموعة معينة
     */
    public static function getGroup($prefix) {
        self::load();
        $group = [];
        
        foreach (self::$settings as $key => $value) {
            if (strpos($key, $prefix . '_') === 0) {
                $group[substr($key, strlen($prefix) + 1)] = $value;
            }
        }
        
        return $group;
    }
    
    /**
     * تحديث إعدادات متعددة
     */
    public static function updateMultiple($settings) {
        foreach ($settings as $key => $value) {
            self::set($key, $value);
        }
    }
    
    /**
     * حذف إعداد
     */
    public static function delete($key) {
        self::load();
        unset(self::$settings[$key]);
        
        try {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
                DB_USER,
                DB_PASS
            );
            
            $stmt = $pdo->prepare("DELETE FROM system_settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            
        } catch (Exception $e) {
            log_message("Failed to delete setting $key: " . $e->getMessage(), 'ERROR');
        }
    }
    
    /**
     * إعادة تحميل الإعدادات
     */
    public static function reload() {
        self::$loaded = false;
        self::$settings = [];
        self::load();
    }
    
    /**
     * تحويل القيمة إلى النوع المناسب
     */
    private static function castValue($value, $type) {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'array':
                return json_decode($value, true) ?: [];
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }
    
    /**
     * تحويل القيمة إلى نص للحفظ
     */
    private static function valueToString($value) {
        if (is_array($value) || is_object($value)) {
            return json_encode($value);
        }
        
        if (is_bool($value)) {
            return $value ? '1' : '0';
        }
        
        return (string) $value;
    }
    
    /**
     * التحقق من صحة الإعداد
     */
    public static function validate($key, $value) {
        $validations = [
            'site_email' => 'email',
            'max_file_size' => 'positive_integer',
            'session_lifetime' => 'positive_integer',
            'password_min_length' => 'positive_integer',
            'api_rate_limit' => 'positive_integer',
        ];
        
        if (!isset($validations[$key])) {
            return true;
        }
        
        switch ($validations[$key]) {
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            case 'positive_integer':
                return is_numeric($value) && $value > 0;
            default:
                return true;
        }
    }
    
    /**
     * تصدير الإعدادات
     */
    public static function export($format = 'json') {
        self::load();
        
        switch ($format) {
            case 'json':
                return json_encode(self::$settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            case 'php':
                return "<?php\nreturn " . var_export(self::$settings, true) . ";\n";
            default:
                return self::$settings;
        }
    }
    
    /**
     * استيراد الإعدادات
     */
    public static function import($data, $format = 'json') {
        switch ($format) {
            case 'json':
                $settings = json_decode($data, true);
                break;
            case 'php':
                $settings = eval('return ' . $data . ';');
                break;
            case 'array':
                $settings = $data;
                break;
            default:
                return false;
        }
        
        if (!is_array($settings)) {
            return false;
        }
        
        foreach ($settings as $key => $value) {
            if (self::validate($key, $value)) {
                self::set($key, $value);
            }
        }
        
        return true;
    }
}

// تحميل الإعدادات تلقائياً
AppSettings::load();

// تعريف ثابت للتحقق من التحميل الصحيح
define('APP_SETTINGS_LOADED', true);
?>
