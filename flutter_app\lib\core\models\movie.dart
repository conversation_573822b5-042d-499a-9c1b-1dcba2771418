class Movie {
  final int id;
  final String title;
  final String? originalTitle;
  final String slug;
  final String? description;
  final String? poster;
  final String? backdrop;
  final String? trailer;
  final int? year;
  final int? duration; // in minutes
  final double? rating;
  final int? voteCount;
  final String? country;
  final String? language;
  final List<String> genres;
  final List<String> cast;
  final List<String> directors;
  final String? contentRating;
  final bool premium;
  final bool featured;
  final bool downloadEnabled;
  final String status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int viewCount;
  final bool? isFavorite;
  final bool? isInWatchlist;
  final WatchProgress? watchProgress;
  final List<VideoSource>? videoSources;
  final List<Subtitle>? subtitles;

  Movie({
    required this.id,
    required this.title,
    this.originalTitle,
    required this.slug,
    this.description,
    this.poster,
    this.backdrop,
    this.trailer,
    this.year,
    this.duration,
    this.rating,
    this.voteCount,
    this.country,
    this.language,
    this.genres = const [],
    this.cast = const [],
    this.directors = const [],
    this.contentRating,
    this.premium = false,
    this.featured = false,
    this.downloadEnabled = false,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.viewCount = 0,
    this.isFavorite,
    this.isInWatchlist,
    this.watchProgress,
    this.videoSources,
    this.subtitles,
  });

  factory Movie.fromJson(Map<String, dynamic> json) {
    return Movie(
      id: json['id'],
      title: json['title'],
      originalTitle: json['original_title'],
      slug: json['slug'],
      description: json['description'],
      poster: json['poster'],
      backdrop: json['backdrop'],
      trailer: json['trailer'],
      year: json['year'],
      duration: json['duration'],
      rating: json['rating']?.toDouble(),
      voteCount: json['vote_count'],
      country: json['country'],
      language: json['language'],
      genres: json['genres'] != null 
          ? List<String>.from(json['genres'])
          : [],
      cast: json['cast'] != null 
          ? List<String>.from(json['cast'])
          : [],
      directors: json['directors'] != null 
          ? List<String>.from(json['directors'])
          : [],
      contentRating: json['content_rating'],
      premium: json['premium'] ?? false,
      featured: json['featured'] ?? false,
      downloadEnabled: json['download_enabled'] ?? false,
      status: json['status'] ?? 'published',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'])
          : null,
      viewCount: json['view_count'] ?? 0,
      isFavorite: json['is_favorite'],
      isInWatchlist: json['is_in_watchlist'],
      watchProgress: json['watch_progress'] != null
          ? WatchProgress.fromJson(json['watch_progress'])
          : null,
      videoSources: json['video_sources'] != null
          ? (json['video_sources'] as List)
              .map((e) => VideoSource.fromJson(e))
              .toList()
          : null,
      subtitles: json['subtitles'] != null
          ? (json['subtitles'] as List)
              .map((e) => Subtitle.fromJson(e))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'original_title': originalTitle,
      'slug': slug,
      'description': description,
      'poster': poster,
      'backdrop': backdrop,
      'trailer': trailer,
      'year': year,
      'duration': duration,
      'rating': rating,
      'vote_count': voteCount,
      'country': country,
      'language': language,
      'genres': genres,
      'cast': cast,
      'directors': directors,
      'content_rating': contentRating,
      'premium': premium,
      'featured': featured,
      'download_enabled': downloadEnabled,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'view_count': viewCount,
      'is_favorite': isFavorite,
      'is_in_watchlist': isInWatchlist,
      'watch_progress': watchProgress?.toJson(),
      'video_sources': videoSources?.map((e) => e.toJson()).toList(),
      'subtitles': subtitles?.map((e) => e.toJson()).toList(),
    };
  }

  Movie copyWith({
    int? id,
    String? title,
    String? originalTitle,
    String? slug,
    String? description,
    String? poster,
    String? backdrop,
    String? trailer,
    int? year,
    int? duration,
    double? rating,
    int? voteCount,
    String? country,
    String? language,
    List<String>? genres,
    List<String>? cast,
    List<String>? directors,
    String? contentRating,
    bool? premium,
    bool? featured,
    bool? downloadEnabled,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? viewCount,
    bool? isFavorite,
    bool? isInWatchlist,
    WatchProgress? watchProgress,
    List<VideoSource>? videoSources,
    List<Subtitle>? subtitles,
  }) {
    return Movie(
      id: id ?? this.id,
      title: title ?? this.title,
      originalTitle: originalTitle ?? this.originalTitle,
      slug: slug ?? this.slug,
      description: description ?? this.description,
      poster: poster ?? this.poster,
      backdrop: backdrop ?? this.backdrop,
      trailer: trailer ?? this.trailer,
      year: year ?? this.year,
      duration: duration ?? this.duration,
      rating: rating ?? this.rating,
      voteCount: voteCount ?? this.voteCount,
      country: country ?? this.country,
      language: language ?? this.language,
      genres: genres ?? this.genres,
      cast: cast ?? this.cast,
      directors: directors ?? this.directors,
      contentRating: contentRating ?? this.contentRating,
      premium: premium ?? this.premium,
      featured: featured ?? this.featured,
      downloadEnabled: downloadEnabled ?? this.downloadEnabled,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      viewCount: viewCount ?? this.viewCount,
      isFavorite: isFavorite ?? this.isFavorite,
      isInWatchlist: isInWatchlist ?? this.isInWatchlist,
      watchProgress: watchProgress ?? this.watchProgress,
      videoSources: videoSources ?? this.videoSources,
      subtitles: subtitles ?? this.subtitles,
    );
  }

  // Getters
  String get posterUrl => poster ?? '';
  String get backdropUrl => backdrop ?? '';
  String get trailerUrl => trailer ?? '';
  
  String get genresString => genres.join(', ');
  String get castString => cast.take(3).join(', ');
  String get directorsString => directors.join(', ');
  
  String get durationString {
    if (duration == null) return '';
    final hours = duration! ~/ 60;
    final minutes = duration! % 60;
    if (hours > 0) {
      return '${hours}س ${minutes}د';
    } else {
      return '${minutes}د';
    }
  }
  
  String get ratingString => rating != null ? rating!.toStringAsFixed(1) : '';
  
  bool get hasWatchProgress => watchProgress != null && watchProgress!.progress > 0;
  
  double get watchProgressPercentage {
    if (watchProgress == null || duration == null) return 0.0;
    return (watchProgress!.progress / (duration! * 60)) * 100;
  }
  
  bool get isCompleted => watchProgressPercentage >= 90;
}

class WatchProgress {
  final int progress; // in seconds
  final int duration; // in seconds
  final DateTime lastWatched;

  WatchProgress({
    required this.progress,
    required this.duration,
    required this.lastWatched,
  });

  factory WatchProgress.fromJson(Map<String, dynamic> json) {
    return WatchProgress(
      progress: json['progress'],
      duration: json['duration'],
      lastWatched: DateTime.parse(json['last_watched']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'progress': progress,
      'duration': duration,
      'last_watched': lastWatched.toIso8601String(),
    };
  }

  double get percentage => duration > 0 ? (progress / duration) * 100 : 0.0;
  bool get isCompleted => percentage >= 90;
}

class VideoSource {
  final String quality;
  final String url;
  final String type;
  final int? size; // in bytes

  VideoSource({
    required this.quality,
    required this.url,
    required this.type,
    this.size,
  });

  factory VideoSource.fromJson(Map<String, dynamic> json) {
    return VideoSource(
      quality: json['quality'],
      url: json['url'],
      type: json['type'],
      size: json['size'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'quality': quality,
      'url': url,
      'type': type,
      'size': size,
    };
  }
}

class Subtitle {
  final String language;
  final String label;
  final String url;
  final String format;

  Subtitle({
    required this.language,
    required this.label,
    required this.url,
    required this.format,
  });

  factory Subtitle.fromJson(Map<String, dynamic> json) {
    return Subtitle(
      language: json['language'],
      label: json['label'],
      url: json['url'],
      format: json['format'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'language': language,
      'label': label,
      'url': url,
      'format': format,
    };
  }
}
