<?php
/**
 * دوال مساعدة شاملة - Shahid Platform Helper Functions
 * مجموعة من الدوال المساعدة المستخدمة في جميع أنحاء النظام
 */

// منع الوصول المباشر
if (!defined('SHAHID_PLATFORM')) {
    die('Access Denied');
}

/**
 * ===== دوال الأمان والحماية =====
 */

/**
 * تنظيف وتأمين النص المدخل
 */
function sanitize_input($input) {
    if (is_array($input)) {
        return array_map('sanitize_input', $input);
    }
    
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return $input;
}

/**
 * تشفير كلمة المرور
 */
function hash_password($password) {
    return password_hash($password . SECURITY_SALT, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 */
function verify_password($password, $hash) {
    return password_verify($password . SECURITY_SALT, $hash);
}

/**
 * إنشاء رمز CSRF
 */
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * التحقق من رمز CSRF
 */
function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * إنشاء رمز عشوائي آمن
 */
function generate_secure_token($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * ===== دوال التحقق من صحة البيانات =====
 */

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من قوة كلمة المرور
 */
function validate_password($password) {
    $errors = [];
    
    if (strlen($password) < PASSWORD_MIN_LENGTH) {
        $errors[] = "كلمة المرور يجب أن تكون " . PASSWORD_MIN_LENGTH . " أحرف على الأقل";
    }
    
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل";
    }
    
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل";
    }
    
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل";
    }
    
    return empty($errors) ? true : $errors;
}

/**
 * التحقق من صحة رقم الهاتف
 */
function validate_phone($phone) {
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    return preg_match('/^(\+966|966|0)?[5][0-9]{8}$/', $phone);
}

/**
 * ===== دوال التاريخ والوقت =====
 */

/**
 * تنسيق التاريخ للعرض
 */
function format_date($date, $format = null) {
    if (!$format) {
        $format = DISPLAY_DATE_FORMAT;
    }
    
    if (is_string($date)) {
        $date = new DateTime($date);
    }
    
    return $date->format($format);
}

/**
 * تنسيق الوقت للعرض
 */
function format_time($time, $format = null) {
    if (!$format) {
        $format = DISPLAY_TIME_FORMAT;
    }
    
    if (is_string($time)) {
        $time = new DateTime($time);
    }
    
    return $time->format($format);
}

/**
 * حساب الوقت المنقضي منذ تاريخ معين
 */
function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' دقيقة';
    if ($time < 86400) return floor($time/3600) . ' ساعة';
    if ($time < 2592000) return floor($time/86400) . ' يوم';
    if ($time < 31536000) return floor($time/2592000) . ' شهر';
    
    return floor($time/31536000) . ' سنة';
}

/**
 * ===== دوال الملفات والمجلدات =====
 */

/**
 * إنشاء مجلد إذا لم يكن موجوداً
 */
function create_directory($path, $permissions = 0755) {
    if (!is_dir($path)) {
        return mkdir($path, $permissions, true);
    }
    return true;
}

/**
 * حذف مجلد وجميع محتوياته
 */
function delete_directory($path) {
    if (!is_dir($path)) {
        return false;
    }
    
    $files = array_diff(scandir($path), ['.', '..']);
    
    foreach ($files as $file) {
        $filePath = $path . DIRECTORY_SEPARATOR . $file;
        is_dir($filePath) ? delete_directory($filePath) : unlink($filePath);
    }
    
    return rmdir($path);
}

/**
 * تنسيق حجم الملف
 */
function format_file_size($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * التحقق من نوع الملف
 */
function validate_file_type($filename, $allowed_types) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    $allowed = explode(',', strtolower($allowed_types));
    return in_array($extension, $allowed);
}

/**
 * ===== دوال النصوص والترجمة =====
 */

/**
 * اختصار النص مع إضافة نقاط
 */
function truncate_text($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length) . $suffix;
}

/**
 * تنظيف النص من HTML
 */
function clean_text($text) {
    $text = strip_tags($text);
    $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
    return trim($text);
}

/**
 * تحويل النص إلى URL صديق
 */
function slugify($text) {
    $text = transliterate_arabic($text);
    $text = preg_replace('/[^a-zA-Z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return strtolower(trim($text, '-'));
}

/**
 * تحويل الأحرف العربية إلى لاتينية
 */
function transliterate_arabic($text) {
    $arabic = ['ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'];
    $latin = ['a', 'b', 't', 'th', 'j', 'h', 'kh', 'd', 'th', 'r', 'z', 's', 'sh', 's', 'd', 't', 'th', 'a', 'gh', 'f', 'q', 'k', 'l', 'm', 'n', 'h', 'w', 'y'];
    
    return str_replace($arabic, $latin, $text);
}

/**
 * ===== دوال الشبكة والURL =====
 */

/**
 * الحصول على عنوان IP الحقيقي
 */
function get_real_ip() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * إعادة توجيه إلى صفحة أخرى
 */
function redirect($url, $permanent = false) {
    $status_code = $permanent ? 301 : 302;
    
    if (!headers_sent()) {
        header("Location: $url", true, $status_code);
        exit;
    }
    
    echo "<script>window.location.href='$url';</script>";
    exit;
}

/**
 * التحقق من طلب AJAX
 */
function is_ajax_request() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * ===== دوال JSON والAPI =====
 */

/**
 * إرسال استجابة JSON
 */
function json_response($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

/**
 * إرسال استجابة نجاح
 */
function success_response($message, $data = null) {
    $response = [
        'success' => true,
        'message' => $message,
        'timestamp' => date('c')
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    json_response($response);
}

/**
 * إرسال استجابة خطأ
 */
function error_response($message, $code = 400, $details = null) {
    $response = [
        'success' => false,
        'error' => $message,
        'code' => $code,
        'timestamp' => date('c')
    ];
    
    if ($details !== null) {
        $response['details'] = $details;
    }
    
    json_response($response, $code);
}

/**
 * ===== دوال قاعدة البيانات =====
 */

/**
 * تنظيف البيانات لقاعدة البيانات
 */
function db_escape($value, $pdo) {
    if (is_null($value)) {
        return null;
    }
    
    if (is_bool($value)) {
        return $value ? 1 : 0;
    }
    
    if (is_numeric($value)) {
        return $value;
    }
    
    return $pdo->quote($value);
}

/**
 * بناء شرط WHERE من مصفوفة
 */
function build_where_clause($conditions, $operator = 'AND') {
    if (empty($conditions)) {
        return '';
    }
    
    $clauses = [];
    foreach ($conditions as $field => $value) {
        if (is_array($value)) {
            $placeholders = str_repeat('?,', count($value) - 1) . '?';
            $clauses[] = "$field IN ($placeholders)";
        } else {
            $clauses[] = "$field = ?";
        }
    }
    
    return 'WHERE ' . implode(" $operator ", $clauses);
}

/**
 * ===== دوال متنوعة =====
 */

/**
 * إنشاء معرف فريد
 */
function generate_uuid() {
    return sprintf(
        '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

/**
 * تسجيل رسالة في ملف السجل
 */
function log_message($message, $level = 'INFO', $file = 'system.log') {
    $log_path = LOGS_PATH . '/' . $file;
    $timestamp = date('Y-m-d H:i:s');
    $ip = get_real_ip();
    $user_id = $_SESSION['user_id'] ?? 'guest';
    
    $log_entry = "[$timestamp] [$level] [IP: $ip] [User: $user_id] $message" . PHP_EOL;
    
    create_directory(dirname($log_path));
    file_put_contents($log_path, $log_entry, FILE_APPEND | LOCK_EX);
}

/**
 * تحويل المصفوفة إلى XML
 */
function array_to_xml($array, $root_element = 'root', $xml = null) {
    if ($xml === null) {
        $xml = new SimpleXMLElement("<$root_element/>");
    }
    
    foreach ($array as $key => $value) {
        if (is_array($value)) {
            array_to_xml($value, $key, $xml->addChild($key));
        } else {
            $xml->addChild($key, htmlspecialchars($value));
        }
    }
    
    return $xml;
}

/**
 * ضغط وتحسين CSS
 */
function minify_css($css) {
    $css = preg_replace('/\/\*.*?\*\//s', '', $css);
    $css = preg_replace('/\s+/', ' ', $css);
    $css = str_replace(['; ', ' {', '{ ', ' }', '} ', ': '], [';', '{', '{', '}', '}', ':'], $css);
    return trim($css);
}

/**
 * ضغط وتحسين JavaScript
 */
function minify_js($js) {
    $js = preg_replace('/\/\*.*?\*\//s', '', $js);
    $js = preg_replace('/\/\/.*$/m', '', $js);
    $js = preg_replace('/\s+/', ' ', $js);
    return trim($js);
}

// تعريف ثابت للتحقق من التحميل الصحيح
define('HELPERS_LOADED', true);
?>
