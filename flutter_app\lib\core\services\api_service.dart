import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

import '../app_config.dart';
import 'storage_service.dart';
import '../models/api_response.dart';
import '../exceptions/api_exception.dart';

class ApiService {
  static late Dio _dio;
  static final StorageService _storage = StorageService();
  
  static Future<void> init() async {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.baseUrl,
      connectTimeout: AppConfig.connectionTimeout,
      receiveTimeout: AppConfig.receiveTimeout,
      sendTimeout: AppConfig.sendTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    
    // Add interceptors
    _dio.interceptors.add(_AuthInterceptor());
    _dio.interceptors.add(_LoggingInterceptor());
    _dio.interceptors.add(_ErrorInterceptor());
  }
  
  // GET Request
  static Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      await _checkConnectivity();
      
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
        options: options,
      );
      
      return _handleResponse<T>(response);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  // POST Request
  static Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      await _checkConnectivity();
      
      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      
      return _handleResponse<T>(response);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  // PUT Request
  static Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      await _checkConnectivity();
      
      final response = await _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      
      return _handleResponse<T>(response);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  // DELETE Request
  static Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      await _checkConnectivity();
      
      final response = await _dio.delete(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      
      return _handleResponse<T>(response);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  // Upload File
  static Future<ApiResponse<T>> uploadFile<T>(
    String endpoint,
    File file, {
    String fieldName = 'file',
    Map<String, dynamic>? data,
    ProgressCallback? onSendProgress,
  }) async {
    try {
      await _checkConnectivity();
      
      final formData = FormData();
      
      // Add file
      formData.files.add(MapEntry(
        fieldName,
        await MultipartFile.fromFile(file.path),
      ));
      
      // Add additional data
      if (data != null) {
        data.forEach((key, value) {
          formData.fields.add(MapEntry(key, value.toString()));
        });
      }
      
      final response = await _dio.post(
        endpoint,
        data: formData,
        onSendProgress: onSendProgress,
      );
      
      return _handleResponse<T>(response);
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  // Download File
  static Future<void> downloadFile(
    String url,
    String savePath, {
    ProgressCallback? onReceiveProgress,
    CancelToken? cancelToken,
  }) async {
    try {
      await _checkConnectivity();
      
      await _dio.download(
        url,
        savePath,
        onReceiveProgress: onReceiveProgress,
        cancelToken: cancelToken,
      );
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  // Check connectivity
  static Future<void> _checkConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      throw ApiException(
        message: AppConfig.networkErrorMessage,
        statusCode: 0,
        type: ApiExceptionType.network,
      );
    }
  }
  
  // Handle response
  static ApiResponse<T> _handleResponse<T>(Response response) {
    final data = response.data;
    
    if (data is Map<String, dynamic>) {
      return ApiResponse<T>.fromJson(data);
    } else {
      return ApiResponse<T>(
        success: true,
        data: data as T?,
        message: 'Success',
      );
    }
  }
  
  // Handle errors
  static ApiException _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return ApiException(
            message: AppConfig.networkErrorMessage,
            statusCode: 0,
            type: ApiExceptionType.timeout,
          );
          
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode ?? 0;
          final data = error.response?.data;
          
          String message = AppConfig.serverErrorMessage;
          if (data is Map<String, dynamic> && data.containsKey('error')) {
            message = data['error'].toString();
          }
          
          return ApiException(
            message: message,
            statusCode: statusCode,
            type: _getExceptionType(statusCode),
          );
          
        case DioExceptionType.cancel:
          return ApiException(
            message: 'Request cancelled',
            statusCode: 0,
            type: ApiExceptionType.cancel,
          );
          
        default:
          return ApiException(
            message: AppConfig.networkErrorMessage,
            statusCode: 0,
            type: ApiExceptionType.network,
          );
      }
    } else if (error is ApiException) {
      return error;
    } else {
      return ApiException(
        message: error.toString(),
        statusCode: 0,
        type: ApiExceptionType.unknown,
      );
    }
  }
  
  // Get exception type based on status code
  static ApiExceptionType _getExceptionType(int statusCode) {
    switch (statusCode) {
      case 401:
        return ApiExceptionType.unauthorized;
      case 403:
        return ApiExceptionType.forbidden;
      case 404:
        return ApiExceptionType.notFound;
      case 422:
        return ApiExceptionType.validation;
      case 429:
        return ApiExceptionType.tooManyRequests;
      case 500:
      case 502:
      case 503:
      case 504:
        return ApiExceptionType.server;
      default:
        return ApiExceptionType.unknown;
    }
  }
}

// Auth Interceptor
class _AuthInterceptor extends Interceptor {
  final StorageService _storage = StorageService();
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final token = _storage.getString(AppConfig.tokenKey);
    if (token != null && token.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      // Token expired, clear storage and redirect to login
      _storage.remove(AppConfig.tokenKey);
      _storage.remove(AppConfig.userKey);
    }
    handler.next(err);
  }
}

// Logging Interceptor
class _LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (kDebugMode && AppConfig.enableLogging) {
      debugPrint('🚀 REQUEST[${options.method}] => PATH: ${options.path}');
      debugPrint('Headers: ${options.headers}');
      debugPrint('Data: ${options.data}');
    }
    handler.next(options);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode && AppConfig.enableLogging) {
      debugPrint('✅ RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}');
      debugPrint('Data: ${response.data}');
    }
    handler.next(response);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode && AppConfig.enableLogging) {
      debugPrint('❌ ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.path}');
      debugPrint('Message: ${err.message}');
      debugPrint('Data: ${err.response?.data}');
    }
    handler.next(err);
  }
}

// Error Interceptor
class _ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Handle specific error cases
    if (err.response?.statusCode == 401) {
      // Handle unauthorized
    } else if (err.response?.statusCode == 403) {
      // Handle forbidden
    } else if (err.response?.statusCode == 404) {
      // Handle not found
    }
    
    handler.next(err);
  }
}
