<?php include 'views/layout/header.php'; ?>

<!-- Hero Section -->
<?php if (!empty($hero_content)): ?>
<section class="hero-section">
    <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel">
        <div class="carousel-inner">
            <?php foreach ($hero_content as $index => $content): ?>
            <div class="carousel-item <?= $index === 0 ? 'active' : '' ?>">
                <div class="hero-slide" style="background-image: url('<?= $content['backdrop'] ?? $content['poster'] ?>');">
                    <div class="hero-overlay"></div>
                    <div class="container">
                        <div class="row align-items-center min-vh-100">
                            <div class="col-lg-6">
                                <div class="hero-content">
                                    <h1 class="hero-title"><?= htmlspecialchars($content['title']) ?></h1>
                                    <p class="hero-description">
                                        <?= htmlspecialchars(substr($content['description'] ?? '', 0, 200)) ?>...
                                    </p>
                                    <div class="hero-meta">
                                        <?php if ($content['year']): ?>
                                            <span class="badge bg-secondary me-2"><?= $content['year'] ?></span>
                                        <?php endif; ?>
                                        <?php if ($content['rating']): ?>
                                            <span class="badge bg-warning me-2">
                                                <i class="fas fa-star"></i> <?= $content['rating'] ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if ($content['premium']): ?>
                                            <span class="badge bg-primary me-2">Premium</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="hero-actions mt-4">
                                        <a href="/watch/<?= $content['id'] ?>?type=<?= $content['type'] ?>" 
                                           class="btn btn-primary btn-lg me-3">
                                            <i class="fas fa-play me-2"></i>مشاهدة الآن
                                        </a>
                                        <a href="/<?= $content['type'] === 'movie' ? 'movies' : 'series' ?>/<?= $content['slug'] ?>" 
                                           class="btn btn-outline-light btn-lg">
                                            <i class="fas fa-info-circle me-2"></i>المزيد
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Carousel Controls -->
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon"></span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon"></span>
        </button>
        
        <!-- Indicators -->
        <div class="carousel-indicators">
            <?php foreach ($hero_content as $index => $content): ?>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="<?= $index ?>" 
                    class="<?= $index === 0 ? 'active' : '' ?>"></button>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Content Sections -->
<div class="container py-5">
    
    <!-- Latest Movies -->
    <?php if (!empty($latest_movies)): ?>
    <section class="content-section mb-5">
        <div class="section-header d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title">أحدث الأفلام</h2>
            <a href="/movies" class="btn btn-outline-primary">عرض الكل</a>
        </div>
        <div class="content-slider">
            <div class="row">
                <?php foreach (array_slice($latest_movies, 0, 6) as $movie): ?>
                <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
                    <div class="content-card">
                        <div class="card-image">
                            <img src="<?= $movie['poster'] ?? '/assets/images/no-poster.jpg' ?>" 
                                 alt="<?= htmlspecialchars($movie['title']) ?>" 
                                 class="img-fluid">
                            <div class="card-overlay">
                                <div class="card-actions">
                                    <a href="/watch/<?= $movie['id'] ?>?type=movie" class="btn btn-primary btn-sm">
                                        <i class="fas fa-play"></i>
                                    </a>
                                    <button class="btn btn-outline-light btn-sm favorite-btn" 
                                            data-id="<?= $movie['id'] ?>" data-type="movie">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                                <?php if ($movie['premium']): ?>
                                    <span class="premium-badge">Premium</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="card-info">
                            <h6 class="card-title">
                                <a href="/movies/<?= $movie['slug'] ?>"><?= htmlspecialchars($movie['title']) ?></a>
                            </h6>
                            <div class="card-meta">
                                <span class="year"><?= $movie['year'] ?></span>
                                <?php if ($movie['rating']): ?>
                                    <span class="rating">
                                        <i class="fas fa-star text-warning"></i> <?= $movie['rating'] ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>
    
    <!-- Latest Series -->
    <?php if (!empty($latest_series)): ?>
    <section class="content-section mb-5">
        <div class="section-header d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title">أحدث المسلسلات</h2>
            <a href="/series" class="btn btn-outline-primary">عرض الكل</a>
        </div>
        <div class="content-slider">
            <div class="row">
                <?php foreach (array_slice($latest_series, 0, 6) as $series): ?>
                <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
                    <div class="content-card">
                        <div class="card-image">
                            <img src="<?= $series['poster'] ?? '/assets/images/no-poster.jpg' ?>" 
                                 alt="<?= htmlspecialchars($series['title']) ?>" 
                                 class="img-fluid">
                            <div class="card-overlay">
                                <div class="card-actions">
                                    <a href="/series/<?= $series['slug'] ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-play"></i>
                                    </a>
                                    <button class="btn btn-outline-light btn-sm favorite-btn" 
                                            data-id="<?= $series['id'] ?>" data-type="series">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                                <?php if ($series['premium']): ?>
                                    <span class="premium-badge">Premium</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="card-info">
                            <h6 class="card-title">
                                <a href="/series/<?= $series['slug'] ?>"><?= htmlspecialchars($series['title']) ?></a>
                            </h6>
                            <div class="card-meta">
                                <span class="year"><?= $series['year'] ?></span>
                                <span class="episodes"><?= $series['total_episodes'] ?> حلقة</span>
                                <?php if ($series['rating']): ?>
                                    <span class="rating">
                                        <i class="fas fa-star text-warning"></i> <?= $series['rating'] ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>
    
    <!-- Popular Movies -->
    <?php if (!empty($popular_movies)): ?>
    <section class="content-section mb-5">
        <div class="section-header d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title">الأفلام الأكثر مشاهدة</h2>
            <a href="/movies?sort=popular" class="btn btn-outline-primary">عرض الكل</a>
        </div>
        <div class="content-slider">
            <div class="row">
                <?php foreach (array_slice($popular_movies, 0, 6) as $movie): ?>
                <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
                    <div class="content-card">
                        <div class="card-image">
                            <img src="<?= $movie['poster'] ?? '/assets/images/no-poster.jpg' ?>" 
                                 alt="<?= htmlspecialchars($movie['title']) ?>" 
                                 class="img-fluid">
                            <div class="card-overlay">
                                <div class="card-actions">
                                    <a href="/watch/<?= $movie['id'] ?>?type=movie" class="btn btn-primary btn-sm">
                                        <i class="fas fa-play"></i>
                                    </a>
                                    <button class="btn btn-outline-light btn-sm favorite-btn" 
                                            data-id="<?= $movie['id'] ?>" data-type="movie">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                                <?php if ($movie['premium']): ?>
                                    <span class="premium-badge">Premium</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="card-info">
                            <h6 class="card-title">
                                <a href="/movies/<?= $movie['slug'] ?>"><?= htmlspecialchars($movie['title']) ?></a>
                            </h6>
                            <div class="card-meta">
                                <span class="year"><?= $movie['year'] ?></span>
                                <span class="views"><?= number_format($movie['views']) ?> مشاهدة</span>
                                <?php if ($movie['rating']): ?>
                                    <span class="rating">
                                        <i class="fas fa-star text-warning"></i> <?= $movie['rating'] ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>
    
    <!-- Call to Action -->
    <?php if (!isset($user) || !$user): ?>
    <section class="cta-section bg-primary text-white rounded p-5 text-center">
        <h3>انضم إلى Shahid اليوم</h3>
        <p class="lead">استمتع بآلاف الأفلام والمسلسلات بجودة عالية</p>
        <a href="/register" class="btn btn-light btn-lg">ابدأ المشاهدة مجاناً</a>
    </section>
    <?php endif; ?>
    
</div>

<script>
// Favorite functionality
$('.favorite-btn').click(function() {
    const btn = $(this);
    const contentId = btn.data('id');
    const contentType = btn.data('type');
    const icon = btn.find('i');
    
    <?php if (isset($user) && $user): ?>
    $.post('/toggle-favorite', {
        [contentType + '_id']: contentId,
        action: icon.hasClass('fas') ? 'remove' : 'add',
        csrf_token: $('meta[name="csrf-token"]').attr('content')
    })
    .done(function(response) {
        if (response.success) {
            icon.toggleClass('far fas');
            // Show toast notification
            showToast(response.message, 'success');
        }
    })
    .fail(function() {
        showToast('حدث خطأ، حاول مرة أخرى', 'error');
    });
    <?php else: ?>
    window.location.href = '/login';
    <?php endif; ?>
});

function showToast(message, type) {
    // Simple toast implementation
    const toast = $(`
        <div class="toast-notification toast-${type}">
            ${message}
        </div>
    `);
    
    $('body').append(toast);
    toast.fadeIn().delay(3000).fadeOut(function() {
        $(this).remove();
    });
}
</script>

<?php include 'views/layout/footer.php'; ?>
