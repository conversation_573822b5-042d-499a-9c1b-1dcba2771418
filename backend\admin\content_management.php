<?php
/**
 * Shahid Platform - Content Management System
 * Complete Content Management for Movies and Series
 */

session_start();
require_once '../config/database.php';
require_once '../config/auth.php';

// Check admin authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Handle form submissions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_movie':
            $result = addMovie($pdo, $_POST, $_FILES);
            if ($result['success']) {
                $message = 'تم إضافة الفيلم بنجاح!';
            } else {
                $error = $result['error'];
            }
            break;
            
        case 'add_series':
            $result = addSeries($pdo, $_POST, $_FILES);
            if ($result['success']) {
                $message = 'تم إضافة المسلسل بنجاح!';
            } else {
                $error = $result['error'];
            }
            break;
            
        case 'delete_movie':
            $result = deleteMovie($pdo, $_POST['movie_id']);
            if ($result['success']) {
                $message = 'تم حذف الفيلم بنجاح!';
            } else {
                $error = $result['error'];
            }
            break;
            
        case 'delete_series':
            $result = deleteSeries($pdo, $_POST['series_id']);
            if ($result['success']) {
                $message = 'تم حذف المسلسل بنجاح!';
            } else {
                $error = $result['error'];
            }
            break;
    }
}

// Get content for display
$movies = getMovies($pdo);
$series = getSeries($pdo);
$categories = getCategories($pdo);

// Functions
function addMovie($pdo, $data, $files) {
    try {
        // Handle file uploads
        $poster = uploadFile($files['poster'], 'posters');
        $video = uploadFile($files['video'], 'videos');
        
        $stmt = $pdo->prepare("
            INSERT INTO movies (title, title_ar, description, description_ar, year, duration, rating, genre, poster, trailer, video_url, status, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())
        ");
        
        $stmt->execute([
            $data['title'],
            $data['title_ar'],
            $data['description'],
            $data['description_ar'],
            $data['year'],
            $data['duration'],
            $data['rating'],
            $data['genre'],
            $poster,
            $data['trailer'],
            $video
        ]);
        
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function addSeries($pdo, $data, $files) {
    try {
        // Handle file uploads
        $poster = uploadFile($files['poster'], 'posters');
        
        $stmt = $pdo->prepare("
            INSERT INTO series (title, title_ar, description, description_ar, year, seasons, episodes, rating, genre, poster, trailer, status, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())
        ");
        
        $stmt->execute([
            $data['title'],
            $data['title_ar'],
            $data['description'],
            $data['description_ar'],
            $data['year'],
            $data['seasons'],
            $data['episodes'],
            $data['rating'],
            $data['genre'],
            $poster,
            $data['trailer']
        ]);
        
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function deleteMovie($pdo, $movieId) {
    try {
        $stmt = $pdo->prepare("UPDATE movies SET status = 'deleted' WHERE id = ?");
        $stmt->execute([$movieId]);
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function deleteSeries($pdo, $seriesId) {
    try {
        $stmt = $pdo->prepare("UPDATE series SET status = 'deleted' WHERE id = ?");
        $stmt->execute([$seriesId]);
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function uploadFile($file, $folder) {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return null;
    }
    
    $uploadDir = "../uploads/$folder/";
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $fileName = time() . '_' . basename($file['name']);
    $uploadPath = $uploadDir . $fileName;
    
    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
        return "uploads/$folder/$fileName";
    }
    
    return null;
}

function getMovies($pdo) {
    $stmt = $pdo->query("SELECT * FROM movies WHERE status = 'active' ORDER BY created_at DESC LIMIT 20");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getSeries($pdo) {
    $stmt = $pdo->query("SELECT * FROM series WHERE status IN ('active', 'ongoing', 'completed') ORDER BY created_at DESC LIMIT 20");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getCategories($pdo) {
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المحتوى - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .nav-tabs {
            display: flex;
            background: rgba(47, 47, 47, 0.9);
            border-radius: 10px;
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .nav-tab {
            flex: 1;
            padding: 1rem;
            background: transparent;
            border: none;
            color: #ccc;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .nav-tab.active {
            background: #E50914;
            color: white;
        }
        
        .nav-tab:hover {
            background: rgba(229, 9, 20, 0.7);
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-section {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .form-section h2 {
            color: #E50914;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #ccc;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #555;
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.3);
            color: #fff;
            font-size: 1rem;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #E50914;
            box-shadow: 0 0 10px rgba(229, 9, 20, 0.3);
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .content-card {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
            transition: all 0.3s ease;
        }
        
        .content-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(229, 9, 20, 0.3);
        }
        
        .content-card h3 {
            color: #E50914;
            margin-bottom: 1rem;
        }
        
        .content-card p {
            color: #ccc;
            margin-bottom: 0.5rem;
        }
        
        .content-actions {
            margin-top: 1rem;
            display: flex;
            gap: 0.5rem;
        }
        
        .message {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
            color: #F44336;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .back-link {
            display: inline-block;
            background: linear-gradient(45deg, #555, #333);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }
        
        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 إدارة المحتوى</h1>
        <p>إضافة وإدارة الأفلام والمسلسلات</p>
    </div>

    <div class="container">
        <a href="dashboard.php" class="back-link">← العودة إلى لوحة التحكم</a>
        
        <?php if ($message): ?>
            <div class="message"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('add-movie')">إضافة فيلم</button>
            <button class="nav-tab" onclick="showTab('add-series')">إضافة مسلسل</button>
            <button class="nav-tab" onclick="showTab('manage-movies')">إدارة الأفلام</button>
            <button class="nav-tab" onclick="showTab('manage-series')">إدارة المسلسلات</button>
        </div>

        <!-- Add Movie Tab -->
        <div id="add-movie" class="tab-content active">
            <div class="form-section">
                <h2>إضافة فيلم جديد</h2>
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="add_movie">
                    <div class="form-grid">
                        <div class="form-group">
                            <label>العنوان (إنجليزي)</label>
                            <input type="text" name="title" required>
                        </div>
                        <div class="form-group">
                            <label>العنوان (عربي)</label>
                            <input type="text" name="title_ar" required>
                        </div>
                        <div class="form-group">
                            <label>سنة الإنتاج</label>
                            <input type="number" name="year" min="1900" max="2030" required>
                        </div>
                        <div class="form-group">
                            <label>المدة (بالدقائق)</label>
                            <input type="number" name="duration" min="1" required>
                        </div>
                        <div class="form-group">
                            <label>التقييم</label>
                            <input type="number" name="rating" min="0" max="10" step="0.1" required>
                        </div>
                        <div class="form-group">
                            <label>النوع</label>
                            <input type="text" name="genre" placeholder="Action,Drama,Thriller" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>الوصف (إنجليزي)</label>
                        <textarea name="description" rows="3" required></textarea>
                    </div>
                    <div class="form-group">
                        <label>الوصف (عربي)</label>
                        <textarea name="description_ar" rows="3" required></textarea>
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label>رابط الإعلان</label>
                            <input type="url" name="trailer">
                        </div>
                        <div class="form-group">
                            <label>صورة الفيلم</label>
                            <input type="file" name="poster" accept="image/*">
                        </div>
                        <div class="form-group">
                            <label>ملف الفيديو</label>
                            <input type="file" name="video" accept="video/*">
                        </div>
                    </div>
                    <button type="submit" class="btn">إضافة الفيلم</button>
                </form>
            </div>
        </div>

        <!-- Add Series Tab -->
        <div id="add-series" class="tab-content">
            <div class="form-section">
                <h2>إضافة مسلسل جديد</h2>
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="add_series">
                    <div class="form-grid">
                        <div class="form-group">
                            <label>العنوان (إنجليزي)</label>
                            <input type="text" name="title" required>
                        </div>
                        <div class="form-group">
                            <label>العنوان (عربي)</label>
                            <input type="text" name="title_ar" required>
                        </div>
                        <div class="form-group">
                            <label>سنة الإنتاج</label>
                            <input type="number" name="year" min="1900" max="2030" required>
                        </div>
                        <div class="form-group">
                            <label>عدد المواسم</label>
                            <input type="number" name="seasons" min="1" required>
                        </div>
                        <div class="form-group">
                            <label>عدد الحلقات</label>
                            <input type="number" name="episodes" min="1" required>
                        </div>
                        <div class="form-group">
                            <label>التقييم</label>
                            <input type="number" name="rating" min="0" max="10" step="0.1" required>
                        </div>
                        <div class="form-group">
                            <label>النوع</label>
                            <input type="text" name="genre" placeholder="Drama,Comedy,Action" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>الوصف (إنجليزي)</label>
                        <textarea name="description" rows="3" required></textarea>
                    </div>
                    <div class="form-group">
                        <label>الوصف (عربي)</label>
                        <textarea name="description_ar" rows="3" required></textarea>
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label>رابط الإعلان</label>
                            <input type="url" name="trailer">
                        </div>
                        <div class="form-group">
                            <label>صورة المسلسل</label>
                            <input type="file" name="poster" accept="image/*">
                        </div>
                    </div>
                    <button type="submit" class="btn">إضافة المسلسل</button>
                </form>
            </div>
        </div>

        <!-- Manage Movies Tab -->
        <div id="manage-movies" class="tab-content">
            <div class="form-section">
                <h2>إدارة الأفلام</h2>
                <div class="content-grid">
                    <?php foreach ($movies as $movie): ?>
                    <div class="content-card">
                        <h3><?php echo htmlspecialchars($movie['title']); ?></h3>
                        <p><strong>العنوان العربي:</strong> <?php echo htmlspecialchars($movie['title_ar']); ?></p>
                        <p><strong>السنة:</strong> <?php echo $movie['year']; ?></p>
                        <p><strong>المدة:</strong> <?php echo $movie['duration']; ?> دقيقة</p>
                        <p><strong>التقييم:</strong> <?php echo $movie['rating']; ?>/10</p>
                        <p><strong>النوع:</strong> <?php echo htmlspecialchars($movie['genre']); ?></p>
                        <div class="content-actions">
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="delete_movie">
                                <input type="hidden" name="movie_id" value="<?php echo $movie['id']; ?>">
                                <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الفيلم؟')">حذف</button>
                            </form>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Manage Series Tab -->
        <div id="manage-series" class="tab-content">
            <div class="form-section">
                <h2>إدارة المسلسلات</h2>
                <div class="content-grid">
                    <?php foreach ($series as $show): ?>
                    <div class="content-card">
                        <h3><?php echo htmlspecialchars($show['title']); ?></h3>
                        <p><strong>العنوان العربي:</strong> <?php echo htmlspecialchars($show['title_ar']); ?></p>
                        <p><strong>السنة:</strong> <?php echo $show['year']; ?></p>
                        <p><strong>المواسم:</strong> <?php echo $show['seasons']; ?></p>
                        <p><strong>الحلقات:</strong> <?php echo $show['episodes']; ?></p>
                        <p><strong>التقييم:</strong> <?php echo $show['rating']; ?>/10</p>
                        <p><strong>النوع:</strong> <?php echo htmlspecialchars($show['genre']); ?></p>
                        <p><strong>الحالة:</strong> <?php echo $show['status']; ?></p>
                        <div class="content-actions">
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="delete_series">
                                <input type="hidden" name="series_id" value="<?php echo $show['id']; ?>">
                                <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المسلسل؟')">حذف</button>
                            </form>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
        
        console.log('🎬 Content Management System loaded successfully!');
    </script>
</body>
</html>
