<?php
/**
 * Test Permissions - Shahid Platform
 * Quick test to check if all files are accessible
 */

echo "<h1>🔧 اختبار الصلاحيات - Shahid Platform</h1>";

$testFiles = [
    'index_simple.php' => 'الصفحة الرئيسية',
    'api/test_api.php' => 'اختبار API',
    'api/index_simple.php' => 'API البسيط',
    'admin/index.php' => 'لوحة الإدارة',
    'create_database.php' => 'إنشاء قاعدة البيانات',
    'add_sample_data.php' => 'إضافة بيانات تجريبية'
];

echo "<h2>📁 فحص الملفات:</h2>";

foreach ($testFiles as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    if (file_exists($fullPath)) {
        $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $file;
        echo "<p>✅ <strong>$description</strong>: <a href='$url' target='_blank'>$file</a></p>";
    } else {
        echo "<p>❌ <strong>$description</strong>: $file (غير موجود)</p>";
    }
}

echo "<h2>🔗 اختبار الوصول:</h2>";

// Test API access
$apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/test_api.php';
echo "<p><a href='$apiUrl' target='_blank' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧪 اختبار API</a></p>";

// Test Admin access
$adminUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/admin/';
echo "<p><a href='$adminUrl' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🎛️ لوحة الإدارة</a></p>";

// Test main page
$mainUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/index_simple.php';
echo "<p><a href='$mainUrl' target='_blank' style='background: #E50914; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 الصفحة الرئيسية</a></p>";

echo "<h2>⚙️ معلومات الخادم:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<strong>خادم الويب:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
echo "<strong>إصدار PHP:</strong> " . PHP_VERSION . "<br>";
echo "<strong>المجلد الحالي:</strong> " . __DIR__ . "<br>";
echo "<strong>URL الحالي:</strong> " . $_SERVER['REQUEST_URI'] . "<br>";
echo "<strong>المضيف:</strong> " . $_SERVER['HTTP_HOST'] . "<br>";
echo "</div>";

echo "<h2>🔧 إصلاح المشاكل:</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>إذا كانت هناك مشاكل في الوصول:</h3>";
echo "<ol>";
echo "<li>تأكد من تشغيل خادم Apache</li>";
echo "<li>تحقق من إعدادات .htaccess</li>";
echo "<li>تأكد من صلاحيات المجلدات</li>";
echo "<li>أعد تشغيل خادم Apache</li>";
echo "</ol>";
echo "</div>";

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f8f9fa; }
h1 { color: #E50914; border-bottom: 3px solid #E50914; padding-bottom: 10px; }
h2 { color: #333; margin-top: 30px; }
h3 { color: #555; }
p { margin: 10px 0; }
a { color: #E50914; text-decoration: none; }
a:hover { text-decoration: underline; }
ol { margin: 10px 0; padding-left: 20px; }
li { margin: 5px 0; }
</style>";
?>
