/* Shahid Movie Details Page Stylesheet */

/* Movie Hero Section */
.movie-hero {
    position: relative;
    min-height: 100vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    margin-top: 76px;
}

.movie-hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        rgba(20, 20, 20, 0.9) 0%,
        rgba(20, 20, 20, 0.6) 50%,
        rgba(20, 20, 20, 0.9) 100%
    );
}

.movie-poster-large {
    position: relative;
    max-width: 350px;
    margin: 0 auto;
}

.movie-poster-large img {
    width: 100%;
    border-radius: var(--border-radius);
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.5);
}

.premium-badge-large {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(229, 9, 20, 0.3);
}

.movie-details {
    position: relative;
    z-index: 2;
    padding-left: 2rem;
}

.movie-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    line-height: 1.2;
}

.movie-meta-large {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.movie-genres-large {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.genre-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 0.25rem 0.5rem rgba(229, 9, 20, 0.3);
}

.movie-description {
    font-size: 1.2rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    max-width: 600px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.movie-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2rem;
}

.movie-actions .btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 25px;
    transition: var(--transition);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
}

.movie-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.4);
}

.watch-progress {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
}

.progress-info {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.progress {
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    background: var(--gradient-primary);
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Movie Information Section */
.movie-info-section {
    background: var(--dark-color);
    padding: 4rem 0;
}

.info-card {
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.info-card h3 {
    color: var(--text-color);
    font-weight: 700;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.info-card strong {
    color: var(--primary-color);
    font-weight: 600;
}

.info-card span {
    color: var(--text-color);
}

.quality-badges .badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 15px;
}

.subtitle-list .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 12px;
}

.stats-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--text-muted);
    font-weight: 500;
}

.stat-value {
    color: var(--text-color);
    font-weight: 600;
}

/* Related Movies Section */
.related-section {
    background: var(--secondary-color);
    padding: 4rem 0;
}

.related-section .section-title {
    color: var(--text-color);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2rem;
    text-align: center;
}

/* Modals */
.modal-content {
    background: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    color: var(--text-color);
    font-weight: 600;
}

.modal-body {
    color: var(--text-color);
}

.modal-footer {
    border-top: 1px solid var(--border-color);
}

.form-label {
    color: var(--text-color);
    font-weight: 600;
}

.form-select,
.form-control {
    background-color: var(--dark-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.form-select:focus,
.form-control:focus {
    background-color: var(--dark-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    color: var(--text-color);
}

.form-select option {
    background-color: var(--dark-color);
    color: var(--text-color);
}

/* Dropdown Menus */
.dropdown-menu {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.dropdown-item {
    color: var(--text-color);
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: white;
}

.dropdown-divider {
    border-color: var(--border-color);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .movie-title {
        font-size: 3rem;
    }
    
    .movie-details {
        padding-left: 1.5rem;
    }
}

@media (max-width: 992px) {
    .movie-hero {
        min-height: auto;
        padding: 2rem 0;
    }
    
    .movie-details {
        padding-left: 0;
        padding-top: 2rem;
        text-align: center;
    }
    
    .movie-title {
        font-size: 2.5rem;
    }
    
    .movie-description {
        margin: 0 auto 2rem;
    }
    
    .movie-actions {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .movie-hero {
        padding: 1.5rem 0;
    }
    
    .movie-title {
        font-size: 2rem;
    }
    
    .movie-description {
        font-size: 1rem;
    }
    
    .meta-item {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }
    
    .movie-actions .btn {
        padding: 0.6rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .info-card {
        padding: 1.5rem;
    }
    
    .movie-info-section,
    .related-section {
        padding: 2rem 0;
    }
}

@media (max-width: 576px) {
    .movie-title {
        font-size: 1.75rem;
    }
    
    .movie-meta-large {
        gap: 0.75rem;
    }
    
    .meta-item {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
    }
    
    .movie-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .movie-actions .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .info-card {
        padding: 1rem;
    }
    
    .info-card h3 {
        font-size: 1.25rem;
    }
    
    .premium-badge-large {
        top: 0.5rem;
        right: 0.5rem;
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* Animation for hero section */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.movie-details > * {
    animation: fadeInUp 0.6s ease forwards;
}

.movie-details > *:nth-child(1) { animation-delay: 0.1s; }
.movie-details > *:nth-child(2) { animation-delay: 0.2s; }
.movie-details > *:nth-child(3) { animation-delay: 0.3s; }
.movie-details > *:nth-child(4) { animation-delay: 0.4s; }
.movie-details > *:nth-child(5) { animation-delay: 0.5s; }

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .movie-hero-overlay {
        background: rgba(0, 0, 0, 0.9);
    }
    
    .info-card {
        border: 2px solid var(--text-color);
    }
    
    .meta-item {
        border: 1px solid var(--text-color);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .movie-details > * {
        animation: none;
    }
    
    .movie-actions .btn:hover {
        transform: none;
    }
    
    .progress-bar {
        transition: none;
    }
}
