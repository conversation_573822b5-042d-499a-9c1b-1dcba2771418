<?php
/**
 * Shahid - Movie Model
 * Professional Video Streaming Platform
 */

class Movie extends Model {
    protected $table = 'movies';
    
    public function getLatest($limit = 20, $offset = 0) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' 
                ORDER BY created_at DESC 
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getFeatured($limit = 10) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' AND featured = 1 
                ORDER BY views DESC, created_at DESC 
                LIMIT :limit";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getPopular($limit = 20, $offset = 0) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' 
                ORDER BY views DESC, rating DESC 
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getByGenre($genre, $limit = 20, $offset = 0) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' AND JSON_CONTAINS(genres, :genre)
                ORDER BY created_at DESC 
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':genre', json_encode($genre));
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getByYear($year, $limit = 20, $offset = 0) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' AND year = :year
                ORDER BY rating DESC, views DESC 
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':year', $year);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function search($query, $limit = 20, $offset = 0) {
        $searchTerm = "%$query%";
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' AND (
                    title LIKE :search OR 
                    description LIKE :search OR 
                    director LIKE :search OR
                    JSON_SEARCH(cast, 'one', :search) IS NOT NULL
                )
                ORDER BY 
                    CASE WHEN title LIKE :search THEN 1 ELSE 2 END,
                    views DESC 
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':search', $searchTerm);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getRelated($movieId, $limit = 6) {
        // Get current movie genres
        $movie = $this->find($movieId);
        if (!$movie || !$movie['genres']) {
            return [];
        }
        
        $genres = json_decode($movie['genres'], true);
        if (empty($genres)) {
            return [];
        }
        
        $genreConditions = [];
        $params = [':movie_id' => $movieId, ':limit' => $limit];
        
        foreach ($genres as $index => $genre) {
            $paramName = ":genre_$index";
            $genreConditions[] = "JSON_CONTAINS(genres, $paramName)";
            $params[$paramName] = json_encode($genre);
        }
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' AND id != :movie_id AND (
                    " . implode(' OR ', $genreConditions) . "
                )
                ORDER BY rating DESC, views DESC 
                LIMIT :limit";
        
        $stmt = $this->db->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function incrementViews($movieId) {
        $sql = "UPDATE {$this->table} SET views = views + 1 WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $movieId);
        return $stmt->execute();
    }
    
    public function getSubtitles($movieId) {
        $sql = "SELECT * FROM subtitles 
                WHERE content_id = :content_id AND content_type = 'movie'
                ORDER BY is_default DESC, language";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':content_id', $movieId);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getAudioTracks($movieId) {
        $sql = "SELECT * FROM audio_tracks 
                WHERE content_id = :content_id AND content_type = 'movie'
                ORDER BY is_default DESC, language";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':content_id', $movieId);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getWithDetails($movieId) {
        $movie = $this->find($movieId);
        if (!$movie) {
            return null;
        }
        
        // Add subtitles and audio tracks
        $movie['subtitles'] = $this->getSubtitles($movieId);
        $movie['audio_tracks'] = $this->getAudioTracks($movieId);
        
        // Decode JSON fields
        $movie['genres'] = json_decode($movie['genres'], true) ?: [];
        $movie['cast'] = json_decode($movie['cast'], true) ?: [];
        $movie['video_quality'] = json_decode($movie['video_quality'], true) ?: [];
        
        return $movie;
    }
    
    public function createMovie($data) {
        // Encode JSON fields
        if (isset($data['genres']) && is_array($data['genres'])) {
            $data['genres'] = json_encode($data['genres']);
        }
        if (isset($data['cast']) && is_array($data['cast'])) {
            $data['cast'] = json_encode($data['cast']);
        }
        if (isset($data['video_quality']) && is_array($data['video_quality'])) {
            $data['video_quality'] = json_encode($data['video_quality']);
        }
        
        // Generate slug if not provided
        if (!isset($data['slug']) && isset($data['title'])) {
            $data['slug'] = $this->generateSlug($data['title']);
        }
        
        return $this->create($data);
    }
    
    public function updateMovie($movieId, $data) {
        // Encode JSON fields
        if (isset($data['genres']) && is_array($data['genres'])) {
            $data['genres'] = json_encode($data['genres']);
        }
        if (isset($data['cast']) && is_array($data['cast'])) {
            $data['cast'] = json_encode($data['cast']);
        }
        if (isset($data['video_quality']) && is_array($data['video_quality'])) {
            $data['video_quality'] = json_encode($data['video_quality']);
        }
        
        return $this->update($movieId, $data);
    }
    
    private function generateSlug($title) {
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
        
        // Check if slug exists
        $originalSlug = $slug;
        $counter = 1;
        
        while ($this->findWhere(['slug' => $slug])) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
}
?>
