<?php
/**
 * Shahid - Home Controller
 * Professional Video Streaming Platform
 */

require_once 'models/Movie.php';
require_once 'models/Series.php';
require_once 'models/User.php';

class HomeController extends Controller {
    
    public function index() {
        try {
            $movieModel = new Movie();
            $seriesModel = new Series();
            
            // Get featured content
            $featuredMovies = $movieModel->getFeatured(5);
            $featuredSeries = $seriesModel->getFeatured(5);
            
            // Get latest content
            $latestMovies = $movieModel->getLatest(10);
            $latestSeries = $seriesModel->getLatest(10);
            
            // Get popular content
            $popularMovies = $movieModel->getPopular(10);
            $popularSeries = $seriesModel->getPopular(10);
            
            // Combine featured content for hero slider
            $heroContent = array_merge(
                array_map(function($movie) {
                    $movie['type'] = 'movie';
                    return $movie;
                }, $featuredMovies),
                array_map(function($series) {
                    $series['type'] = 'series';
                    return $series;
                }, $featuredSeries)
            );
            
            // Shuffle and limit hero content
            shuffle($heroContent);
            $heroContent = array_slice($heroContent, 0, 5);
            
            $data = [
                'page_title' => 'Home - ' . $this->config['site']['name'],
                'hero_content' => $heroContent,
                'latest_movies' => $latestMovies,
                'latest_series' => $latestSeries,
                'popular_movies' => $popularMovies,
                'popular_series' => $popularSeries,
                'user' => $this->auth->getCurrentUser()
            ];
            
            $this->view('home/index', $data);
            
        } catch (Exception $e) {
            error_log('Home Controller Error: ' . $e->getMessage());
            $this->view('errors/500', ['error' => 'Unable to load homepage']);
        }
    }
    
    public function search() {
        $query = $this->sanitizeInput($_GET['q'] ?? '');
        $type = $this->sanitizeInput($_GET['type'] ?? 'all');
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        if (empty($query)) {
            $this->redirect('/');
            return;
        }
        
        try {
            $results = [];
            $totalResults = 0;
            
            if ($type === 'all' || $type === 'movies') {
                $movieModel = new Movie();
                $movieResults = $movieModel->search($query, $limit, $offset);
                $results['movies'] = array_map(function($movie) {
                    $movie['type'] = 'movie';
                    return $movie;
                }, $movieResults);
                
                if ($type === 'movies') {
                    $totalResults = count($movieResults);
                }
            }
            
            if ($type === 'all' || $type === 'series') {
                $seriesModel = new Series();
                $seriesResults = $seriesModel->search($query, $limit, $offset);
                $results['series'] = array_map(function($series) {
                    $series['type'] = 'series';
                    return $series;
                }, $seriesResults);
                
                if ($type === 'series') {
                    $totalResults = count($seriesResults);
                }
            }
            
            if ($type === 'all') {
                $allResults = array_merge($results['movies'] ?? [], $results['series'] ?? []);
                $totalResults = count($allResults);
                
                // Sort by relevance (title matches first)
                usort($allResults, function($a, $b) use ($query) {
                    $aScore = stripos($a['title'], $query) !== false ? 1 : 0;
                    $bScore = stripos($b['title'], $query) !== false ? 1 : 0;
                    
                    if ($aScore === $bScore) {
                        return $b['views'] - $a['views'];
                    }
                    
                    return $bScore - $aScore;
                });
                
                $results['all'] = array_slice($allResults, 0, $limit);
            }
            
            $data = [
                'page_title' => 'Search Results for "' . htmlspecialchars($query) . '"',
                'query' => $query,
                'type' => $type,
                'results' => $results,
                'total_results' => $totalResults,
                'current_page' => $page,
                'has_more' => $totalResults >= $limit,
                'user' => $this->auth->getCurrentUser()
            ];
            
            $this->view('search/results', $data);
            
        } catch (Exception $e) {
            error_log('Search Error: ' . $e->getMessage());
            $this->view('errors/500', ['error' => 'Search temporarily unavailable']);
        }
    }
    
    public function autocomplete() {
        header('Content-Type: application/json');
        
        $query = $this->sanitizeInput($_GET['q'] ?? '');
        
        if (strlen($query) < 2) {
            echo json_encode([]);
            return;
        }
        
        try {
            $movieModel = new Movie();
            $seriesModel = new Series();
            
            $movies = $movieModel->search($query, 5);
            $series = $seriesModel->search($query, 5);
            
            $suggestions = [];
            
            foreach ($movies as $movie) {
                $suggestions[] = [
                    'id' => $movie['id'],
                    'title' => $movie['title'],
                    'type' => 'movie',
                    'year' => $movie['year'],
                    'poster' => $movie['poster'],
                    'url' => '/watch/' . $movie['id'] . '?type=movie'
                ];
            }
            
            foreach ($series as $s) {
                $suggestions[] = [
                    'id' => $s['id'],
                    'title' => $s['title'],
                    'type' => 'series',
                    'year' => $s['year'],
                    'poster' => $s['poster'],
                    'url' => '/series/' . $s['slug']
                ];
            }
            
            // Sort by title relevance
            usort($suggestions, function($a, $b) use ($query) {
                $aScore = stripos($a['title'], $query) !== false ? 1 : 0;
                $bScore = stripos($b['title'], $query) !== false ? 1 : 0;
                
                if ($aScore === $bScore) {
                    return strcmp($a['title'], $b['title']);
                }
                
                return $bScore - $aScore;
            });
            
            echo json_encode(array_slice($suggestions, 0, 8));
            
        } catch (Exception $e) {
            error_log('Autocomplete Error: ' . $e->getMessage());
            echo json_encode([]);
        }
    }
    
    public function trending() {
        try {
            $movieModel = new Movie();
            $seriesModel = new Series();
            
            // Get trending content (most viewed in last 7 days)
            $trendingMovies = $movieModel->getPopular(20);
            $trendingSeries = $seriesModel->getPopular(20);
            
            $data = [
                'page_title' => 'Trending Now',
                'trending_movies' => $trendingMovies,
                'trending_series' => $trendingSeries,
                'user' => $this->auth->getCurrentUser()
            ];
            
            $this->view('home/trending', $data);
            
        } catch (Exception $e) {
            error_log('Trending Error: ' . $e->getMessage());
            $this->view('errors/500', ['error' => 'Unable to load trending content']);
        }
    }
}
?>
