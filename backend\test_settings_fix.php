<?php
/**
 * Test Settings Fix - Shahid Platform
 * Quick test to verify settings page fix
 */

echo "<h1>🔧 اختبار إصلاح صفحة الإعدادات - Shahid Platform</h1>";

// Check if database config exists
if (!file_exists('config/database.php')) {
    echo "<div class='error'>❌ ملف إعدادات قاعدة البيانات غير موجود</div>";
    exit();
}

try {
    $config = include 'config/database.php';
    $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", 
                   $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ اتصال قاعدة البيانات ناجح</div>";
    
    // Test database stats queries
    echo "<h2>📊 اختبار استعلامات الإحصائيات:</h2>";
    
    $dbStats = [];
    
    try {
        // Test movies count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM movies");
        $dbStats['movies'] = $stmt->fetch()['count'];
        echo "<div class='success'>✅ عدد الأفلام: {$dbStats['movies']}</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في عدد الأفلام: " . htmlspecialchars($e->getMessage()) . "</div>";
        $dbStats['movies'] = 0;
    }
    
    try {
        // Test series count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM series");
        $dbStats['series'] = $stmt->fetch()['count'];
        echo "<div class='success'>✅ عدد المسلسلات: {$dbStats['series']}</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في عدد المسلسلات: " . htmlspecialchars($e->getMessage()) . "</div>";
        $dbStats['series'] = 0;
    }
    
    try {
        // Test episodes count (this was the problematic one)
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM episodes");
        $result = $stmt->fetch();
        $dbStats['episodes'] = $result['count'];
        echo "<div class='success'>✅ عدد الحلقات: {$dbStats['episodes']} (تم إصلاح المشكلة)</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في عدد الحلقات: " . htmlspecialchars($e->getMessage()) . "</div>";
        $dbStats['episodes'] = 0;
    }
    
    try {
        // Test users count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $dbStats['users'] = $stmt->fetch()['count'];
        echo "<div class='success'>✅ عدد المستخدمين: {$dbStats['users']}</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في عدد المستخدمين: " . htmlspecialchars($e->getMessage()) . "</div>";
        $dbStats['users'] = 0;
    }
    
    try {
        // Test database size
        $stmt = $pdo->prepare("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'db_size' FROM information_schema.tables WHERE table_schema = ?");
        $stmt->execute([$config['name']]);
        $result = $stmt->fetch();
        $dbStats['size'] = ($result['db_size'] ?? 0) . ' MB';
        echo "<div class='success'>✅ حجم قاعدة البيانات: {$dbStats['size']}</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في حجم قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "</div>";
        $dbStats['size'] = 'Unknown';
    }
    
    // Test settings queries
    echo "<h2>⚙️ اختبار استعلامات الإعدادات:</h2>";
    
    try {
        $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings");
        $settings = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        echo "<div class='success'>✅ تم جلب الإعدادات بنجاح (عدد الإعدادات: " . count($settings) . ")</div>";
        
        if (!empty($settings)) {
            echo "<h3>📋 عينة من الإعدادات:</h3>";
            echo "<ul>";
            $count = 0;
            foreach ($settings as $key => $value) {
                if ($count < 5) {
                    echo "<li><strong>$key:</strong> " . htmlspecialchars($value) . "</li>";
                    $count++;
                }
            }
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في جلب الإعدادات: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    // Test system info
    echo "<h2>💻 معلومات النظام:</h2>";
    
    $systemInfo = [
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time')
    ];
    
    try {
        $stmt = $pdo->query('SELECT VERSION() as version');
        $systemInfo['mysql_version'] = $stmt->fetch()['version'];
    } catch (Exception $e) {
        $systemInfo['mysql_version'] = 'Unknown';
    }
    
    echo "<div class='info'>";
    echo "<h3>📋 تفاصيل النظام:</h3>";
    echo "<ul>";
    foreach ($systemInfo as $key => $value) {
        $label = str_replace('_', ' ', ucfirst($key));
        echo "<li><strong>$label:</strong> $value</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    // Final result
    echo "<h2>🎯 النتيجة النهائية:</h2>";
    echo "<div class='success'>";
    echo "<h3>✅ تم إصلاح مشكلة صفحة الإعدادات بنجاح!</h3>";
    echo "<p>جميع استعلامات الإحصائيات تعمل بشكل صحيح</p>";
    echo "<p>لا توجد أخطاء في مفاتيح المصفوفات</p>";
    echo "</div>";
    
    echo "<h3>🔗 اختبر صفحة الإعدادات:</h3>";
    echo "<p><a href='admin/settings.php' target='_blank' class='btn btn-primary'>⚙️ فتح صفحة الإعدادات</a></p>";
    echo "<p><a href='admin/' target='_blank' class='btn btn-success'>🎛️ لوحة الإدارة الرئيسية</a></p>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات:</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<h4>🔧 الحلول المقترحة:</h4>";
    echo "<ul>";
    echo "<li>تأكد من إنشاء قاعدة البيانات: <a href='create_database.php'>create_database.php</a></li>";
    echo "<li>تحقق من بيانات الاتصال في config/database.php</li>";
    echo "<li>تأكد من تشغيل خادم MySQL/MariaDB</li>";
    echo "</ul>";
    echo "</div>";
}

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 40px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1 {
    color: #E50914;
    border-bottom: 3px solid #E50914;
    padding-bottom: 10px;
    margin-bottom: 30px;
}

h2 {
    color: #333;
    margin-top: 30px;
    margin-bottom: 15px;
    border-left: 4px solid #E50914;
    padding-left: 15px;
}

h3 {
    color: #555;
    margin-top: 20px;
    margin-bottom: 10px;
}

.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: all 0.3s;
}

.btn-primary {
    background: #E50914;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn:hover {
    opacity: 0.8;
    text-decoration: none;
}

p {
    margin: 10px 0;
}

ul {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}

a {
    color: #E50914;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
