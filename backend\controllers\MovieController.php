<?php
/**
 * Shahid - Movie Controller
 * Professional Video Streaming Platform
 */

require_once 'models/Movie.php';
require_once 'models/User.php';

class MovieController extends Controller {
    
    public function index() {
        $page = max(1, intval($_GET['page'] ?? 1));
        $genre = $this->sanitizeInput($_GET['genre'] ?? '');
        $year = intval($_GET['year'] ?? 0);
        $sort = $this->sanitizeInput($_GET['sort'] ?? 'latest');
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        try {
            $movieModel = new Movie();
            $movies = [];
            $totalMovies = 0;
            
            if (!empty($genre)) {
                $movies = $movieModel->getByGenre($genre, $limit, $offset);
                $totalMovies = $movieModel->count(['genres' => $genre]);
            } elseif ($year > 0) {
                $movies = $movieModel->getByYear($year, $limit, $offset);
                $totalMovies = $movieModel->count(['year' => $year]);
            } else {
                switch ($sort) {
                    case 'popular':
                        $movies = $movieModel->getPopular($limit, $offset);
                        break;
                    case 'rating':
                        $movies = $movieModel->findAll($limit, $offset);
                        // Sort by rating would need custom query
                        break;
                    case 'latest':
                    default:
                        $movies = $movieModel->getLatest($limit, $offset);
                        break;
                }
                $totalMovies = $movieModel->count(['status' => 'active']);
            }
            
            // Get available genres and years for filters
            $genres = $this->getAvailableGenres('movies');
            $years = $this->getAvailableYears('movies');
            
            $totalPages = ceil($totalMovies / $limit);
            
            $data = [
                'page_title' => 'Movies - ' . $this->config['site']['name'],
                'movies' => $movies,
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_movies' => $totalMovies,
                'genre' => $genre,
                'year' => $year,
                'sort' => $sort,
                'genres' => $genres,
                'years' => $years,
                'user' => $this->auth->getCurrentUser()
            ];
            
            $this->view('movies/index', $data);
            
        } catch (Exception $e) {
            error_log('Movie Controller Error: ' . $e->getMessage());
            $this->view('errors/500', ['error' => 'Unable to load movies']);
        }
    }
    
    public function show($slug) {
        try {
            $movieModel = new Movie();
            
            // Find movie by slug
            $movies = $movieModel->findWhere(['slug' => $slug, 'status' => 'active']);
            
            if (empty($movies)) {
                $this->view('errors/404', ['message' => 'Movie not found']);
                return;
            }
            
            $movie = $movieModel->getWithDetails($movies[0]['id']);
            
            // Check if user has access (premium content)
            $hasAccess = true;
            if ($movie['premium']) {
                $user = $this->auth->getCurrentUser();
                if (!$user) {
                    $_SESSION['redirect_after_login'] = '/movies/' . $slug;
                    $this->redirect('/login');
                    return;
                }
                
                $userModel = new User();
                $hasAccess = $userModel->hasActiveSubscription($user['id']);
            }
            
            // Get related movies
            $relatedMovies = $movieModel->getRelated($movie['id'], 6);
            
            // Get user's watch progress if logged in
            $watchProgress = null;
            if ($this->auth->isLoggedIn()) {
                $user = $this->auth->getCurrentUser();
                $watchProgress = $this->getUserWatchProgress($user['id'], $movie['id'], 'movie');
            }
            
            $data = [
                'page_title' => $movie['title'] . ' - ' . $this->config['site']['name'],
                'movie' => $movie,
                'related_movies' => $relatedMovies,
                'has_access' => $hasAccess,
                'watch_progress' => $watchProgress,
                'user' => $this->auth->getCurrentUser()
            ];
            
            $this->view('movies/show', $data);
            
        } catch (Exception $e) {
            error_log('Movie Show Error: ' . $e->getMessage());
            $this->view('errors/500', ['error' => 'Unable to load movie']);
        }
    }
    
    public function watch($id) {
        $this->requireAuth();
        
        try {
            $movieModel = new Movie();
            $movie = $movieModel->getWithDetails($id);
            
            if (!$movie || $movie['status'] !== 'active') {
                $this->view('errors/404', ['message' => 'Movie not found']);
                return;
            }
            
            // Check premium access
            if ($movie['premium']) {
                $user = $this->auth->getCurrentUser();
                $userModel = new User();
                
                if (!$userModel->hasActiveSubscription($user['id'])) {
                    $this->redirect('/subscriptions');
                    return;
                }
            }
            
            // Increment view count
            $movieModel->incrementViews($id);
            
            // Get watch progress
            $user = $this->auth->getCurrentUser();
            $watchProgress = $this->getUserWatchProgress($user['id'], $id, 'movie');
            
            // Generate secure video token
            $videoToken = $this->generateVideoToken($id, 'movie', $user['id']);
            
            $data = [
                'page_title' => 'Watch: ' . $movie['title'],
                'movie' => $movie,
                'watch_progress' => $watchProgress,
                'video_token' => $videoToken,
                'user' => $user
            ];
            
            $this->view('player/movie', $data);
            
        } catch (Exception $e) {
            error_log('Movie Watch Error: ' . $e->getMessage());
            $this->view('errors/500', ['error' => 'Unable to play movie']);
        }
    }
    
    public function toggleFavorite() {
        $this->requireAuth();
        header('Content-Type: application/json');
        
        if ($_POST) {
            $this->validateCSRF();
            
            $movieId = intval($_POST['movie_id'] ?? 0);
            $action = $_POST['action'] ?? 'add'; // add or remove
            
            if ($movieId <= 0) {
                $this->json(['success' => false, 'message' => 'Invalid movie ID'], 400);
                return;
            }
            
            try {
                $user = $this->auth->getCurrentUser();
                $userModel = new User();
                
                if ($action === 'add') {
                    $result = $userModel->addToFavorites($user['id'], $movieId, 'movie');
                    $message = $result ? 'Added to favorites' : 'Already in favorites';
                } else {
                    $result = $userModel->removeFromFavorites($user['id'], $movieId, 'movie');
                    $message = $result ? 'Removed from favorites' : 'Not in favorites';
                }
                
                $this->json(['success' => true, 'message' => $message]);
                
            } catch (Exception $e) {
                error_log('Toggle Favorite Error: ' . $e->getMessage());
                $this->json(['success' => false, 'message' => 'Unable to update favorites'], 500);
            }
        } else {
            $this->json(['success' => false, 'message' => 'Invalid request method'], 405);
        }
    }
    
    public function updateProgress() {
        $this->requireAuth();
        header('Content-Type: application/json');
        
        if ($_POST) {
            $movieId = intval($_POST['movie_id'] ?? 0);
            $progress = intval($_POST['progress'] ?? 0);
            $duration = intval($_POST['duration'] ?? 0);
            
            if ($movieId <= 0 || $progress < 0 || $duration <= 0) {
                $this->json(['success' => false, 'message' => 'Invalid parameters'], 400);
                return;
            }
            
            try {
                $user = $this->auth->getCurrentUser();
                $userModel = new User();
                
                $result = $userModel->updateWatchProgress($user['id'], $movieId, 'movie', $progress, $duration);
                
                $this->json(['success' => $result, 'message' => $result ? 'Progress updated' : 'Failed to update progress']);
                
            } catch (Exception $e) {
                error_log('Update Progress Error: ' . $e->getMessage());
                $this->json(['success' => false, 'message' => 'Unable to update progress'], 500);
            }
        } else {
            $this->json(['success' => false, 'message' => 'Invalid request method'], 405);
        }
    }
    
    private function getAvailableGenres($type = 'movies') {
        try {
            $sql = "SELECT DISTINCT JSON_UNQUOTE(JSON_EXTRACT(genres, '$[*]')) as genre 
                    FROM $type 
                    WHERE status = 'active' AND genres IS NOT NULL";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $results = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $genres = [];
            foreach ($results as $result) {
                if (!empty($result)) {
                    $genres[] = $result;
                }
            }
            
            return array_unique($genres);
        } catch (Exception $e) {
            return [];
        }
    }
    
    private function getAvailableYears($type = 'movies') {
        try {
            $sql = "SELECT DISTINCT year FROM $type 
                    WHERE status = 'active' AND year IS NOT NULL 
                    ORDER BY year DESC";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (Exception $e) {
            return [];
        }
    }
    
    private function getUserWatchProgress($userId, $contentId, $contentType) {
        try {
            $sql = "SELECT * FROM watch_history 
                    WHERE user_id = :user_id AND content_id = :content_id AND content_type = :content_type
                    ORDER BY watched_at DESC LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $userId);
            $stmt->bindParam(':content_id', $contentId);
            $stmt->bindParam(':content_type', $contentType);
            $stmt->execute();
            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }
    
    private function generateVideoToken($contentId, $contentType, $userId) {
        $payload = [
            'content_id' => $contentId,
            'content_type' => $contentType,
            'user_id' => $userId,
            'exp' => time() + 3600 // 1 hour expiry
        ];
        
        return $this->security->generateJWT($payload);
    }
}
?>
