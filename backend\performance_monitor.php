<?php
/**
 * نظام مراقبة الأداء
 * يتتبع أداء النظام، استهلاك الذاكرة، وأوقات الاستجابة
 */

class PerformanceMonitor {
    private static $instance = null;
    private $startTime;
    private $startMemory;
    private $checkpoints = [];
    private $queries = [];
    private $logPath;
    
    private function __construct() {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage(true);
        $this->logPath = __DIR__ . '/logs/performance/';
        
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
        
        // تسجيل نهاية الطلب
        register_shutdown_function([$this, 'logRequest']);
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * إضافة نقطة تحقق للأداء
     */
    public function checkpoint($name, $description = '') {
        $this->checkpoints[] = [
            'name' => $name,
            'description' => $description,
            'time' => microtime(true),
            'memory' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'elapsed' => microtime(true) - $this->startTime,
            'memory_diff' => memory_get_usage(true) - $this->startMemory
        ];
    }
    
    /**
     * تسجيل استعلام قاعدة البيانات
     */
    public function logQuery($sql, $executionTime, $params = []) {
        $this->queries[] = [
            'sql' => $sql,
            'execution_time' => $executionTime,
            'params' => $params,
            'memory' => memory_get_usage(true),
            'timestamp' => microtime(true)
        ];
    }
    
    /**
     * الحصول على إحصائيات الأداء الحالية
     */
    public function getStats() {
        $currentTime = microtime(true);
        $currentMemory = memory_get_usage(true);
        
        return [
            'execution_time' => $currentTime - $this->startTime,
            'memory_usage' => $currentMemory,
            'memory_peak' => memory_get_peak_usage(true),
            'memory_diff' => $currentMemory - $this->startMemory,
            'queries_count' => count($this->queries),
            'checkpoints_count' => count($this->checkpoints),
            'server_load' => $this->getServerLoad(),
            'disk_usage' => $this->getDiskUsage()
        ];
    }
    
    /**
     * الحصول على تفاصيل نقاط التحقق
     */
    public function getCheckpoints() {
        return $this->checkpoints;
    }
    
    /**
     * الحصول على تفاصيل الاستعلامات
     */
    public function getQueries() {
        return $this->queries;
    }
    
    /**
     * تحليل الاستعلامات البطيئة
     */
    public function getSlowQueries($threshold = 0.1) {
        return array_filter($this->queries, function($query) use ($threshold) {
            return $query['execution_time'] > $threshold;
        });
    }
    
    /**
     * الحصول على حمولة الخادم
     */
    private function getServerLoad() {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1min' => $load[0],
                '5min' => $load[1],
                '15min' => $load[2]
            ];
        }
        
        return null;
    }
    
    /**
     * الحصول على استخدام القرص
     */
    private function getDiskUsage() {
        $total = disk_total_space(__DIR__);
        $free = disk_free_space(__DIR__);
        $used = $total - $free;
        
        return [
            'total' => $total,
            'used' => $used,
            'free' => $free,
            'percentage' => $total > 0 ? round(($used / $total) * 100, 2) : 0
        ];
    }
    
    /**
     * تسجيل الطلب في نهاية التنفيذ
     */
    public function logRequest() {
        $stats = $this->getStats();
        
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'CLI',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip' => $this->getRealIP(),
            'stats' => $stats,
            'checkpoints' => $this->checkpoints,
            'queries' => $this->queries,
            'slow_queries' => $this->getSlowQueries(),
            'response_code' => http_response_code()
        ];
        
        // تسجيل في ملف يومي
        $logFile = $this->logPath . 'performance_' . date('Y-m-d') . '.log';
        file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);
        
        // تنظيف السجلات القديمة
        $this->cleanOldLogs();
    }
    
    /**
     * تنظيف السجلات القديمة
     */
    private function cleanOldLogs() {
        $files = glob($this->logPath . 'performance_*.log');
        $cutoff = time() - (7 * 24 * 60 * 60); // 7 أيام
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff) {
                unlink($file);
            }
        }
    }
    
    /**
     * الحصول على عنوان IP الحقيقي
     */
    private function getRealIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * تحليل أداء النظام
     */
    public function analyzePerformance($days = 7) {
        $analysis = [
            'summary' => [
                'total_requests' => 0,
                'avg_response_time' => 0,
                'avg_memory_usage' => 0,
                'slow_requests' => 0,
                'error_requests' => 0
            ],
            'daily_stats' => [],
            'slowest_endpoints' => [],
            'memory_intensive_endpoints' => [],
            'most_accessed_endpoints' => []
        ];
        
        $allRequests = [];
        $endpointStats = [];
        
        for ($i = 0; $i < $days; $i++) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $logFile = $this->logPath . "performance_{$date}.log";
            
            if (file_exists($logFile)) {
                $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                $dailyRequests = 0;
                $dailyResponseTime = 0;
                $dailyMemory = 0;
                $dailySlowRequests = 0;
                $dailyErrors = 0;
                
                foreach ($lines as $line) {
                    $data = json_decode($line, true);
                    if ($data) {
                        $allRequests[] = $data;
                        $dailyRequests++;
                        
                        $responseTime = $data['stats']['execution_time'];
                        $memoryUsage = $data['stats']['memory_usage'];
                        $url = $data['url'];
                        $responseCode = $data['response_code'] ?? 200;
                        
                        $dailyResponseTime += $responseTime;
                        $dailyMemory += $memoryUsage;
                        
                        if ($responseTime > 1.0) { // أبطأ من ثانية واحدة
                            $dailySlowRequests++;
                        }
                        
                        if ($responseCode >= 400) {
                            $dailyErrors++;
                        }
                        
                        // إحصائيات نقاط النهاية
                        if (!isset($endpointStats[$url])) {
                            $endpointStats[$url] = [
                                'count' => 0,
                                'total_time' => 0,
                                'total_memory' => 0,
                                'max_time' => 0,
                                'max_memory' => 0
                            ];
                        }
                        
                        $endpointStats[$url]['count']++;
                        $endpointStats[$url]['total_time'] += $responseTime;
                        $endpointStats[$url]['total_memory'] += $memoryUsage;
                        $endpointStats[$url]['max_time'] = max($endpointStats[$url]['max_time'], $responseTime);
                        $endpointStats[$url]['max_memory'] = max($endpointStats[$url]['max_memory'], $memoryUsage);
                    }
                }
                
                $analysis['daily_stats'][$date] = [
                    'requests' => $dailyRequests,
                    'avg_response_time' => $dailyRequests > 0 ? $dailyResponseTime / $dailyRequests : 0,
                    'avg_memory' => $dailyRequests > 0 ? $dailyMemory / $dailyRequests : 0,
                    'slow_requests' => $dailySlowRequests,
                    'error_requests' => $dailyErrors
                ];
            }
        }
        
        // حساب الإحصائيات الإجمالية
        $totalRequests = count($allRequests);
        if ($totalRequests > 0) {
            $totalTime = array_sum(array_column(array_column($allRequests, 'stats'), 'execution_time'));
            $totalMemory = array_sum(array_column(array_column($allRequests, 'stats'), 'memory_usage'));
            
            $analysis['summary']['total_requests'] = $totalRequests;
            $analysis['summary']['avg_response_time'] = $totalTime / $totalRequests;
            $analysis['summary']['avg_memory_usage'] = $totalMemory / $totalRequests;
            $analysis['summary']['slow_requests'] = count(array_filter($allRequests, function($req) {
                return $req['stats']['execution_time'] > 1.0;
            }));
            $analysis['summary']['error_requests'] = count(array_filter($allRequests, function($req) {
                return ($req['response_code'] ?? 200) >= 400;
            }));
        }
        
        // ترتيب نقاط النهاية
        foreach ($endpointStats as $url => &$stats) {
            $stats['avg_time'] = $stats['total_time'] / $stats['count'];
            $stats['avg_memory'] = $stats['total_memory'] / $stats['count'];
        }
        
        // أبطأ نقاط النهاية
        uasort($endpointStats, function($a, $b) {
            return $b['avg_time'] <=> $a['avg_time'];
        });
        $analysis['slowest_endpoints'] = array_slice($endpointStats, 0, 10, true);
        
        // أكثر نقاط النهاية استهلاكاً للذاكرة
        uasort($endpointStats, function($a, $b) {
            return $b['avg_memory'] <=> $a['avg_memory'];
        });
        $analysis['memory_intensive_endpoints'] = array_slice($endpointStats, 0, 10, true);
        
        // أكثر نقاط النهاية وصولاً
        uasort($endpointStats, function($a, $b) {
            return $b['count'] <=> $a['count'];
        });
        $analysis['most_accessed_endpoints'] = array_slice($endpointStats, 0, 10, true);
        
        return $analysis;
    }
    
    /**
     * تنسيق حجم الذاكرة
     */
    public function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * تنسيق الوقت
     */
    public function formatTime($seconds, $precision = 3) {
        if ($seconds < 1) {
            return round($seconds * 1000, $precision) . ' ms';
        }
        
        return round($seconds, $precision) . ' s';
    }
    
    /**
     * الحصول على تقرير الأداء
     */
    public function getPerformanceReport($days = 7) {
        $analysis = $this->analyzePerformance($days);
        $currentStats = $this->getStats();
        
        return [
            'current_stats' => $currentStats,
            'analysis' => $analysis,
            'recommendations' => $this->getRecommendations($analysis),
            'system_info' => [
                'php_version' => PHP_VERSION,
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
            ]
        ];
    }
    
    /**
     * الحصول على توصيات التحسين
     */
    private function getRecommendations($analysis) {
        $recommendations = [];
        
        if ($analysis['summary']['avg_response_time'] > 1.0) {
            $recommendations[] = [
                'type' => 'warning',
                'message' => 'متوسط وقت الاستجابة مرتفع (أكثر من ثانية واحدة)',
                'suggestion' => 'فكر في تحسين الاستعلامات أو إضافة نظام تخزين مؤقت'
            ];
        }
        
        if ($analysis['summary']['slow_requests'] > ($analysis['summary']['total_requests'] * 0.1)) {
            $recommendations[] = [
                'type' => 'error',
                'message' => 'نسبة عالية من الطلبات البطيئة (أكثر من 10%)',
                'suggestion' => 'راجع أبطأ نقاط النهاية وحسن أداءها'
            ];
        }
        
        if ($analysis['summary']['error_requests'] > 0) {
            $recommendations[] = [
                'type' => 'error',
                'message' => 'يوجد طلبات تنتهي بأخطاء',
                'suggestion' => 'راجع سجلات الأخطاء وأصلح المشاكل'
            ];
        }
        
        $diskUsage = $this->getDiskUsage();
        if ($diskUsage['percentage'] > 80) {
            $recommendations[] = [
                'type' => 'warning',
                'message' => 'مساحة القرص منخفضة (' . $diskUsage['percentage'] . '%)',
                'suggestion' => 'قم بتنظيف الملفات غير المطلوبة أو زيادة مساحة التخزين'
            ];
        }
        
        return $recommendations;
    }
}

// تهيئة مراقب الأداء
$performanceMonitor = PerformanceMonitor::getInstance();

// دالة مساعدة لإضافة نقطة تحقق
function performance_checkpoint($name, $description = '') {
    PerformanceMonitor::getInstance()->checkpoint($name, $description);
}

// دالة مساعدة لتسجيل استعلام
function log_query($sql, $executionTime, $params = []) {
    PerformanceMonitor::getInstance()->logQuery($sql, $executionTime, $params);
}

// معالجة طلبات API
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    $action = $_GET['action'];
    $monitor = PerformanceMonitor::getInstance();
    
    switch ($action) {
        case 'current_stats':
            echo json_encode([
                'success' => true,
                'data' => $monitor->getStats()
            ]);
            break;
            
        case 'performance_report':
            $days = $_GET['days'] ?? 7;
            echo json_encode([
                'success' => true,
                'data' => $monitor->getPerformanceReport($days)
            ]);
            break;
            
        case 'analyze':
            $days = $_GET['days'] ?? 7;
            echo json_encode([
                'success' => true,
                'data' => $monitor->analyzePerformance($days)
            ]);
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'error' => 'إجراء غير صالح'
            ]);
    }
    exit;
}
?>
