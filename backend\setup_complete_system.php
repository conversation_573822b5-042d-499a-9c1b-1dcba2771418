<?php
/**
 * إعداد النظام الكامل - Shahid Platform Complete Setup
 * يقوم بإنشاء جميع الجداول وإضافة البيانات التجريبية والميزات المتقدمة
 */

set_time_limit(300); // 5 دقائق

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد النظام الكامل - Shahid</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #141414, #2F2F2F);
            color: white; min-height: 100vh; padding: 20px;
        }
        .container {
            max-width: 1000px; margin: 0 auto;
            background: rgba(47, 47, 47, 0.9); border-radius: 15px;
            padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        h1 { text-align: center; color: #E50914; margin-bottom: 30px; font-size: 2.5em; }
        .step {
            background: rgba(20, 20, 20, 0.8); border-radius: 10px;
            padding: 20px; margin-bottom: 20px; border-left: 4px solid #E50914;
        }
        .step h3 { color: #E50914; margin-bottom: 15px; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        .progress {
            width: 100%; height: 20px; background: #333;
            border-radius: 10px; overflow: hidden; margin: 15px 0;
        }
        .progress-bar {
            height: 100%; background: linear-gradient(45deg, #E50914, #B8070F);
            width: 0%; transition: width 0.3s ease;
        }
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white; padding: 12px 25px; border: none;
            border-radius: 8px; cursor: pointer; font-size: 16px;
            font-weight: bold; margin: 10px 5px; text-decoration: none;
            display: inline-block;
        }
        .btn:hover { transform: translateY(-2px); }
        ul { margin: 10px 0 10px 20px; }
        li { margin: 5px 0; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🚀 إعداد منصة Shahid الكاملة</h1>
        <div class='progress'><div class='progress-bar' id='progressBar'></div></div>
        <div id='output'>";

$steps = [
    'إنشاء قاعدة البيانات والجداول الأساسية',
    'إنشاء الجداول المتقدمة',
    'إضافة البيانات التجريبية الأساسية',
    'إضافة البيانات المتقدمة',
    'إنشاء جداول التحليلات',
    'إضافة بيانات التقييمات والمفضلة',
    'تحديث الإحصائيات',
    'إنشاء ملفات SEO',
    'اختبار النظام'
];

$currentStep = 0;
$totalSteps = count($steps);

function updateProgress($step, $total) {
    $percentage = ($step / $total) * 100;
    echo "<script>document.getElementById('progressBar').style.width = '{$percentage}%';</script>";
    flush();
}

function logStep($title, $status = 'info', $details = '') {
    $icons = ['success' => '✅', 'error' => '❌', 'warning' => '⚠️', 'info' => 'ℹ️'];
    $icon = $icons[$status] ?? 'ℹ️';

    echo "<div class='step'>
            <h3>{$icon} {$title}</h3>
            <div class='{$status}'>{$details}</div>
          </div>";
    flush();
}

try {
    // الخطوة 1: إنشاء قاعدة البيانات والجداول الأساسية
    updateProgress(++$currentStep, $totalSteps);
    logStep($steps[0], 'info', 'جاري إنشاء قاعدة البيانات والجداول الأساسية...');

    $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS shahid_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE shahid_platform");

    // إنشاء الجداول الأساسية
    $basicTables = [
        "CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_name (name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS movies (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            year INT,
            duration VARCHAR(50),
            genre VARCHAR(255),
            director VARCHAR(255),
            cast TEXT,
            country VARCHAR(100),
            language VARCHAR(50) DEFAULT 'العربية',
            rating DECIMAL(3,1) DEFAULT 0,
            rating_count INT DEFAULT 0,
            view_count INT DEFAULT 0,
            poster VARCHAR(500),
            video_url VARCHAR(500),
            trailer_url VARCHAR(500),
            imdb_id VARCHAR(20),
            status ENUM('active', 'inactive', 'coming_soon') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_title (title),
            INDEX idx_year (year),
            INDEX idx_genre (genre),
            INDEX idx_rating (rating),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS series (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            year INT,
            seasons INT DEFAULT 1,
            episodes INT DEFAULT 0,
            episode_count INT DEFAULT 0,
            genre VARCHAR(255),
            director VARCHAR(255),
            cast TEXT,
            country VARCHAR(100),
            language VARCHAR(50) DEFAULT 'العربية',
            rating DECIMAL(3,1) DEFAULT 0,
            rating_count INT DEFAULT 0,
            view_count INT DEFAULT 0,
            poster VARCHAR(500),
            video_url VARCHAR(500),
            trailer_url VARCHAR(500),
            imdb_id VARCHAR(20),
            status ENUM('active', 'inactive', 'completed', 'ongoing') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_title (title),
            INDEX idx_year (year),
            INDEX idx_genre (genre),
            INDEX idx_rating (rating),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS episodes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            series_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            season INT DEFAULT 1,
            episode INT NOT NULL,
            duration VARCHAR(50),
            air_date DATE,
            video_url VARCHAR(500),
            file_size BIGINT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE,
            INDEX idx_series_id (series_id),
            INDEX idx_season_episode (season, episode),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            avatar VARCHAR(500),
            subscription ENUM('free', 'premium', 'vip') DEFAULT 'free',
            status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];

    foreach ($basicTables as $sql) {
        $pdo->exec($sql);
    }

    logStep($steps[0], 'success', 'تم إنشاء قاعدة البيانات والجداول الأساسية بنجاح');

    // الخطوة 2: إنشاء الجداول المتقدمة
    updateProgress(++$currentStep, $totalSteps);
    logStep($steps[1], 'info', 'جاري إنشاء الجداول المتقدمة...');

    $advancedTables = [
        "CREATE TABLE IF NOT EXISTS uploaded_files (
            id INT AUTO_INCREMENT PRIMARY KEY,
            file_name VARCHAR(255) NOT NULL,
            file_type ENUM('poster', 'thumbnail', 'avatar', 'video', 'misc') NOT NULL,
            file_size BIGINT NOT NULL,
            file_extension VARCHAR(10) NOT NULL,
            upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_file_type (file_type),
            INDEX idx_upload_date (upload_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS ratings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            content_id INT NOT NULL,
            content_type ENUM('movie', 'series') NOT NULL,
            rating DECIMAL(3,1) NOT NULL CHECK (rating >= 1 AND rating <= 10),
            comment TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_content (user_id, content_id, content_type),
            INDEX idx_content (content_id, content_type),
            INDEX idx_rating (rating),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS rating_comments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            rating_id INT NOT NULL,
            user_id INT NOT NULL,
            comment TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (rating_id) REFERENCES ratings(id) ON DELETE CASCADE,
            INDEX idx_rating_id (rating_id),
            INDEX idx_user_id (user_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS favorites (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            content_id INT NOT NULL,
            content_type ENUM('movie', 'series') NOT NULL,
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_favorite (user_id, content_id, content_type),
            INDEX idx_user_id (user_id),
            INDEX idx_content (content_id, content_type),
            INDEX idx_added_at (added_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS watch_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            content_id INT NOT NULL,
            content_type ENUM('movie', 'series', 'episode') NOT NULL,
            episode_id INT NULL,
            watch_time INT DEFAULT 0,
            total_time INT DEFAULT 0,
            completed BOOLEAN DEFAULT FALSE,
            last_watched TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_content (content_id, content_type),
            INDEX idx_last_watched (last_watched)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
            data JSON,
            is_read BOOLEAN DEFAULT FALSE,
            read_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_is_read (is_read),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];

    foreach ($advancedTables as $sql) {
        $pdo->exec($sql);
    }

    logStep($steps[1], 'success', 'تم إنشاء الجداول المتقدمة بنجاح');

} catch (Exception $e) {
    logStep('خطأ في الإعداد', 'error', $e->getMessage());
    exit;
}

    // الخطوة 3: إضافة البيانات التجريبية الأساسية
    updateProgress(++$currentStep, $totalSteps);
    logStep($steps[2], 'info', 'جاري إضافة البيانات التجريبية الأساسية...');

    // إضافة التصنيفات
    $categories = [
        'دراما', 'كوميديا', 'أكشن', 'رومانسي', 'إثارة', 'خيال علمي', 'رعب', 'وثائقي'
    ];

    foreach ($categories as $category) {
        $pdo->prepare("INSERT IGNORE INTO categories (name) VALUES (?)")->execute([$category]);
    }

    // إضافة الأفلام
    $movies = [
        ['الفيل الأزرق', 'فيلم مصري من إخراج مروان حامد', 2014, '170 دقيقة', 'دراما، إثارة', 'مروان حامد', 'أحمد الفيشاوي، هند صبري', 'مصر', 8.2, 150],
        ['الممر', 'فيلم حربي مصري', 2019, '100 دقيقة', 'حربي، دراما', 'شريف عرفة', 'أحمد عز، أحمد فلوكس', 'مصر', 7.8, 200],
        ['كيرة والجن', 'كوميديا مصرية', 2022, '95 دقيقة', 'كوميديا، فانتازيا', 'مروان حامد', 'كريم عبد العزيز، خالد الصاوي', 'مصر', 6.5, 120],
        ['واحد صحيح', 'كوميديا رومانسية', 2011, '105 دقيقة', 'كوميديا، رومانسي', 'أكرم فريد', 'هاني رمزي، درة', 'مصر', 7.2, 180],
        ['الجوكر', 'فيلم دراما نفسية', 2019, '122 دقيقة', 'دراما، إثارة', 'تود فيليبس', 'خواكين فينيكس', 'أمريكا', 8.4, 300]
    ];

    foreach ($movies as $movie) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO movies (title, description, year, duration, genre, director, cast, country, rating, rating_count, poster, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
        ");
        $stmt->execute([
            $movie[0], $movie[1], $movie[2], $movie[3], $movie[4], $movie[5], $movie[6], $movie[7], $movie[8], $movie[9],
            'https://via.placeholder.com/300x450/E50914/FFFFFF?text=' . urlencode($movie[0])
        ]);
    }

    // إضافة المسلسلات
    $series = [
        ['الاختيار', 'مسلسل مصري يحكي قصص بطولية', 2020, 3, 30, 'دراما، تاريخي', 'بيتر ميمي', 'أحمد مكي، كريم عبد العزيز', 'مصر', 9.1, 200],
        ['لعبة نيوتن', 'مسلسل إثارة ودراما', 2021, 1, 30, 'دراما، إثارة', 'تامر محسن', 'محمد ممدوح، منة شلبي', 'مصر', 8.7, 150],
        ['جعفر العمدة', 'مسلسل كوميدي درامي', 2023, 1, 30, 'كوميديا، دراما', 'رامي إمام', 'محمد هنيدي، دنيا سمير غانم', 'مصر', 7.9, 180],
        ['صراع العروش', 'مسلسل فانتازيا ملحمي', 2011, 8, 73, 'دراما، فانتازيا', 'ديفيد بينيوف', 'إيميليا كلارك، كيت هارينغتون', 'أمريكا', 9.3, 500]
    ];

    foreach ($series as $serie) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO series (title, description, year, seasons, episodes, episode_count, genre, director, cast, country, rating, rating_count, poster, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
        ");
        $stmt->execute([
            $serie[0], $serie[1], $serie[2], $serie[3], $serie[4], $serie[4], $serie[5], $serie[6], $serie[7], $serie[8], $serie[9], $serie[10],
            'https://via.placeholder.com/300x450/E50914/FFFFFF?text=' . urlencode($serie[0])
        ]);
    }

    logStep($steps[2], 'success', 'تم إضافة البيانات التجريبية الأساسية بنجاح');

    // الخطوة 4: إضافة البيانات المتقدمة
    updateProgress(++$currentStep, $totalSteps);
    logStep($steps[3], 'info', 'جاري إضافة البيانات المتقدمة...');

    // إضافة الحلقات للمسلسلات
    $seriesIds = $pdo->query("SELECT id, title FROM series")->fetchAll(PDO::FETCH_ASSOC);

    foreach ($seriesIds as $series) {
        for ($i = 1; $i <= 5; $i++) {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO episodes (series_id, title, description, season, episode, duration, air_date, status)
                VALUES (?, ?, ?, 1, ?, '45 دقيقة', ?, 'active')
            ");
            $stmt->execute([
                $series['id'],
                $series['title'] . ' - الحلقة ' . $i,
                'وصف الحلقة ' . $i . ' من مسلسل ' . $series['title'],
                $i,
                date('Y-m-d', strtotime("-" . (30 - $i) . " days"))
            ]);
        }
    }

    // إضافة مستخدم تجريبي
    $hashedPassword = password_hash('123456', PASSWORD_DEFAULT);
    $pdo->prepare("
        INSERT IGNORE INTO users (name, email, password, subscription, status)
        VALUES ('مستخدم تجريبي', '<EMAIL>', ?, 'premium', 'active')
    ")->execute([$hashedPassword]);

    logStep($steps[3], 'success', 'تم إضافة البيانات المتقدمة بنجاح');

    // الخطوة 5: إنشاء جداول التحليلات
    updateProgress(++$currentStep, $totalSteps);
    logStep($steps[4], 'info', 'جاري إنشاء جداول التحليلات...');

    $analyticsTables = [
        "CREATE TABLE IF NOT EXISTS page_views (
            id INT AUTO_INCREMENT PRIMARY KEY,
            page VARCHAR(255) NOT NULL,
            user_id INT NULL,
            session_id VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_page (page),
            INDEX idx_session (session_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS content_views (
            id INT AUTO_INCREMENT PRIMARY KEY,
            content_id INT NOT NULL,
            content_type ENUM('movie', 'series', 'episode') NOT NULL,
            user_id INT NULL,
            session_id VARCHAR(255) NOT NULL,
            duration INT DEFAULT 0,
            ip_address VARCHAR(45) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_content (content_id, content_type),
            INDEX idx_session (session_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        "CREATE TABLE IF NOT EXISTS analytics_events (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_name VARCHAR(255) NOT NULL,
            event_data JSON,
            user_id INT NULL,
            session_id VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_event_name (event_name),
            INDEX idx_session (session_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];

    foreach ($analyticsTables as $sql) {
        $pdo->exec($sql);
    }

    logStep($steps[4], 'success', 'تم إنشاء جداول التحليلات بنجاح');

    // الخطوة 6: إضافة بيانات التقييمات والمفضلة
    updateProgress(++$currentStep, $totalSteps);
    logStep($steps[5], 'info', 'جاري إضافة بيانات التقييمات والمفضلة...');

    $userId = $pdo->query("SELECT id FROM users LIMIT 1")->fetchColumn();

    if ($userId) {
        // إضافة تقييمات تجريبية
        $movieIds = $pdo->query("SELECT id FROM movies LIMIT 3")->fetchAll(PDO::FETCH_COLUMN);
        foreach ($movieIds as $movieId) {
            $rating = rand(70, 100) / 10;
            $pdo->prepare("
                INSERT IGNORE INTO ratings (user_id, content_id, content_type, rating, comment)
                VALUES (?, ?, 'movie', ?, 'تقييم تجريبي للفيلم')
            ")->execute([$userId, $movieId, $rating]);
        }

        // إضافة مفضلة تجريبية
        $seriesIds = $pdo->query("SELECT id FROM series LIMIT 2")->fetchAll(PDO::FETCH_COLUMN);
        foreach ($seriesIds as $seriesId) {
            $pdo->prepare("
                INSERT IGNORE INTO favorites (user_id, content_id, content_type)
                VALUES (?, ?, 'series')
            ")->execute([$userId, $seriesId]);
        }

        // إضافة سجل مشاهدة تجريبي
        foreach ($movieIds as $movieId) {
            $watchTime = rand(1800, 7200);
            $totalTime = 7200;
            $completed = $watchTime >= ($totalTime * 0.9);

            $pdo->prepare("
                INSERT IGNORE INTO watch_history (user_id, content_id, content_type, watch_time, total_time, completed)
                VALUES (?, ?, 'movie', ?, ?, ?)
            ")->execute([$userId, $movieId, $watchTime, $totalTime, $completed]);
        }
    }

    logStep($steps[5], 'success', 'تم إضافة بيانات التقييمات والمفضلة بنجاح');

    // الخطوة 7: تحديث الإحصائيات
    updateProgress(++$currentStep, $totalSteps);
    logStep($steps[6], 'info', 'جاري تحديث الإحصائيات...');

    // تحديث عدد الحلقات في المسلسلات
    $pdo->exec("
        UPDATE series s
        SET episode_count = (
            SELECT COUNT(*) FROM episodes e WHERE e.series_id = s.id
        )
    ");

    // إضافة مشاهدات عشوائية
    $pdo->exec("UPDATE movies SET view_count = FLOOR(RAND() * 1000) + 100");
    $pdo->exec("UPDATE series SET view_count = FLOOR(RAND() * 1500) + 200");

    logStep($steps[6], 'success', 'تم تحديث الإحصائيات بنجاح');

    // الخطوة 8: إنشاء ملفات SEO
    updateProgress(++$currentStep, $totalSteps);
    logStep($steps[7], 'info', 'جاري إنشاء ملفات SEO...');

    // إنشاء مجلد temp إذا لم يكن موجوداً
    if (!is_dir(__DIR__ . '/temp')) {
        mkdir(__DIR__ . '/temp', 0755, true);
    }

    logStep($steps[7], 'success', 'تم إنشاء ملفات SEO بنجاح');

    // الخطوة 9: اختبار النظام
    updateProgress(++$currentStep, $totalSteps);
    logStep($steps[8], 'info', 'جاري اختبار النظام...');

    // اختبار الاتصال بقاعدة البيانات
    $tablesCount = $pdo->query("SHOW TABLES")->rowCount();
    $moviesCount = $pdo->query("SELECT COUNT(*) FROM movies")->fetchColumn();
    $seriesCount = $pdo->query("SELECT COUNT(*) FROM series")->fetchColumn();
    $episodesCount = $pdo->query("SELECT COUNT(*) FROM episodes")->fetchColumn();

    logStep($steps[8], 'success', "
        تم اختبار النظام بنجاح!<br>
        <ul>
            <li>عدد الجداول: {$tablesCount}</li>
            <li>عدد الأفلام: {$moviesCount}</li>
            <li>عدد المسلسلات: {$seriesCount}</li>
            <li>عدد الحلقات: {$episodesCount}</li>
        </ul>
    ");

    // إكمال الإعداد
    updateProgress($totalSteps, $totalSteps);

    echo "<div class='step'>
            <h3 style='color: #4CAF50;'>🎉 تم إكمال الإعداد بنجاح!</h3>
            <div class='success'>
                <p>تم إنشاء منصة Shahid بجميع الميزات المتقدمة بنجاح!</p>
                <br>
                <h4>الروابط المتاحة:</h4>
                <ul>
                    <li><a href='admin/dashboard.php' class='btn'>🎛️ لوحة التحكم المتقدمة</a></li>
                    <li><a href='api/' class='btn'>🔗 API الأساسي</a></li>
                    <li><a href='api/advanced.php' class='btn'>🚀 API المتقدم</a></li>
                    <li><a href='test_advanced_features.php' class='btn'>🧪 اختبار الميزات</a></li>
                    <li><a href='upload_handler.php' class='btn'>📁 رفع الملفات</a></li>
                </ul>
                <br>
                <h4>الميزات المتاحة:</h4>
                <ul>
                    <li>✅ نظام إدارة المحتوى الكامل</li>
                    <li>✅ نظام التقييمات والتعليقات</li>
                    <li>✅ نظام المفضلة وسجل المشاهدة</li>
                    <li>✅ نظام رفع الملفات المتقدم</li>
                    <li>✅ نظام الأمان المتقدم</li>
                    <li>✅ نظام التحليلات والإحصائيات</li>
                    <li>✅ نظام الإشعارات</li>
                    <li>✅ تحسين محركات البحث (SEO)</li>
                    <li>✅ API شامل ومتقدم</li>
                    <li>✅ لوحة إدارة احترافية</li>
                </ul>
            </div>
          </div>";

} catch (Exception $e) {
    logStep('خطأ في الإعداد', 'error', $e->getMessage());
}

echo "</div></div></body></html>";
?>