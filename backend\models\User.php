<?php
/**
 * Shahid - User Model
 * Professional Video Streaming Platform
 */

class User extends Model {
    protected $table = 'users';
    
    public function findByEmail($email) {
        return $this->findWhere(['email' => $email]);
    }
    
    public function createUser($data) {
        $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['status'] = 'active';
        $data['role'] = 'user';
        
        return $this->create($data);
    }
    
    public function updateProfile($userId, $data) {
        unset($data['password']); // Don't allow password update through this method
        return $this->update($userId, $data);
    }
    
    public function changePassword($userId, $newPassword) {
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        return $this->update($userId, ['password' => $hashedPassword]);
    }
    
    public function getSubscription($userId) {
        $sql = "SELECT s.*, us.expires_at, us.status as subscription_status 
                FROM user_subscriptions us 
                JOIN subscriptions s ON us.subscription_id = s.id 
                WHERE us.user_id = :user_id AND us.status = 'active'
                ORDER BY us.expires_at DESC LIMIT 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    public function hasActiveSubscription($userId) {
        $subscription = $this->getSubscription($userId);
        return $subscription && strtotime($subscription['expires_at']) > time();
    }
    
    public function getFavorites($userId, $limit = 20, $offset = 0) {
        $sql = "SELECT f.*, 
                CASE 
                    WHEN f.content_type = 'movie' THEN m.title
                    WHEN f.content_type = 'series' THEN s.title
                END as title,
                CASE 
                    WHEN f.content_type = 'movie' THEN m.poster
                    WHEN f.content_type = 'series' THEN s.poster
                END as poster
                FROM favorites f
                LEFT JOIN movies m ON f.content_id = m.id AND f.content_type = 'movie'
                LEFT JOIN series s ON f.content_id = s.id AND f.content_type = 'series'
                WHERE f.user_id = :user_id
                ORDER BY f.created_at DESC
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getWatchHistory($userId, $limit = 20, $offset = 0) {
        $sql = "SELECT wh.*, 
                CASE 
                    WHEN wh.content_type = 'movie' THEN m.title
                    WHEN wh.content_type = 'episode' THEN CONCAT(s.title, ' - Episode ', e.episode_number)
                END as title,
                CASE 
                    WHEN wh.content_type = 'movie' THEN m.poster
                    WHEN wh.content_type = 'episode' THEN s.poster
                END as poster
                FROM watch_history wh
                LEFT JOIN movies m ON wh.content_id = m.id AND wh.content_type = 'movie'
                LEFT JOIN episodes e ON wh.content_id = e.id AND wh.content_type = 'episode'
                LEFT JOIN series s ON e.series_id = s.id
                WHERE wh.user_id = :user_id
                ORDER BY wh.watched_at DESC
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function addToFavorites($userId, $contentId, $contentType) {
        // Check if already in favorites
        $existing = $this->findWhere([
            'user_id' => $userId,
            'content_id' => $contentId,
            'content_type' => $contentType
        ]);
        
        if (empty($existing)) {
            $sql = "INSERT INTO favorites (user_id, content_id, content_type, created_at) 
                    VALUES (:user_id, :content_id, :content_type, NOW())";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $userId);
            $stmt->bindParam(':content_id', $contentId);
            $stmt->bindParam(':content_type', $contentType);
            return $stmt->execute();
        }
        
        return false;
    }
    
    public function removeFromFavorites($userId, $contentId, $contentType) {
        $sql = "DELETE FROM favorites 
                WHERE user_id = :user_id AND content_id = :content_id AND content_type = :content_type";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':content_id', $contentId);
        $stmt->bindParam(':content_type', $contentType);
        return $stmt->execute();
    }
    
    public function updateWatchProgress($userId, $contentId, $contentType, $progress, $duration) {
        // Check if record exists
        $sql = "SELECT id FROM watch_history 
                WHERE user_id = :user_id AND content_id = :content_id AND content_type = :content_type";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':content_id', $contentId);
        $stmt->bindParam(':content_type', $contentType);
        $stmt->execute();
        $existing = $stmt->fetch();
        
        if ($existing) {
            // Update existing record
            $sql = "UPDATE watch_history 
                    SET progress = :progress, duration = :duration, watched_at = NOW()
                    WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':progress', $progress);
            $stmt->bindParam(':duration', $duration);
            $stmt->bindParam(':id', $existing['id']);
        } else {
            // Create new record
            $sql = "INSERT INTO watch_history (user_id, content_id, content_type, progress, duration, watched_at) 
                    VALUES (:user_id, :content_id, :content_type, :progress, :duration, NOW())";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $userId);
            $stmt->bindParam(':content_id', $contentId);
            $stmt->bindParam(':content_type', $contentType);
            $stmt->bindParam(':progress', $progress);
            $stmt->bindParam(':duration', $duration);
        }
        
        return $stmt->execute();
    }
}
?>
