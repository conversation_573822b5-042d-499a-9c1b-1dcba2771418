/// مزودي الحالة الشاملة لتطبيق Shahid Platform
/// يحتوي على جميع مزودي الحالة المستخدمة في التطبيق

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/content_models.dart';
import '../services/api_service.dart';

// ===== مزود حالة التطبيق الرئيسي =====
class AppProvider with ChangeNotifier {
  bool _isLoading = false;
  String? _errorMessage;
  bool _isDarkMode = true;
  String _currentLanguage = 'ar';

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isDarkMode => _isDarkMode;
  String get currentLanguage => _currentLanguage;

  // تعيين حالة التحميل
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // تعيين رسالة الخطأ
  void setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  // مسح الخطأ
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // تبديل الثيم
  void toggleTheme() {
    _isDarkMode = !_isDarkMode;
    notifyListeners();
  }

  // تغيير اللغة
  void changeLanguage(String language) {
    _currentLanguage = language;
    notifyListeners();
  }
}

// ===== مزود حالة المستخدم =====
class UserProvider with ChangeNotifier {
  User? _currentUser;
  bool _isLoggedIn = false;
  List<Favorite> _favorites = [];
  List<WatchHistory> _watchHistory = [];
  List<AppNotification> _notifications = [];

  // Getters
  User? get currentUser => _currentUser;
  bool get isLoggedIn => _isLoggedIn;
  List<Favorite> get favorites => _favorites;
  List<WatchHistory> get watchHistory => _watchHistory;
  List<AppNotification> get notifications => _notifications;
  int get unreadNotificationsCount => 
      _notifications.where((n) => !n.isRead).length;

  // تسجيل الدخول
  Future<bool> login(String email, String password) async {
    try {
      // استدعاء API تسجيل الدخول
      final response = await ApiService.login(email, password);
      if (response['success']) {
        _currentUser = User.fromJson(response['data']['user']);
        _isLoggedIn = true;
        await _loadUserData();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Login error: $e');
      return false;
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    try {
      await ApiService.logout();
    } catch (e) {
      debugPrint('Logout error: $e');
    } finally {
      _currentUser = null;
      _isLoggedIn = false;
      _favorites.clear();
      _watchHistory.clear();
      _notifications.clear();
      notifyListeners();
    }
  }

  // تحديث بيانات المستخدم
  void updateUser(User user) {
    _currentUser = user;
    notifyListeners();
  }

  // تحميل بيانات المستخدم
  Future<void> _loadUserData() async {
    if (_currentUser == null) return;

    try {
      // تحميل المفضلة
      final favoritesResponse = await ApiService.getUserFavorites(_currentUser!.id);
      if (favoritesResponse['success']) {
        _favorites = (favoritesResponse['data'] as List)
            .map((e) => Favorite.fromJson(e))
            .toList();
      }

      // تحميل سجل المشاهدة
      final historyResponse = await ApiService.getUserWatchHistory(_currentUser!.id);
      if (historyResponse['success']) {
        _watchHistory = (historyResponse['data'] as List)
            .map((e) => WatchHistory.fromJson(e))
            .toList();
      }

      // تحميل الإشعارات
      final notificationsResponse = await ApiService.getUserNotifications(_currentUser!.id);
      if (notificationsResponse['success']) {
        _notifications = (notificationsResponse['data'] as List)
            .map((e) => AppNotification.fromJson(e))
            .toList();
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading user data: $e');
    }
  }

  // إضافة/إزالة من المفضلة
  Future<bool> toggleFavorite(int contentId, String contentType) async {
    try {
      final isFavorite = _favorites.any((f) => 
          f.contentId == contentId && f.contentType == contentType);

      if (isFavorite) {
        // إزالة من المفضلة
        final response = await ApiService.removeFavorite(contentId, contentType);
        if (response['success']) {
          _favorites.removeWhere((f) => 
              f.contentId == contentId && f.contentType == contentType);
          notifyListeners();
          return true;
        }
      } else {
        // إضافة للمفضلة
        final response = await ApiService.addFavorite(contentId, contentType);
        if (response['success']) {
          _favorites.add(Favorite.fromJson(response['data']));
          notifyListeners();
          return true;
        }
      }
      return false;
    } catch (e) {
      debugPrint('Toggle favorite error: $e');
      return false;
    }
  }

  // التحقق من وجود المحتوى في المفضلة
  bool isFavorite(int contentId, String contentType) {
    return _favorites.any((f) => 
        f.contentId == contentId && f.contentType == contentType);
  }

  // تحديث سجل المشاهدة
  Future<void> updateWatchHistory(int contentId, String contentType, 
      int watchedDuration, int totalDuration) async {
    try {
      final response = await ApiService.updateWatchHistory(
          contentId, contentType, watchedDuration, totalDuration);
      
      if (response['success']) {
        final existingIndex = _watchHistory.indexWhere((h) => 
            h.contentId == contentId && h.contentType == contentType);
        
        if (existingIndex != -1) {
          _watchHistory[existingIndex] = WatchHistory.fromJson(response['data']);
        } else {
          _watchHistory.add(WatchHistory.fromJson(response['data']));
        }
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Update watch history error: $e');
    }
  }

  // الحصول على تقدم المشاهدة
  double getWatchProgress(int contentId, String contentType) {
    final history = _watchHistory.firstWhere(
      (h) => h.contentId == contentId && h.contentType == contentType,
      orElse: () => WatchHistory(
        id: 0, userId: 0, contentId: contentId, contentType: contentType,
        watchedDuration: 0, totalDuration: 0, lastWatched: DateTime.now(),
      ),
    );
    return history.progressPercentage;
  }

  // تحديث حالة قراءة الإشعار
  Future<void> markNotificationAsRead(int notificationId) async {
    try {
      final response = await ApiService.markNotificationAsRead(notificationId);
      if (response['success']) {
        final index = _notifications.indexWhere((n) => n.id == notificationId);
        if (index != -1) {
          _notifications[index] = AppNotification(
            id: _notifications[index].id,
            userId: _notifications[index].userId,
            title: _notifications[index].title,
            message: _notifications[index].message,
            type: _notifications[index].type,
            isRead: true,
            createdAt: _notifications[index].createdAt,
          );
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Mark notification as read error: $e');
    }
  }
}

// ===== مزود حالة المحتوى =====
class ContentProvider with ChangeNotifier {
  List<Movie> _movies = [];
  List<Series> _series = [];
  List<Movie> _trendingMovies = [];
  List<Series> _trendingSeries = [];
  List<Movie> _recentMovies = [];
  List<Series> _recentSeries = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<Movie> get movies => _movies;
  List<Series> get series => _series;
  List<Movie> get trendingMovies => _trendingMovies;
  List<Series> get trendingSeries => _trendingSeries;
  List<Movie> get recentMovies => _recentMovies;
  List<Series> get recentSeries => _recentSeries;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // تحميل جميع المحتوى
  Future<void> loadAllContent() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      await Future.wait([
        loadMovies(),
        loadSeries(),
        loadTrendingContent(),
        loadRecentContent(),
      ]);
    } catch (e) {
      _errorMessage = 'خطأ في تحميل المحتوى: $e';
      debugPrint('Load content error: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تحميل الأفلام
  Future<void> loadMovies() async {
    try {
      final response = await ApiService.getMovies();
      if (response['success']) {
        _movies = (response['data'] as List)
            .map((e) => Movie.fromJson(e))
            .toList();
      }
    } catch (e) {
      debugPrint('Load movies error: $e');
    }
  }

  // تحميل المسلسلات
  Future<void> loadSeries() async {
    try {
      final response = await ApiService.getSeries();
      if (response['success']) {
        _series = (response['data'] as List)
            .map((e) => Series.fromJson(e))
            .toList();
      }
    } catch (e) {
      debugPrint('Load series error: $e');
    }
  }

  // تحميل المحتوى الرائج
  Future<void> loadTrendingContent() async {
    try {
      final moviesResponse = await ApiService.getTrendingMovies();
      if (moviesResponse['success']) {
        _trendingMovies = (moviesResponse['data'] as List)
            .map((e) => Movie.fromJson(e))
            .toList();
      }

      final seriesResponse = await ApiService.getTrendingSeries();
      if (seriesResponse['success']) {
        _trendingSeries = (seriesResponse['data'] as List)
            .map((e) => Series.fromJson(e))
            .toList();
      }
    } catch (e) {
      debugPrint('Load trending content error: $e');
    }
  }

  // تحميل المحتوى الحديث
  Future<void> loadRecentContent() async {
    try {
      final moviesResponse = await ApiService.getRecentMovies();
      if (moviesResponse['success']) {
        _recentMovies = (moviesResponse['data'] as List)
            .map((e) => Movie.fromJson(e))
            .toList();
      }

      final seriesResponse = await ApiService.getRecentSeries();
      if (seriesResponse['success']) {
        _recentSeries = (seriesResponse['data'] as List)
            .map((e) => Series.fromJson(e))
            .toList();
      }
    } catch (e) {
      debugPrint('Load recent content error: $e');
    }
  }

  // البحث في المحتوى
  Future<SearchResult> searchContent(String query) async {
    try {
      final response = await ApiService.searchContent(query);
      if (response['success']) {
        return SearchResult.fromJson(response['data']);
      }
      return SearchResult(movies: [], series: [], totalResults: 0, query: query);
    } catch (e) {
      debugPrint('Search content error: $e');
      return SearchResult(movies: [], series: [], totalResults: 0, query: query);
    }
  }

  // الحصول على فيلم بالمعرف
  Movie? getMovieById(int id) {
    try {
      return _movies.firstWhere((movie) => movie.id == id);
    } catch (e) {
      return null;
    }
  }

  // الحصول على مسلسل بالمعرف
  Series? getSeriesById(int id) {
    try {
      return _series.firstWhere((series) => series.id == id);
    } catch (e) {
      return null;
    }
  }

  // تحديث تقييم المحتوى
  Future<bool> rateContent(int contentId, String contentType, 
      double rating, String? comment) async {
    try {
      final response = await ApiService.rateContent(
          contentId, contentType, rating, comment);
      
      if (response['success']) {
        // تحديث التقييم في القائمة المحلية
        if (contentType == 'movie') {
          final index = _movies.indexWhere((m) => m.id == contentId);
          if (index != -1) {
            // يمكن تحديث التقييم هنا إذا كان متوفراً في الاستجابة
          }
        } else if (contentType == 'series') {
          final index = _series.indexWhere((s) => s.id == contentId);
          if (index != -1) {
            // يمكن تحديث التقييم هنا إذا كان متوفراً في الاستجابة
          }
        }
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Rate content error: $e');
      return false;
    }
  }

  // تحديث عدد المشاهدات
  Future<void> incrementViewCount(int contentId, String contentType) async {
    try {
      await ApiService.incrementViewCount(contentId, contentType);
      
      // تحديث العدد في القائمة المحلية
      if (contentType == 'movie') {
        final index = _movies.indexWhere((m) => m.id == contentId);
        if (index != -1) {
          _movies[index] = Movie(
            id: _movies[index].id,
            title: _movies[index].title,
            titleAr: _movies[index].titleAr,
            description: _movies[index].description,
            descriptionAr: _movies[index].descriptionAr,
            posterUrl: _movies[index].posterUrl,
            trailerUrl: _movies[index].trailerUrl,
            videoUrl: _movies[index].videoUrl,
            rating: _movies[index].rating,
            duration: _movies[index].duration,
            releaseDate: _movies[index].releaseDate,
            category: _movies[index].category,
            language: _movies[index].language,
            country: _movies[index].country,
            director: _movies[index].director,
            cast: _movies[index].cast,
            genres: _movies[index].genres,
            viewCount: _movies[index].viewCount + 1,
            isPremium: _movies[index].isPremium,
            status: _movies[index].status,
            createdAt: _movies[index].createdAt,
            updatedAt: _movies[index].updatedAt,
          );
        }
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Increment view count error: $e');
    }
  }
}

// ===== مزود حالة المشغل =====
class PlayerProvider with ChangeNotifier {
  bool _isPlaying = false;
  bool _isFullscreen = false;
  double _currentPosition = 0.0;
  double _duration = 0.0;
  double _volume = 1.0;
  double _playbackSpeed = 1.0;
  bool _showControls = true;

  // Getters
  bool get isPlaying => _isPlaying;
  bool get isFullscreen => _isFullscreen;
  double get currentPosition => _currentPosition;
  double get duration => _duration;
  double get volume => _volume;
  double get playbackSpeed => _playbackSpeed;
  bool get showControls => _showControls;
  double get progress => duration > 0 ? currentPosition / duration : 0.0;

  // تشغيل/إيقاف
  void togglePlayPause() {
    _isPlaying = !_isPlaying;
    notifyListeners();
  }

  // تعيين حالة التشغيل
  void setPlaying(bool playing) {
    _isPlaying = playing;
    notifyListeners();
  }

  // تبديل الشاشة الكاملة
  void toggleFullscreen() {
    _isFullscreen = !_isFullscreen;
    notifyListeners();
  }

  // تحديث الموضع الحالي
  void updatePosition(double position) {
    _currentPosition = position;
    notifyListeners();
  }

  // تعيين المدة الإجمالية
  void setDuration(double duration) {
    _duration = duration;
    notifyListeners();
  }

  // تعيين مستوى الصوت
  void setVolume(double volume) {
    _volume = volume.clamp(0.0, 1.0);
    notifyListeners();
  }

  // تعيين سرعة التشغيل
  void setPlaybackSpeed(double speed) {
    _playbackSpeed = speed;
    notifyListeners();
  }

  // إظهار/إخفاء عناصر التحكم
  void toggleControls() {
    _showControls = !_showControls;
    notifyListeners();
  }

  // الانتقال إلى موضع معين
  void seekTo(double position) {
    _currentPosition = position.clamp(0.0, _duration);
    notifyListeners();
  }

  // إعادة تعيين المشغل
  void reset() {
    _isPlaying = false;
    _isFullscreen = false;
    _currentPosition = 0.0;
    _duration = 0.0;
    _volume = 1.0;
    _playbackSpeed = 1.0;
    _showControls = true;
    notifyListeners();
  }
}
