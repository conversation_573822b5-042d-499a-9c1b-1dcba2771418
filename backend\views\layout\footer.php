    </main>
    
    <!-- Footer -->
    <footer class="footer bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <!-- About Section -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 class="mb-3">
                        <img src="/assets/images/logo.png" alt="Shahid" height="30" class="me-2">
                        Shah<PERSON>
                    </h5>
                    <p class="text-muted">
                        منصة احترافية لمشاهدة المسلسلات والأفلام بجودة عالية. استمتع بأفضل المحتوى العربي والعالمي.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="/" class="text-muted text-decoration-none">الرئيسية</a></li>
                        <li><a href="/movies" class="text-muted text-decoration-none">الأفلام</a></li>
                        <li><a href="/series" class="text-muted text-decoration-none">المسلسلات</a></li>
                        <li><a href="/trending" class="text-muted text-decoration-none">الأكثر مشاهدة</a></li>
                    </ul>
                </div>
                
                <!-- Categories -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">التصنيفات</h6>
                    <ul class="list-unstyled">
                        <li><a href="/movies?genre=action" class="text-muted text-decoration-none">أكشن</a></li>
                        <li><a href="/movies?genre=drama" class="text-muted text-decoration-none">دراما</a></li>
                        <li><a href="/movies?genre=comedy" class="text-muted text-decoration-none">كوميديا</a></li>
                        <li><a href="/movies?genre=horror" class="text-muted text-decoration-none">رعب</a></li>
                    </ul>
                </div>
                
                <!-- Support -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">الدعم</h6>
                    <ul class="list-unstyled">
                        <li><a href="/help" class="text-muted text-decoration-none">مركز المساعدة</a></li>
                        <li><a href="/contact" class="text-muted text-decoration-none">اتصل بنا</a></li>
                        <li><a href="/privacy" class="text-muted text-decoration-none">سياسة الخصوصية</a></li>
                        <li><a href="/terms" class="text-muted text-decoration-none">شروط الاستخدام</a></li>
                    </ul>
                </div>
                
                <!-- Subscription -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">الاشتراك</h6>
                    <ul class="list-unstyled">
                        <li><a href="/subscriptions" class="text-muted text-decoration-none">خطط الاشتراك</a></li>
                        <li><a href="/gift-cards" class="text-muted text-decoration-none">بطاقات الهدايا</a></li>
                        <li><a href="/download" class="text-muted text-decoration-none">تحميل التطبيق</a></li>
                    </ul>
                </div>
            </div>
            
            <hr class="my-4">
            
            <!-- Bottom Footer -->
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        &copy; <?= date('Y') ?> Shahid. جميع الحقوق محفوظة.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="payment-methods">
                        <img src="/assets/images/visa.png" alt="Visa" height="24" class="me-2">
                        <img src="/assets/images/mastercard.png" alt="Mastercard" height="24" class="me-2">
                        <img src="/assets/images/paypal.png" alt="PayPal" height="24">
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Back to Top Button -->
    <button class="btn-back-to-top" id="backToTop">
        <i class="fas fa-chevron-up"></i>
    </button>
    
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/jquery.min.js"></script>
    <script src="/assets/js/app.js"></script>
    
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?= $js ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Google Analytics -->
    <?php if (!empty($config['seo']['google_analytics_id'])): ?>
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?= $config['seo']['google_analytics_id'] ?>"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '<?= $config['seo']['google_analytics_id'] ?>');
    </script>
    <?php endif; ?>
    
    <!-- Custom Scripts -->
    <script>
        // CSRF Token for AJAX requests
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        // Back to top button
        $(window).scroll(function() {
            if ($(this).scrollTop() > 100) {
                $('#backToTop').fadeIn();
            } else {
                $('#backToTop').fadeOut();
            }
        });
        
        $('#backToTop').click(function() {
            $('html, body').animate({scrollTop: 0}, 800);
            return false;
        });
        
        // Search autocomplete
        let searchTimeout;
        $('.search-container input').on('input', function() {
            const query = $(this).val();
            const suggestions = $('.search-suggestions');
            
            clearTimeout(searchTimeout);
            
            if (query.length < 2) {
                suggestions.hide();
                return;
            }
            
            searchTimeout = setTimeout(function() {
                $.get('/autocomplete', {q: query})
                    .done(function(data) {
                        if (data.length > 0) {
                            let html = '<div class="suggestions-list">';
                            data.forEach(function(item) {
                                html += `
                                    <a href="${item.url}" class="suggestion-item">
                                        <img src="${item.poster || '/assets/images/no-poster.jpg'}" alt="${item.title}">
                                        <div class="suggestion-info">
                                            <div class="suggestion-title">${item.title}</div>
                                            <div class="suggestion-meta">${item.type} • ${item.year}</div>
                                        </div>
                                    </a>
                                `;
                            });
                            html += '</div>';
                            suggestions.html(html).show();
                        } else {
                            suggestions.hide();
                        }
                    })
                    .fail(function() {
                        suggestions.hide();
                    });
            }, 300);
        });
        
        // Hide suggestions when clicking outside
        $(document).click(function(e) {
            if (!$(e.target).closest('.search-container').length) {
                $('.search-suggestions').hide();
            }
        });
        
        // Loading overlay functions
        function showLoading() {
            $('#loadingOverlay').fadeIn();
        }
        
        function hideLoading() {
            $('#loadingOverlay').fadeOut();
        }
        
        // Auto-hide alerts
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    </script>
    
    <?php if (isset($inline_js)): ?>
        <script><?= $inline_js ?></script>
    <?php endif; ?>
    
</body>
</html>
