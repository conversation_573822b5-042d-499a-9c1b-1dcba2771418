<?php
/**
 * Shahid - View Class
 * Professional Video Streaming Platform
 * 
 * Handles view rendering and template management
 */

class View {
    private $viewsPath;
    private $data = [];
    private $layout = null;
    
    public function __construct($viewsPath = 'views/') {
        $this->viewsPath = rtrim($viewsPath, '/') . '/';
    }
    
    /**
     * Set data for the view
     */
    public function set($key, $value = null) {
        if (is_array($key)) {
            $this->data = array_merge($this->data, $key);
        } else {
            $this->data[$key] = $value;
        }
        return $this;
    }
    
    /**
     * Get data from the view
     */
    public function get($key, $default = null) {
        return isset($this->data[$key]) ? $this->data[$key] : $default;
    }
    
    /**
     * Set layout template
     */
    public function setLayout($layout) {
        $this->layout = $layout;
        return $this;
    }
    
    /**
     * Render a view
     */
    public function render($view, $data = [], $return = false) {
        // Merge data
        $viewData = array_merge($this->data, $data);
        
        // Extract data to variables
        extract($viewData);
        
        // Start output buffering
        ob_start();
        
        // Include view file
        $viewFile = $this->viewsPath . $view . '.php';
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            throw new Exception("View file not found: $viewFile");
        }
        
        // Get content
        $content = ob_get_clean();
        
        // If layout is set, render with layout
        if ($this->layout) {
            $layoutFile = $this->viewsPath . 'layouts/' . $this->layout . '.php';
            if (file_exists($layoutFile)) {
                ob_start();
                include $layoutFile;
                $content = ob_get_clean();
            }
        }
        
        // Return or output
        if ($return) {
            return $content;
        } else {
            echo $content;
        }
    }
    
    /**
     * Render JSON response
     */
    public function json($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    /**
     * Render error page
     */
    public function error($code = 404, $message = null) {
        http_response_code($code);
        
        $errorMessages = [
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Page Not Found',
            500 => 'Internal Server Error'
        ];
        
        $title = isset($errorMessages[$code]) ? $errorMessages[$code] : 'Error';
        $message = $message ?: $title;
        
        $this->set([
            'title' => $title,
            'message' => $message,
            'code' => $code
        ]);
        
        // Try to render custom error page
        $errorView = "errors/$code";
        $errorFile = $this->viewsPath . $errorView . '.php';
        
        if (file_exists($errorFile)) {
            $this->render($errorView);
        } else {
            // Render default error page
            $this->renderDefaultError($code, $title, $message);
        }
    }
    
    /**
     * Render default error page
     */
    private function renderDefaultError($code, $title, $message) {
        echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>$title - Shahid</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .error-container { background: white; padding: 60px 40px; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 500px; }
        .error-code { font-size: 72px; font-weight: bold; color: #E50914; margin-bottom: 20px; }
        .error-title { font-size: 24px; color: #333; margin-bottom: 15px; }
        .error-message { color: #666; margin-bottom: 30px; line-height: 1.6; }
        .btn { display: inline-block; background: linear-gradient(45deg, #E50914, #B20710); color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; font-weight: 500; transition: transform 0.2s; }
        .btn:hover { transform: translateY(-2px); }
    </style>
</head>
<body>
    <div class='error-container'>
        <div class='error-code'>$code</div>
        <h1 class='error-title'>$title</h1>
        <p class='error-message'>$message</p>
        <a href='/' class='btn'>العودة للرئيسية</a>
    </div>
</body>
</html>";
    }
    
    /**
     * Redirect to URL
     */
    public function redirect($url, $statusCode = 302) {
        http_response_code($statusCode);
        header("Location: $url");
        exit;
    }
    
    /**
     * Include partial view
     */
    public function partial($partial, $data = []) {
        $viewData = array_merge($this->data, $data);
        extract($viewData);
        
        $partialFile = $this->viewsPath . 'partials/' . $partial . '.php';
        if (file_exists($partialFile)) {
            include $partialFile;
        } else {
            throw new Exception("Partial file not found: $partialFile");
        }
    }
    
    /**
     * Escape HTML
     */
    public function escape($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Format date
     */
    public function formatDate($date, $format = 'Y-m-d H:i:s') {
        if ($date instanceof DateTime) {
            return $date->format($format);
        } elseif (is_string($date)) {
            return date($format, strtotime($date));
        }
        return $date;
    }
    
    /**
     * Format file size
     */
    public function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * Truncate text
     */
    public function truncate($text, $length = 100, $suffix = '...') {
        if (strlen($text) <= $length) {
            return $text;
        }
        
        return substr($text, 0, $length) . $suffix;
    }
    
    /**
     * Generate URL
     */
    public function url($path = '') {
        $baseUrl = rtrim($_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']), '/');
        return $baseUrl . '/' . ltrim($path, '/');
    }
    
    /**
     * Generate asset URL
     */
    public function asset($path) {
        return $this->url('assets/' . ltrim($path, '/'));
    }
    
    /**
     * Check if current page matches path
     */
    public function isActive($path) {
        $currentPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        return $currentPath === $path;
    }
    
    /**
     * Include CSS file
     */
    public function css($file) {
        echo '<link rel="stylesheet" href="' . $this->asset('css/' . $file) . '">';
    }
    
    /**
     * Include JS file
     */
    public function js($file) {
        echo '<script src="' . $this->asset('js/' . $file) . '"></script>';
    }
}
?>
