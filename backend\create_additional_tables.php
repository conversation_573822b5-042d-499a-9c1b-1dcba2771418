<?php
/**
 * إنشاء الجداول الإضافية للميزات المتقدمة
 */

require_once 'config/database.php';

try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔧 إنشاء الجداول الإضافية...</h2>";
    
    // جدول الملفات المرفوعة
    $sql = "CREATE TABLE IF NOT EXISTS uploaded_files (
        id INT AUTO_INCREMENT PRIMARY KEY,
        file_name VARCHAR(255) NOT NULL,
        file_type ENUM('poster', 'thumbnail', 'avatar', 'video', 'misc') NOT NULL,
        file_size BIGINT NOT NULL,
        file_extension VARCHAR(10) NOT NULL,
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_file_type (file_type),
        INDEX idx_upload_date (upload_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول uploaded_files<br>";
    
    // جدول التقييمات
    $sql = "CREATE TABLE IF NOT EXISTS ratings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        content_id INT NOT NULL,
        content_type ENUM('movie', 'series') NOT NULL,
        rating DECIMAL(3,1) NOT NULL CHECK (rating >= 1 AND rating <= 10),
        comment TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_content (user_id, content_id, content_type),
        INDEX idx_content (content_id, content_type),
        INDEX idx_rating (rating),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول ratings<br>";
    
    // جدول تعليقات التقييمات
    $sql = "CREATE TABLE IF NOT EXISTS rating_comments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        rating_id INT NOT NULL,
        user_id INT NOT NULL,
        comment TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (rating_id) REFERENCES ratings(id) ON DELETE CASCADE,
        INDEX idx_rating_id (rating_id),
        INDEX idx_user_id (user_id),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول rating_comments<br>";
    
    // جدول المفضلة
    $sql = "CREATE TABLE IF NOT EXISTS favorites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        content_id INT NOT NULL,
        content_type ENUM('movie', 'series') NOT NULL,
        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_favorite (user_id, content_id, content_type),
        INDEX idx_user_id (user_id),
        INDEX idx_content (content_id, content_type),
        INDEX idx_added_at (added_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول favorites<br>";
    
    // جدول سجل المشاهدة
    $sql = "CREATE TABLE IF NOT EXISTS watch_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        content_id INT NOT NULL,
        content_type ENUM('movie', 'series', 'episode') NOT NULL,
        episode_id INT NULL,
        watch_time INT DEFAULT 0,
        total_time INT DEFAULT 0,
        completed BOOLEAN DEFAULT FALSE,
        last_watched TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_content (content_id, content_type),
        INDEX idx_last_watched (last_watched)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول watch_history<br>";
    
    // جدول الإشعارات
    $sql = "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_is_read (is_read),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول notifications<br>";
    
    // جدول إعدادات المستخدم
    $sql = "CREATE TABLE IF NOT EXISTS user_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL UNIQUE,
        language VARCHAR(10) DEFAULT 'ar',
        theme ENUM('light', 'dark') DEFAULT 'dark',
        notifications_enabled BOOLEAN DEFAULT TRUE,
        email_notifications BOOLEAN DEFAULT TRUE,
        auto_play BOOLEAN DEFAULT TRUE,
        video_quality ENUM('auto', '360p', '480p', '720p', '1080p') DEFAULT 'auto',
        subtitle_language VARCHAR(10) DEFAULT 'ar',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول user_settings<br>";
    
    // جدول جلسات المستخدمين
    $sql = "CREATE TABLE IF NOT EXISTS user_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        session_token VARCHAR(255) NOT NULL UNIQUE,
        device_info TEXT,
        ip_address VARCHAR(45),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        INDEX idx_user_id (user_id),
        INDEX idx_session_token (session_token),
        INDEX idx_expires_at (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول user_sessions<br>";
    
    // جدول إحصائيات النظام
    $sql = "CREATE TABLE IF NOT EXISTS system_stats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        stat_name VARCHAR(100) NOT NULL UNIQUE,
        stat_value BIGINT DEFAULT 0,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_stat_name (stat_name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول system_stats<br>";
    
    // إضافة بيانات إحصائيات أولية
    $stats = [
        'total_users' => 0,
        'total_movies' => 0,
        'total_series' => 0,
        'total_episodes' => 0,
        'total_views' => 0,
        'total_ratings' => 0
    ];
    
    foreach ($stats as $name => $value) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO system_stats (stat_name, stat_value) VALUES (?, ?)");
        $stmt->execute([$name, $value]);
    }
    echo "✅ تم إضافة الإحصائيات الأولية<br>";
    
    // تحديث الإحصائيات الحالية
    $pdo->exec("UPDATE system_stats SET stat_value = (SELECT COUNT(*) FROM movies) WHERE stat_name = 'total_movies'");
    $pdo->exec("UPDATE system_stats SET stat_value = (SELECT COUNT(*) FROM series) WHERE stat_name = 'total_series'");
    $pdo->exec("UPDATE system_stats SET stat_value = (SELECT COUNT(*) FROM episodes) WHERE stat_name = 'total_episodes'");
    echo "✅ تم تحديث الإحصائيات<br>";
    
    // إضافة أعمدة جديدة للجداول الموجودة إذا لم تكن موجودة
    try {
        $pdo->exec("ALTER TABLE movies ADD COLUMN IF NOT EXISTS video_url VARCHAR(500)");
        $pdo->exec("ALTER TABLE movies ADD COLUMN IF NOT EXISTS trailer_url VARCHAR(500)");
        $pdo->exec("ALTER TABLE movies ADD COLUMN IF NOT EXISTS imdb_id VARCHAR(20)");
        echo "✅ تم تحديث جدول movies<br>";
    } catch (Exception $e) {
        // الأعمدة موجودة بالفعل
    }
    
    try {
        $pdo->exec("ALTER TABLE series ADD COLUMN IF NOT EXISTS video_url VARCHAR(500)");
        $pdo->exec("ALTER TABLE series ADD COLUMN IF NOT EXISTS trailer_url VARCHAR(500)");
        $pdo->exec("ALTER TABLE series ADD COLUMN IF NOT EXISTS imdb_id VARCHAR(20)");
        echo "✅ تم تحديث جدول series<br>";
    } catch (Exception $e) {
        // الأعمدة موجودة بالفعل
    }
    
    try {
        $pdo->exec("ALTER TABLE episodes ADD COLUMN IF NOT EXISTS video_url VARCHAR(500)");
        $pdo->exec("ALTER TABLE episodes ADD COLUMN IF NOT EXISTS file_size BIGINT");
        echo "✅ تم تحديث جدول episodes<br>";
    } catch (Exception $e) {
        // الأعمدة موجودة بالفعل
    }
    
    echo "<br><h3>🎉 تم إنشاء جميع الجداول الإضافية بنجاح!</h3>";
    echo "<p>الجداول المضافة:</p>";
    echo "<ul>";
    echo "<li>uploaded_files - لإدارة الملفات المرفوعة</li>";
    echo "<li>ratings - لتقييمات المستخدمين</li>";
    echo "<li>rating_comments - لتعليقات التقييمات</li>";
    echo "<li>favorites - لقائمة المفضلة</li>";
    echo "<li>watch_history - لسجل المشاهدة</li>";
    echo "<li>notifications - للإشعارات</li>";
    echo "<li>user_settings - لإعدادات المستخدم</li>";
    echo "<li>user_sessions - لجلسات المستخدمين</li>";
    echo "<li>system_stats - لإحصائيات النظام</li>";
    echo "</ul>";
    
    echo "<br><a href='admin/' style='background: #E50914; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🎛️ الذهاب للوحة الإدارة</a>";
    echo " <a href='api/' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>🔗 اختبار API</a>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ في قاعدة البيانات:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>وجود قاعدة البيانات shahid_platform</li>";
    echo "<li>صحة بيانات الاتصال</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء الجداول الإضافية - Shahid</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #141414, #2F2F2F);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        
        h2, h3 {
            color: #E50914;
            text-align: center;
        }
        
        ul {
            background: rgba(20, 20, 20, 0.8);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        li {
            margin: 10px 0;
            padding: 5px 0;
            border-bottom: 1px solid #444;
        }
        
        a {
            display: inline-block;
            margin: 10px 5px;
        }
        
        .success {
            color: #4CAF50;
        }
        
        .error {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم إدراجه بواسطة PHP أعلاه -->
    </div>
</body>
</html>
