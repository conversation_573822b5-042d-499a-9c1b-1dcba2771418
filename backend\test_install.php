<?php
/**
 * Test Installation Steps
 * Quick test to verify installation flow
 */

echo "<h2>Testing Installation Flow</h2>";

// Test step progression
$steps = [
    1 => "Requirements Check",
    2 => "Database Configuration", 
    3 => "Create Tables",
    4 => "Admin Account",
    5 => "Site Configuration",
    6 => "Complete"
];

echo "<h3>Installation Steps:</h3>";
foreach ($steps as $num => $title) {
    echo "<p>Step {$num}: {$title}</p>";
}

// Test file checks
echo "<h3>File Status:</h3>";

$files = [
    'config/database.php' => 'Database Config',
    'config/tables_created.lock' => 'Tables Created',
    'config/admin_created.lock' => 'Admin Created', 
    'config/installed.lock' => 'Installation Complete'
];

foreach ($files as $file => $desc) {
    $exists = file_exists($file) ? '✅' : '❌';
    echo "<p>{$exists} {$desc}: {$file}</p>";
}

// Test navigation links
echo "<h3>Navigation Test:</h3>";
for ($i = 1; $i <= 6; $i++) {
    echo "<a href='install.php?step={$i}' style='margin-right: 10px;'>Step {$i}</a>";
}

echo "<br><br><a href='install.php'>Start Installation</a>";
?>
