<?php
/**
 * Quick Fix - Shahid Platform
 * One-click solution to fix database issues
 */

echo "<h1>🔧 إصلاح سريع - Shahid Platform</h1>";

$step = $_GET['step'] ?? '1';

if ($step === '1') {
    // Step 1: Check and create database
    echo "<h2>الخطوة 1: فحص وإنشاء قاعدة البيانات</h2>";
    
    if (!file_exists('config/database.php')) {
        echo "<div class='error'>❌ ملف إعدادات قاعدة البيانات غير موجود</div>";
        echo "<p><a href='install_simple.php' class='btn'>🔧 بدء التثبيت</a></p>";
        exit();
    }
    
    $config = include 'config/database.php';
    echo "<div class='info'>📋 إعدادات قاعدة البيانات موجودة</div>";
    
    try {
        // Try to connect to MySQL server
        $pdo = new PDO("mysql:host={$config['host']};charset=utf8mb4", $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<div class='success'>✅ اتصال MySQL ناجح</div>";
        
        // Check if database exists
        $stmt = $pdo->query("SHOW DATABASES LIKE '{$config['name']}'");
        if ($stmt->rowCount() === 0) {
            echo "<div class='warning'>⚠️ قاعدة البيانات غير موجودة</div>";
            echo "<p><a href='create_database.php' class='btn btn-primary'>🗄️ إنشاء قاعدة البيانات</a></p>";
        } else {
            echo "<div class='success'>✅ قاعدة البيانات موجودة</div>";
            
            // Check if tables exist
            $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", 
                           $config['username'], $config['password']);
            
            $tables = ['users', 'movies', 'series', 'episodes'];
            $missingTables = [];
            
            foreach ($tables as $table) {
                $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() === 0) {
                    $missingTables[] = $table;
                }
            }
            
            if (!empty($missingTables)) {
                echo "<div class='warning'>⚠️ جداول مفقودة: " . implode(', ', $missingTables) . "</div>";
                echo "<p><a href='create_database.php' class='btn btn-primary'>🗄️ إنشاء الجداول</a></p>";
            } else {
                echo "<div class='success'>✅ جميع الجداول موجودة</div>";
                echo "<p><a href='?step=2' class='btn btn-success'>➡️ الخطوة التالية</a></p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في الاتصال: " . htmlspecialchars($e->getMessage()) . "</div>";
        echo "<div class='info'>";
        echo "<h3>🔧 حلول مقترحة:</h3>";
        echo "<ul>";
        echo "<li>تأكد من تشغيل خادم MySQL</li>";
        echo "<li>تحقق من بيانات الاتصال في config/database.php</li>";
        echo "<li>تأكد من صحة اسم المستخدم وكلمة المرور</li>";
        echo "</ul>";
        echo "</div>";
    }
    
} elseif ($step === '2') {
    // Step 2: Test API and Admin
    echo "<h2>الخطوة 2: اختبار API ولوحة الإدارة</h2>";
    
    // Test API
    echo "<h3>🔗 اختبار API:</h3>";
    $apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/status';
    
    try {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 5
            ]
        ]);
        
        $response = @file_get_contents($apiUrl, false, $context);
        if ($response) {
            $data = json_decode($response, true);
            if ($data && isset($data['success']) && $data['success']) {
                echo "<div class='success'>✅ API يعمل بشكل صحيح</div>";
                echo "<p><a href='api/test_api.php' target='_blank' class='btn'>🧪 اختبار API مفصل</a></p>";
            } else {
                echo "<div class='error'>❌ API لا يعمل بشكل صحيح</div>";
            }
        } else {
            echo "<div class='error'>❌ لا يمكن الوصول للـ API</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في اختبار API: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    // Test Admin Panel
    echo "<h3>🎛️ اختبار لوحة الإدارة:</h3>";
    if (file_exists('admin/index.php')) {
        echo "<div class='success'>✅ لوحة الإدارة موجودة</div>";
        echo "<p><a href='admin/' target='_blank' class='btn'>🎛️ فتح لوحة الإدارة</a></p>";
        echo "<div class='info'>";
        echo "<strong>بيانات الدخول:</strong><br>";
        echo "اسم المستخدم: admin<br>";
        echo "كلمة المرور: admin123";
        echo "</div>";
    } else {
        echo "<div class='error'>❌ لوحة الإدارة غير موجودة</div>";
    }
    
    echo "<p><a href='?step=3' class='btn btn-success'>➡️ الخطوة التالية</a></p>";
    
} elseif ($step === '3') {
    // Step 3: Add sample data
    echo "<h2>الخطوة 3: إضافة بيانات تجريبية</h2>";
    
    try {
        $config = include 'config/database.php';
        $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", 
                       $config['username'], $config['password']);
        
        // Check if sample data exists
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM movies");
        $movieCount = $stmt->fetch()['count'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM series");
        $seriesCount = $stmt->fetch()['count'];
        
        if ($movieCount > 0 || $seriesCount > 0) {
            echo "<div class='success'>✅ البيانات التجريبية موجودة</div>";
            echo "<div class='info'>";
            echo "الأفلام: $movieCount<br>";
            echo "المسلسلات: $seriesCount";
            echo "</div>";
        } else {
            echo "<div class='warning'>⚠️ لا توجد بيانات تجريبية</div>";
            echo "<p><a href='add_sample_data.php' class='btn btn-primary'>📊 إضافة بيانات تجريبية</a></p>";
        }
        
        echo "<p><a href='?step=4' class='btn btn-success'>➡️ الخطوة الأخيرة</a></p>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في فحص البيانات: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
} elseif ($step === '4') {
    // Step 4: Final check and links
    echo "<h2>الخطوة 4: الفحص النهائي والروابط</h2>";
    
    echo "<div class='success'>";
    echo "<h3>🎉 تم إصلاح جميع المشاكل!</h3>";
    echo "<p>النظام الآن جاهز للاستخدام</p>";
    echo "</div>";
    
    echo "<h3>🔗 الروابط المهمة:</h3>";
    echo "<div class='links'>";
    echo "<a href='index_simple.php' class='btn btn-primary'>🏠 الصفحة الرئيسية</a>";
    echo "<a href='admin/' class='btn btn-success'>🎛️ لوحة الإدارة</a>";
    echo "<a href='api/test_api.php' class='btn btn-info'>🔗 اختبار API</a>";
    echo "</div>";
    
    echo "<h3>📋 معلومات مهمة:</h3>";
    echo "<div class='info'>";
    echo "<strong>بيانات دخول الإدارة:</strong><br>";
    echo "اسم المستخدم: admin<br>";
    echo "كلمة المرور: admin123<br><br>";
    echo "<strong>بيانات المستخدمين التجريبيين:</strong><br>";
    echo "كلمة المرور: 123456<br>";
    echo "البريد: <EMAIL>, <EMAIL>, etc.";
    echo "</div>";
    
    echo "<h3>📚 التوثيق:</h3>";
    echo "<div class='links'>";
    echo "<a href='COMPLETE_FIX_SUMMARY.md' class='btn'>📄 ملخص الإصلاحات</a>";
    echo "<a href='ADMIN_PANEL_COMPLETE.md' class='btn'>📄 دليل لوحة الإدارة</a>";
    echo "<a href='API_FIX.md' class='btn'>📄 دليل API</a>";
    echo "</div>";
}

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 40px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1 {
    color: #E50914;
    border-bottom: 3px solid #E50914;
    padding-bottom: 10px;
    margin-bottom: 30px;
}

h2 {
    color: #333;
    margin-top: 30px;
    margin-bottom: 20px;
}

h3 {
    color: #555;
    margin-top: 20px;
    margin-bottom: 15px;
}

.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin: 15px 0;
}

.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin: 15px 0;
}

.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 5px;
    margin: 15px 0;
}

.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 5px;
    margin: 15px 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: all 0.3s;
}

.btn-primary {
    background: #E50914;
    color: white;
}

.btn-primary:hover {
    background: #b8070f;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.btn {
    background: #6c757d;
    color: white;
}

.btn:hover {
    background: #5a6268;
}

.links {
    margin: 20px 0;
}

ul {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}

p {
    margin: 10px 0;
}
</style>
