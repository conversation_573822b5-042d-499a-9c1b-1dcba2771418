<?php
/**
 * Test SQL Fix - Shahid Platform
 * Quick test to verify SQL syntax fixes
 */

echo "<h1>🔧 اختبار إصلاح SQL - Shahid Platform</h1>";

// Check if database config exists
if (!file_exists('config/database.php')) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "❌ ملف إعدادات قاعدة البيانات غير موجود";
    echo "</div>";
    exit();
}

try {
    $config = include 'config/database.php';
    $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", 
                   $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
    echo "✅ اتصال قاعدة البيانات ناجح";
    echo "</div>";
    
    // Test movies query
    echo "<h2>🎬 اختبار استعلام الأفلام:</h2>";
    
    try {
        $limit = 5;
        $offset = 0;
        $stmt = $pdo->prepare("SELECT * FROM movies WHERE status = 'published' ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $stmt->execute();
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
        echo "✅ استعلام الأفلام يعمل بشكل صحيح<br>";
        echo "عدد الأفلام المسترجعة: " . count($movies);
        echo "</div>";
        
        if (!empty($movies)) {
            echo "<h3>📋 عينة من الأفلام:</h3>";
            echo "<ul>";
            foreach (array_slice($movies, 0, 3) as $movie) {
                echo "<li><strong>" . htmlspecialchars($movie['title']) . "</strong>";
                if (isset($movie['year'])) echo " (" . $movie['year'] . ")";
                echo "</li>";
            }
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
        echo "❌ خطأ في استعلام الأفلام: " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
    
    // Test series query
    echo "<h2>📺 اختبار استعلام المسلسلات:</h2>";
    
    try {
        $limit = 5;
        $offset = 0;
        $stmt = $pdo->prepare("SELECT * FROM series WHERE status = 'published' ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $stmt->execute();
        $series = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
        echo "✅ استعلام المسلسلات يعمل بشكل صحيح<br>";
        echo "عدد المسلسلات المسترجعة: " . count($series);
        echo "</div>";
        
        if (!empty($series)) {
            echo "<h3>📋 عينة من المسلسلات:</h3>";
            echo "<ul>";
            foreach (array_slice($series, 0, 3) as $show) {
                echo "<li><strong>" . htmlspecialchars($show['title']) . "</strong>";
                if (isset($show['year'])) echo " (" . $show['year'] . ")";
                if (isset($show['seasons'])) echo " - " . $show['seasons'] . " مواسم";
                echo "</li>";
            }
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
        echo "❌ خطأ في استعلام المسلسلات: " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
    
    // Test API endpoints
    echo "<h2>🔗 اختبار API Endpoints:</h2>";
    
    $endpoints = [
        'status' => 'حالة النظام',
        'movies' => 'قائمة الأفلام',
        'series' => 'قائمة المسلسلات'
    ];
    
    foreach ($endpoints as $endpoint => $description) {
        $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/' . $endpoint;
        
        try {
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'timeout' => 5,
                    'header' => 'Content-Type: application/json'
                ]
            ]);
            
            $response = @file_get_contents($url, false, $context);
            if ($response) {
                $data = json_decode($response, true);
                if ($data && isset($data['success']) && $data['success']) {
                    echo "<p>✅ <strong>$description</strong>: <a href='$url' target='_blank'>$endpoint</a></p>";
                } else {
                    echo "<p>⚠️ <strong>$description</strong>: استجابة غير متوقعة</p>";
                }
            } else {
                echo "<p>❌ <strong>$description</strong>: لا يمكن الوصول</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ <strong>$description</strong>: خطأ في الاختبار</p>";
        }
    }
    
    echo "<h2>🎯 النتيجة:</h2>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
    echo "<h3>✅ تم إصلاح مشكلة SQL بنجاح!</h3>";
    echo "<p>جميع الاستعلامات تعمل الآن بشكل صحيح مع MariaDB</p>";
    echo "</div>";
    
    echo "<h3>🔗 اختبر الآن:</h3>";
    echo "<p><a href='api/test_api.php' target='_blank' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧪 اختبار API كامل</a></p>";
    echo "<p><a href='index_simple.php' target='_blank' style='background: #E50914; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 الصفحة الرئيسية</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات:</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<h4>🔧 الحلول المقترحة:</h4>";
    echo "<ul>";
    echo "<li>تأكد من إنشاء قاعدة البيانات: <a href='create_database.php'>create_database.php</a></li>";
    echo "<li>تحقق من بيانات الاتصال في config/database.php</li>";
    echo "<li>تأكد من تشغيل خادم MySQL/MariaDB</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f8f9fa; }
h1 { color: #E50914; border-bottom: 3px solid #E50914; padding-bottom: 10px; }
h2 { color: #333; margin-top: 30px; }
h3 { color: #555; }
p { margin: 10px 0; }
ul { margin: 10px 0; padding-left: 20px; }
li { margin: 5px 0; }
a { color: #E50914; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
