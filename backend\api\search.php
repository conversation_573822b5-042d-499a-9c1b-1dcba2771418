<?php
/**
 * Shahid Search API
 * Professional Video Streaming Platform
 */

require_once '../core/Database.php';
require_once '../core/Security.php';
require_once '../core/ApiController.php';
require_once '../models/Movie.php';
require_once '../models/Series.php';

class SearchAPI extends ApiController {
    
    public function handleRequest() {
        $route = trim($_SERVER['REQUEST_URI'], '/');
        $segments = explode('/', $route);
        
        // Remove 'api/search' from segments
        $segments = array_slice($segments, 2);
        
        $action = $segments[0] ?? null;
        
        switch ($action) {
            case null:
            case '':
                $this->search();
                break;
            case 'suggestions':
                $this->getSearchSuggestions();
                break;
            case 'trending':
                $this->getTrendingSearches();
                break;
            case 'filters':
                $this->getSearchFilters();
                break;
            case 'advanced':
                $this->advancedSearch();
                break;
            default:
                $this->sendError('Invalid search endpoint', 404);
        }
    }
    
    private function search() {
        if (!$this->validateMethod(['GET'])) return;
        
        $query = $_GET['q'] ?? '';
        $type = $_GET['type'] ?? 'all'; // all, movies, series
        $pagination = $this->getPaginationParams();
        
        if (empty($query)) {
            $this->sendError('Search query is required', 400);
            return;
        }
        
        if (strlen($query) < 2) {
            $this->sendError('Search query must be at least 2 characters', 400);
            return;
        }
        
        // Log search query
        $this->logSearchQuery($query, $type);
        
        $results = [];
        $totalResults = 0;
        
        if ($type === 'all' || $type === 'movies') {
            $movieModel = new Movie();
            $movies = $movieModel->searchMovies($query, $pagination['page'], $pagination['limit']);
            $results['movies'] = $movies;
            $totalResults += count($movies);
        }
        
        if ($type === 'all' || $type === 'series') {
            $seriesModel = new Series();
            $series = $seriesModel->searchSeries($query, $pagination['page'], $pagination['limit']);
            $results['series'] = $series;
            $totalResults += count($series);
        }
        
        // Get total count for pagination
        $total = $this->getSearchResultsCount($query, $type);
        
        $response = [
            'success' => true,
            'data' => $results,
            'query' => $query,
            'type' => $type,
            'total_results' => $totalResults,
            'pagination' => [
                'current_page' => $pagination['page'],
                'per_page' => $pagination['limit'],
                'total' => $total,
                'total_pages' => ceil($total / $pagination['limit'])
            ]
        ];
        
        $this->sendResponse($response);
    }
    
    private function getSearchSuggestions() {
        if (!$this->validateMethod(['GET'])) return;
        
        $query = $_GET['q'] ?? '';
        $limit = min(10, intval($_GET['limit'] ?? 5));
        
        if (empty($query) || strlen($query) < 2) {
            $this->sendSuccess([]);
            return;
        }
        
        $suggestions = [];
        
        // Get movie suggestions
        $movieModel = new Movie();
        $movieSuggestions = $movieModel->getSearchSuggestions($query, $limit);
        
        // Get series suggestions
        $seriesModel = new Series();
        $seriesSuggestions = $seriesModel->getSearchSuggestions($query, $limit);
        
        // Combine and limit results
        $allSuggestions = array_merge($movieSuggestions, $seriesSuggestions);
        
        // Sort by relevance (you can implement a scoring algorithm)
        usort($allSuggestions, function($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });
        
        $suggestions = array_slice($allSuggestions, 0, $limit);
        
        $this->sendSuccess($suggestions);
    }
    
    private function getTrendingSearches() {
        if (!$this->validateMethod(['GET'])) return;
        
        $limit = min(20, intval($_GET['limit'] ?? 10));
        $period = $_GET['period'] ?? 'week'; // day, week, month
        
        $trending = $this->getTrendingQueries($period, $limit);
        
        $this->sendSuccess($trending);
    }
    
    private function getSearchFilters() {
        if (!$this->validateMethod(['GET'])) return;
        
        $movieModel = new Movie();
        $seriesModel = new Series();
        
        $filters = [
            'genres' => [
                'movies' => $movieModel->getAllGenres(),
                'series' => $seriesModel->getAllGenres()
            ],
            'years' => $this->getAvailableYears(),
            'countries' => $this->getAvailableCountries(),
            'languages' => $this->getAvailableLanguages(),
            'ratings' => [
                ['value' => '9+', 'label' => '9.0+'],
                ['value' => '8+', 'label' => '8.0+'],
                ['value' => '7+', 'label' => '7.0+'],
                ['value' => '6+', 'label' => '6.0+']
            ],
            'quality' => [
                ['value' => '4K', 'label' => '4K Ultra HD'],
                ['value' => '1080p', 'label' => 'Full HD 1080p'],
                ['value' => '720p', 'label' => 'HD 720p'],
                ['value' => '480p', 'label' => 'SD 480p']
            ]
        ];
        
        $this->sendSuccess($filters);
    }
    
    private function advancedSearch() {
        if (!$this->validateMethod(['GET'])) return;
        
        $filters = [
            'query' => $_GET['q'] ?? '',
            'type' => $_GET['type'] ?? 'all',
            'genre' => $_GET['genre'] ?? '',
            'year' => $_GET['year'] ?? '',
            'country' => $_GET['country'] ?? '',
            'language' => $_GET['language'] ?? '',
            'rating_min' => floatval($_GET['rating_min'] ?? 0),
            'rating_max' => floatval($_GET['rating_max'] ?? 10),
            'duration_min' => intval($_GET['duration_min'] ?? 0),
            'duration_max' => intval($_GET['duration_max'] ?? 0),
            'quality' => $_GET['quality'] ?? '',
            'premium' => $_GET['premium'] ?? null,
            'sort' => $_GET['sort'] ?? 'relevance'
        ];
        
        $pagination = $this->getPaginationParams();
        
        $results = [];
        $total = 0;
        
        if ($filters['type'] === 'all' || $filters['type'] === 'movies') {
            $movieModel = new Movie();
            $movies = $movieModel->advancedSearch($filters, $pagination['page'], $pagination['limit']);
            $results['movies'] = $movies;
            
            if ($filters['type'] === 'movies') {
                $total = $movieModel->countAdvancedSearch($filters);
            }
        }
        
        if ($filters['type'] === 'all' || $filters['type'] === 'series') {
            $seriesModel = new Series();
            $series = $seriesModel->advancedSearch($filters, $pagination['page'], $pagination['limit']);
            $results['series'] = $series;
            
            if ($filters['type'] === 'series') {
                $total = $seriesModel->countAdvancedSearch($filters);
            }
        }
        
        if ($filters['type'] === 'all') {
            $total = count($results['movies'] ?? []) + count($results['series'] ?? []);
        }
        
        // Log advanced search
        if (!empty($filters['query'])) {
            $this->logSearchQuery($filters['query'], $filters['type'], $filters);
        }
        
        $this->sendPaginatedResponse($results, $total, $pagination['page'], $pagination['limit']);
    }
    
    private function getSearchResultsCount($query, $type) {
        $total = 0;
        
        if ($type === 'all' || $type === 'movies') {
            $movieModel = new Movie();
            $total += $movieModel->countSearchResults($query);
        }
        
        if ($type === 'all' || $type === 'series') {
            $seriesModel = new Series();
            $total += $seriesModel->countSearchResults($query);
        }
        
        return $total;
    }
    
    private function logSearchQuery($query, $type, $filters = []) {
        try {
            $user = $this->authenticateUser(false);
            $userId = $user ? $user['id'] : null;
            
            $sql = "INSERT INTO search_logs (user_id, query, type, filters, ip_address, user_agent, created_at) 
                    VALUES (:user_id, :query, :type, :filters, :ip_address, :user_agent, NOW())";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':user_id' => $userId,
                ':query' => $query,
                ':type' => $type,
                ':filters' => json_encode($filters),
                ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
        } catch (Exception $e) {
            // Log error but don't fail the search
            error_log('Failed to log search query: ' . $e->getMessage());
        }
    }
    
    private function getTrendingQueries($period, $limit) {
        $interval = match($period) {
            'day' => '1 DAY',
            'week' => '7 DAY',
            'month' => '30 DAY',
            default => '7 DAY'
        };
        
        try {
            $sql = "SELECT query, COUNT(*) as search_count
                    FROM search_logs 
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL {$interval})
                    AND query != ''
                    GROUP BY query
                    ORDER BY search_count DESC, query ASC
                    LIMIT :limit";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log('Failed to get trending searches: ' . $e->getMessage());
            return [];
        }
    }
    
    private function getAvailableYears() {
        try {
            $sql = "SELECT DISTINCT year 
                    FROM (
                        SELECT year FROM movies WHERE year IS NOT NULL AND year > 0
                        UNION
                        SELECT year FROM series WHERE year IS NOT NULL AND year > 0
                    ) as years
                    ORDER BY year DESC
                    LIMIT 50";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            
            $years = $stmt->fetchAll(PDO::FETCH_COLUMN);
            return array_map('intval', $years);
        } catch (Exception $e) {
            error_log('Failed to get available years: ' . $e->getMessage());
            return [];
        }
    }
    
    private function getAvailableCountries() {
        try {
            $sql = "SELECT DISTINCT country 
                    FROM (
                        SELECT country FROM movies WHERE country IS NOT NULL AND country != ''
                        UNION
                        SELECT country FROM series WHERE country IS NOT NULL AND country != ''
                    ) as countries
                    ORDER BY country ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (Exception $e) {
            error_log('Failed to get available countries: ' . $e->getMessage());
            return [];
        }
    }
    
    private function getAvailableLanguages() {
        try {
            $sql = "SELECT DISTINCT language 
                    FROM (
                        SELECT language FROM movies WHERE language IS NOT NULL AND language != ''
                        UNION
                        SELECT language FROM series WHERE language IS NOT NULL AND language != ''
                    ) as languages
                    ORDER BY language ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (Exception $e) {
            error_log('Failed to get available languages: ' . $e->getMessage());
            return [];
        }
    }
}

// Handle the request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$api = new SearchAPI();
$api->handleRequest();
?>
