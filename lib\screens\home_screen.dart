import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../main.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _isLoading = true;
  String? _error;
  List<Movie> _featuredMovies = [];
  List<Series> _featuredSeries = [];
  Map<String, dynamic>? _apiStatus;

  @override
  void initState() {
    super.initState();
    _loadHomeData();
  }

  Future<void> _loadHomeData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load API status
      final statusResponse = await ApiService.getStatus();
      
      // Load featured movies
      final moviesResponse = await ApiService.getMovies();
      final movies = (moviesResponse['data'] as List?)
          ?.map((json) => Movie.fromJson(json))
          .take(5) // Take first 5 for featured
          .toList() ?? [];

      // Load featured series
      final seriesResponse = await ApiService.getSeries();
      final series = (seriesResponse['data'] as List?)
          ?.map((json) => Series.fromJson(json))
          .take(5) // Take first 5 for featured
          .toList() ?? [];

      setState(() {
        _apiStatus = statusResponse;
        _featuredMovies = movies;
        _featuredSeries = series;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const ShahidAppBar(title: 'الرئيسية'),
      body: _isLoading
          ? const LoadingWidget(message: 'جاري تحميل المحتوى...')
          : _error != null
              ? ErrorWidget(
                  message: _error!,
                  onRetry: _loadHomeData,
                )
              : RefreshIndicator(
                  onRefresh: _loadHomeData,
                  color: const Color(0xFFE50914),
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Hero Section
                        _buildHeroSection(),
                        
                        const SizedBox(height: 24),
                        
                        // Featured Movies Section
                        if (_featuredMovies.isNotEmpty) ...[
                          _buildSectionTitle('الأفلام المميزة'),
                          const SizedBox(height: 12),
                          _buildMoviesRow(_featuredMovies),
                          const SizedBox(height: 24),
                        ],
                        
                        // Featured Series Section
                        if (_featuredSeries.isNotEmpty) ...[
                          _buildSectionTitle('المسلسلات المميزة'),
                          const SizedBox(height: 12),
                          _buildSeriesRow(_featuredSeries),
                          const SizedBox(height: 24),
                        ],
                        
                        // API Status Section (for debugging)
                        if (_apiStatus != null) ...[
                          _buildSectionTitle('حالة النظام'),
                          const SizedBox(height: 12),
                          _buildStatusCard(),
                          const SizedBox(height: 24),
                        ],
                      ],
                    ),
                  ),
                ),
    );
  }

  Widget _buildHeroSection() {
    return Container(
      height: 200,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFE50914),
            Color(0xFF8B0000),
          ],
        ),
      ),
      child: Stack(
        children: [
          Positioned(
            right: 20,
            top: 20,
            child: Icon(
              Icons.play_circle_filled,
              size: 60,
              color: Colors.white.withOpacity(0.3),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  'مرحباً بك في Shahid',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'منصة البث الاحترافية',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    // Navigate to featured content
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFFE50914),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  child: const Text(
                    'استكشف المحتوى',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildMoviesRow(List<Movie> movies) {
    return SizedBox(
      height: 220,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: movies.length,
        itemBuilder: (context, index) {
          final movie = movies[index];
          return ContentCard(
            title: movie.title,
            subtitle: '${movie.year} • ${movie.genre}',
            imageUrl: movie.poster,
            rating: movie.rating,
            onTap: () {
              _showMovieDetails(movie);
            },
          );
        },
      ),
    );
  }

  Widget _buildSeriesRow(List<Series> series) {
    return SizedBox(
      height: 220,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: series.length,
        itemBuilder: (context, index) {
          final serie = series[index];
          return ContentCard(
            title: serie.title,
            subtitle: '${serie.year} • ${serie.seasons} مواسم',
            imageUrl: serie.poster,
            rating: serie.rating,
            onTap: () {
              _showSeriesDetails(serie);
            },
          );
        },
      ),
    );
  }

  Widget _buildStatusCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF2F2F2F),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _apiStatus!['success'] ? Icons.check_circle : Icons.error,
                color: _apiStatus!['success'] ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 8),
              Text(
                _apiStatus!['success'] ? 'النظام يعمل بشكل طبيعي' : 'مشكلة في النظام',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          if (_apiStatus!['data'] != null) ...[
            const SizedBox(height: 8),
            Text(
              'الأفلام: ${_apiStatus!['data']['database']?['movies'] ?? 0}',
              style: const TextStyle(color: Colors.grey),
            ),
            Text(
              'المسلسلات: ${_apiStatus!['data']['database']?['series'] ?? 0}',
              style: const TextStyle(color: Colors.grey),
            ),
          ],
        ],
      ),
    );
  }

  void _showMovieDetails(Movie movie) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF2F2F2F),
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle bar
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                // Movie title
                Text(
                  movie.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                
                // Movie info
                Row(
                  children: [
                    Text(
                      '${movie.year}',
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      movie.duration,
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(width: 16),
                    Row(
                      children: [
                        const Icon(Icons.star, color: Colors.amber, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          movie.rating.toStringAsFixed(1),
                          style: const TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Description
                Text(
                  movie.description,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Additional info
                if (movie.director != null) ...[
                  Text(
                    'الإخراج: ${movie.director}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 8),
                ],
                if (movie.cast != null) ...[
                  Text(
                    'التمثيل: ${movie.cast}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 8),
                ],
                Text(
                  'النوع: ${movie.genre}',
                  style: const TextStyle(color: Colors.grey),
                ),
                
                const SizedBox(height: 24),
                
                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          // Play movie
                        },
                        icon: const Icon(Icons.play_arrow),
                        label: const Text('تشغيل'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFE50914),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          // Add to favorites
                        },
                        icon: const Icon(Icons.favorite_border),
                        label: const Text('إضافة للمفضلة'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.white,
                          side: const BorderSide(color: Colors.white),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showSeriesDetails(Series series) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF2F2F2F),
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle bar
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                // Series title
                Text(
                  series.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                
                // Series info
                Row(
                  children: [
                    Text(
                      '${series.year}',
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '${series.seasons} مواسم',
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '${series.episodes} حلقة',
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(width: 16),
                    Row(
                      children: [
                        const Icon(Icons.star, color: Colors.amber, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          series.rating.toStringAsFixed(1),
                          style: const TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Description
                Text(
                  series.description,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Additional info
                if (series.director != null) ...[
                  Text(
                    'الإخراج: ${series.director}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 8),
                ],
                if (series.cast != null) ...[
                  Text(
                    'التمثيل: ${series.cast}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 8),
                ],
                Text(
                  'النوع: ${series.genre}',
                  style: const TextStyle(color: Colors.grey),
                ),
                
                const SizedBox(height: 24),
                
                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          // View episodes
                        },
                        icon: const Icon(Icons.play_arrow),
                        label: const Text('مشاهدة الحلقات'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFE50914),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          // Add to favorites
                        },
                        icon: const Icon(Icons.favorite_border),
                        label: const Text('إضافة للمفضلة'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.white,
                          side: const BorderSide(color: Colors.white),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
