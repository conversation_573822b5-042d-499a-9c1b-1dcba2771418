/* Shahid Authentication Pages Stylesheet */

:root {
    --auth-primary: #e50914;
    --auth-secondary: #221f1f;
    --auth-dark: #141414;
    --auth-light: #f8f9fa;
    --auth-white: #ffffff;
    --auth-gray: #6c757d;
    --auth-border: #dee2e6;
    --auth-success: #28a745;
    --auth-danger: #dc3545;
    --auth-warning: #ffc107;
    --auth-info: #17a2b8;
    --auth-gradient: linear-gradient(135deg, #e50914 0%, #b20710 100%);
    --auth-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --auth-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --auth-transition: all 0.3s ease;
    --auth-border-radius: 0.5rem;
}

/* Global Auth Styles */
.auth-body {
    font-family: 'Cairo', sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
    background: var(--auth-dark);
}

/* Background */
.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.auth-background video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.auth-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        45deg,
        rgba(20, 20, 20, 0.9) 0%,
        rgba(20, 20, 20, 0.7) 50%,
        rgba(20, 20, 20, 0.9) 100%
    );
    z-index: -1;
}

/* Navigation */
.auth-navbar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 1rem 0;
    background: rgba(20, 20, 20, 0.3);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    text-decoration: none;
}

.navbar-brand img {
    filter: brightness(1.1);
    transition: var(--auth-transition);
}

.navbar-brand:hover img {
    filter: brightness(1.3);
}

/* Auth Container */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
    position: relative;
    z-index: 1;
}

/* Auth Card */
.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--auth-border-radius);
    box-shadow: var(--auth-shadow-lg);
    padding: 2.5rem;
    width: 100%;
    max-width: 450px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideUp 0.6s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Auth Header */
.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h2 {
    color: var(--auth-dark);
    font-weight: 700;
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: var(--auth-gray);
    font-size: 1rem;
    margin-bottom: 0;
}

/* Form Styles */
.auth-form {
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--auth-dark);
    margin-bottom: 0.5rem;
    display: block;
}

.input-group {
    position: relative;
}

.input-group-text {
    background: var(--auth-light);
    border: 1px solid var(--auth-border);
    border-left: none;
    color: var(--auth-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    border-radius: var(--auth-border-radius) 0 0 var(--auth-border-radius);
}

.form-control {
    border: 1px solid var(--auth-border);
    border-radius: 0 var(--auth-border-radius) var(--auth-border-radius) 0;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: var(--auth-transition);
    background: var(--auth-white);
}

.form-control:focus {
    border-color: var(--auth-primary);
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    outline: none;
}

.form-control.is-invalid {
    border-color: var(--auth-danger);
}

.form-control.is-valid {
    border-color: var(--auth-success);
}

.toggle-password {
    border: 1px solid var(--auth-border);
    border-right: none;
    background: var(--auth-light);
    color: var(--auth-gray);
    border-radius: 0 var(--auth-border-radius) var(--auth-border-radius) 0;
    padding: 0.75rem;
    cursor: pointer;
    transition: var(--auth-transition);
}

.toggle-password:hover {
    background: var(--auth-border);
    color: var(--auth-dark);
}

.form-text {
    font-size: 0.875rem;
    color: var(--auth-gray);
    margin-top: 0.25rem;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--auth-danger);
}

/* Password Strength */
.password-strength {
    margin-top: 0.5rem;
}

.strength-bar {
    height: 4px;
    background: var(--auth-border);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: var(--auth-transition);
    border-radius: 2px;
}

.strength-text {
    font-size: 0.75rem;
    color: var(--auth-gray);
}

/* Checkboxes */
.form-check {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.form-check-input {
    margin: 0;
    width: 1.2rem;
    height: 1.2rem;
    border: 2px solid var(--auth-border);
    border-radius: 0.25rem;
    background: var(--auth-white);
    transition: var(--auth-transition);
}

.form-check-input:checked {
    background: var(--auth-primary);
    border-color: var(--auth-primary);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
}

.form-check-label {
    font-size: 0.9rem;
    color: var(--auth-gray);
    line-height: 1.4;
    cursor: pointer;
}

.form-check-label a {
    color: var(--auth-primary);
    text-decoration: none;
    font-weight: 600;
}

.form-check-label a:hover {
    text-decoration: underline;
}

/* Buttons */
.btn {
    border-radius: var(--auth-border-radius);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    transition: var(--auth-transition);
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-primary {
    background: var(--auth-gradient);
    color: var(--auth-white);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #b20710 0%, #8a0508 100%);
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(229, 9, 20, 0.3);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Social Login */
.social-login {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.btn-social {
    background: var(--auth-white);
    color: var(--auth-dark);
    border: 2px solid var(--auth-border);
    font-weight: 600;
    transition: var(--auth-transition);
}

.btn-social:hover {
    transform: translateY(-2px);
    box-shadow: var(--auth-shadow);
}

.btn-google:hover {
    border-color: #db4437;
    color: #db4437;
}

.btn-facebook:hover {
    border-color: #4267B2;
    color: #4267B2;
}

/* Auth Divider */
.auth-divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--auth-border);
}

.auth-divider span {
    background: rgba(255, 255, 255, 0.95);
    padding: 0 1rem;
    color: var(--auth-gray);
    font-size: 0.9rem;
    position: relative;
    z-index: 1;
}

/* Auth Footer */
.auth-footer {
    text-align: center;
}

.auth-footer a {
    color: var(--auth-primary);
    text-decoration: none;
    font-weight: 600;
    transition: var(--auth-transition);
}

.auth-footer a:hover {
    text-decoration: underline;
    color: #b20710;
}

.forgot-link {
    font-size: 0.9rem;
    color: var(--auth-gray) !important;
}

.forgot-link:hover {
    color: var(--auth-primary) !important;
}

.register-link,
.login-link {
    margin-right: 0.5rem;
}

/* Alerts */
.alert {
    border-radius: var(--auth-border-radius);
    border: none;
    padding: 1rem;
    margin-bottom: 1.5rem;
    animation: fadeIn 0.3s ease;
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--auth-danger);
    border-right: 4px solid var(--auth-danger);
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--auth-success);
    border-right: 4px solid var(--auth-success);
}

.alert ul {
    margin: 0;
    padding-right: 1rem;
}

.alert li {
    margin-bottom: 0.25rem;
}

.alert li:last-child {
    margin-bottom: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-container {
        padding: 1rem;
    }
    
    .auth-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
    }
    
    .auth-header h2 {
        font-size: 1.75rem;
    }
    
    .social-login {
        gap: 0.5rem;
    }
    
    .btn-social {
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
}

@media (max-width: 480px) {
    .auth-card {
        padding: 1.5rem 1rem;
    }
    
    .auth-header h2 {
        font-size: 1.5rem;
    }
    
    .form-control {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Loading States */
.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Focus States for Accessibility */
.form-control:focus,
.btn:focus,
.form-check-input:focus {
    outline: 2px solid var(--auth-primary);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .auth-card {
        background: var(--auth-white);
        border: 2px solid var(--auth-dark);
    }
    
    .form-control {
        border: 2px solid var(--auth-dark);
    }
    
    .btn-primary {
        background: var(--auth-dark);
        border: 2px solid var(--auth-dark);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
