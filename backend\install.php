<?php
/**
 * Shahid - Installation Script
 * Professional Video Streaming Platform
 */

session_start();

// Check if already installed
if (file_exists('config/installed.lock')) {
    die('Application is already installed. Delete config/installed.lock to reinstall.');
}

$step = $_GET['step'] ?? 1;

// Auto-advance if previous steps are completed
if ($step == 1 && file_exists('config/database.php')) {
    $step = 3; // Skip to table creation if DB config exists
}
if ($step <= 3 && file_exists('config/tables_created.lock')) {
    $step = 4; // Skip to admin creation if tables exist
}
if ($step <= 4 && file_exists('config/admin_created.lock')) {
    $step = 5; // Skip to final config if admin exists
}

$errors = [];
$success = [];

// Handle form submissions
if ($_POST) {
    switch ($step) {
        case 2:
            // Database configuration - only process if config doesn't exist
            if (file_exists('config/database.php')) {
                header('Location: ?step=3');
                exit;
            }

            $dbHost = $_POST['db_host'] ?? '';
            $dbName = $_POST['db_name'] ?? '';
            $dbUser = $_POST['db_user'] ?? '';
            $dbPass = $_POST['db_pass'] ?? '';
            
            // Test database connection
            try {
                $dsn = "mysql:host=$dbHost;charset=utf8mb4";
                $pdo = new PDO($dsn, $dbUser, $dbPass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Create database if not exists
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                
                // Store database config in session
                $_SESSION['db_config'] = [
                    'host' => $dbHost,
                    'name' => $dbName,
                    'username' => $dbUser,
                    'password' => $dbPass
                ];

                // Create config directory if it doesn't exist
                if (!is_dir('config')) {
                    mkdir('config', 0755, true);
                }

                // Create config file
                $configContent = "<?php\nreturn [\n    'host' => '$dbHost',\n    'name' => '$dbName',\n    'username' => '$dbUser',\n    'password' => '$dbPass'\n];";

                if (file_put_contents('config/database.php', $configContent)) {
                    $success[] = 'Database connection successful!';
                    // Redirect to next step to avoid form resubmission
                    header('Location: ?step=3');
                    exit;
                } else {
                    $errors[] = 'Failed to create database configuration file. Please check write permissions.';
                }
            } catch (PDOException $e) {
                $errors[] = 'Database connection failed: ' . $e->getMessage();
            }
            break;
            
        case 3:
            // Create tables - only if not already created
            if (file_exists('config/tables_created.lock')) {
                header('Location: ?step=4');
                exit;
            }
            // Read database config from file
            if (!file_exists('config/database.php')) {
                $errors[] = 'Database configuration not found. Please complete step 2 first.';
            } else {
                try {
                    $config = include 'config/database.php';

                    // Validate config
                    if (!isset($config['host']) || !isset($config['name']) || !isset($config['username'])) {
                        $errors[] = 'Invalid database configuration. Please repeat step 2.';
                    } else {
                        $dsn = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
                        $pdo = new PDO($dsn, $config['username'], $config['password']);
                        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                        // Read and execute SQL file
                        $sql = file_get_contents('database/schema.sql');
                        $pdo->exec($sql);

                        // Create lock file to prevent re-creation
                        file_put_contents('config/tables_created.lock', date('Y-m-d H:i:s'));

                        $success[] = 'Database tables created successfully!';
                        // Redirect to next step
                        header('Location: ?step=4');
                        exit;
                    }
                } catch (Exception $e) {
                    $errors[] = 'Failed to create tables: ' . $e->getMessage();
                }
            }
            break;
            
        case 4:
            // Admin account creation - only if not already created
            if (file_exists('config/admin_created.lock')) {
                header('Location: ?step=5');
                exit;
            }
            $adminName = $_POST['admin_name'] ?? '';
            $adminEmail = $_POST['admin_email'] ?? '';
            $adminPassword = $_POST['admin_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
            
            // Validation
            if (empty($adminName)) $errors[] = 'Admin name is required';
            if (empty($adminEmail) || !filter_var($adminEmail, FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Valid admin email is required';
            }
            if (empty($adminPassword) || strlen($adminPassword) < 8) {
                $errors[] = 'Admin password must be at least 8 characters';
            }
            if ($adminPassword !== $confirmPassword) {
                $errors[] = 'Passwords do not match';
            }
            
            if (empty($errors)) {
                try {
                    // Read database config from file
                    if (!file_exists('config/database.php')) {
                        $errors[] = 'Database configuration not found. Please complete step 2 first.';
                        break;
                    }
                    $config = include 'config/database.php';
                    $dsn = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
                    $pdo = new PDO($dsn, $config['username'], $config['password']);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // Create admin user
                    $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, status, created_at) VALUES (?, ?, ?, 'admin', 'active', NOW())");
                    $stmt->execute([$adminName, $adminEmail, $hashedPassword]);
                    
                    // Create lock file to prevent re-creation
                    file_put_contents('config/admin_created.lock', date('Y-m-d H:i:s'));

                    $success[] = 'Admin account created successfully!';
                    // Redirect to next step
                    header('Location: ?step=5');
                    exit;
                } catch (Exception $e) {
                    $errors[] = 'Failed to create admin account: ' . $e->getMessage();
                }
            }
            break;
            
        case 5:
            // Site configuration
            $siteName = $_POST['site_name'] ?? 'Shahid';
            $siteUrl = $_POST['site_url'] ?? '';
            $siteDescription = $_POST['site_description'] ?? '';
            
            // Payment configuration
            $stripePublicKey = $_POST['stripe_public_key'] ?? '';
            $stripeSecretKey = $_POST['stripe_secret_key'] ?? '';
            $paypalClientId = $_POST['paypal_client_id'] ?? '';
            $paypalClientSecret = $_POST['paypal_client_secret'] ?? '';
            
            // SEO configuration
            $googleAnalyticsId = $_POST['google_analytics_id'] ?? '';
            $googleSearchConsole = $_POST['google_search_console'] ?? '';
            
            try {
                // Create config file
                $configContent = generateConfigFile([
                    'database' => $_SESSION['db_config'],
                    'site' => [
                        'name' => $siteName,
                        'url' => $siteUrl,
                        'description' => $siteDescription
                    ],
                    'payment' => [
                        'stripe_public_key' => $stripePublicKey,
                        'stripe_secret_key' => $stripeSecretKey,
                        'paypal_client_id' => $paypalClientId,
                        'paypal_client_secret' => $paypalClientSecret
                    ],
                    'seo' => [
                        'google_analytics_id' => $googleAnalyticsId,
                        'google_search_console_code' => $googleSearchConsole
                    ]
                ]);
                
                file_put_contents('config/config.php', $configContent);
                
                // Create installation lock file
                file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
                
                // Clear session
                unset($_SESSION['db_config']);
                
                $success[] = 'Installation completed successfully!';
                // Redirect to final step
                header('Location: ?step=6');
                exit;
            } catch (Exception $e) {
                $errors[] = 'Failed to save configuration: ' . $e->getMessage();
            }
            break;
    }
}

function generateConfigFile($config) {
    return "<?php
/**
 * Shahid - Configuration File
 * Generated by installation script
 */

return [
    // Database Configuration
    'database' => [
        'host' => '{$config['database']['host']}',
        'name' => '{$config['database']['name']}',
        'username' => '{$config['database']['username']}',
        'password' => '{$config['database']['password']}',
        'charset' => 'utf8mb4'
    ],

    // Site Configuration
    'site' => [
        'name' => '{$config['site']['name']}',
        'url' => '{$config['site']['url']}',
        'description' => '{$config['site']['description']}',
        'keywords' => 'movies, series, streaming, video, entertainment',
        'admin_email' => '<EMAIL>',
        'timezone' => 'Asia/Riyadh'
    ],

    // Security Configuration
    'security' => [
        'jwt_secret' => '" . bin2hex(random_bytes(32)) . "',
        'csrf_token_name' => 'csrf_token',
        'session_lifetime' => 3600,
        'password_min_length' => 8,
        'max_login_attempts' => 5,
        'lockout_duration' => 900
    ],

    // Payment Configuration
    'payment' => [
        'stripe' => [
            'public_key' => '{$config['payment']['stripe_public_key']}',
            'secret_key' => '{$config['payment']['stripe_secret_key']}',
            'webhook_secret' => ''
        ],
        'paypal' => [
            'client_id' => '{$config['payment']['paypal_client_id']}',
            'client_secret' => '{$config['payment']['paypal_client_secret']}',
            'mode' => 'sandbox'
        ]
    ],

    // Video Configuration
    'video' => [
        'upload_path' => 'uploads/videos/',
        'subtitle_path' => 'uploads/subtitles/',
        'thumbnail_path' => 'uploads/thumbnails/',
        'max_file_size' => 2147483648,
        'allowed_formats' => ['mp4', 'mkv', 'avi', 'mov'],
        'quality_levels' => ['360p', '480p', '720p', '1080p', 'auto']
    ],

    // API Configuration
    'api' => [
        'version' => 'v1',
        'rate_limit' => 100,
        'pagination_limit' => 20
    ],

    // SEO Configuration
    'seo' => [
        'google_analytics_id' => '{$config['seo']['google_analytics_id']}',
        'google_search_console_code' => '{$config['seo']['google_search_console_code']}',
        'facebook_app_id' => '',
        'twitter_site' => '@shahid'
    ],

    // Email Configuration
    'email' => [
        'smtp_host' => 'smtp.gmail.com',
        'smtp_port' => 587,
        'smtp_username' => '',
        'smtp_password' => '',
        'from_email' => '<EMAIL>',
        'from_name' => 'Shahid Platform'
    ],

    // Firebase Configuration
    'firebase' => [
        'server_key' => '',
        'sender_id' => ''
    ]
];
?>";
}

function checkRequirements() {
    $requirements = [
        'PHP Version >= 8.0' => version_compare(PHP_VERSION, '8.0.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'OpenSSL Extension' => extension_loaded('openssl'),
        'JSON Extension' => extension_loaded('json'),
        'Config Directory Writable' => is_writable('config/'),
        'Uploads Directory Writable' => is_writable('../uploads/') || mkdir('../uploads/', 0755, true)
    ];
    
    return $requirements;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shahid - Installation</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .container { max-width: 800px; margin: 50px auto; background: white; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { background: #e50914; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { padding: 30px; }
        .step { background: #f8f9fa; padding: 15px; margin-bottom: 20px; border-radius: 5px; border-left: 4px solid #e50914; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { background: #e50914; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .btn:hover { background: #d40813; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
        .requirements { list-style: none; }
        .requirements li { padding: 5px 0; }
        .requirements .pass { color: #28a745; }
        .requirements .fail { color: #dc3545; }
        .progress { background: #e9ecef; height: 10px; border-radius: 5px; margin-bottom: 20px; }
        .progress-bar { background: #e50914; height: 100%; border-radius: 5px; transition: width 0.3s; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 Shahid Installation</h1>
            <p>Professional Video Streaming Platform</p>
        </div>
        
        <div class="content">
            <div class="progress">
                <div class="progress-bar" style="width: <?= ($step / 6) * 100 ?>%"></div>
            </div>
            
            <?php if (!empty($errors)): ?>
                <div class="error">
                    <?php foreach ($errors as $error): ?>
                        <p><?= htmlspecialchars($error) ?></p>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="success">
                    <?php foreach ($success as $msg): ?>
                        <p><?= htmlspecialchars($msg) ?></p>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <div class="step">
                    <h2>Step 1: System Requirements</h2>
                    <p>Checking system requirements...</p>
                </div>
                
                <ul class="requirements">
                    <?php foreach (checkRequirements() as $requirement => $status): ?>
                        <li class="<?= $status ? 'pass' : 'fail' ?>">
                            <?= $status ? '✓' : '✗' ?> <?= $requirement ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
                
                <?php if (array_product(checkRequirements())): ?>
                    <p style="margin-top: 20px;">
                        <a href="?step=2" class="btn">Continue to Database Setup</a>
                    </p>
                <?php else: ?>
                    <p style="margin-top: 20px; color: #dc3545;">
                        Please fix the requirements above before continuing.
                    </p>
                <?php endif; ?>
                
            <?php elseif ($step == 2): ?>
                <div class="step">
                    <h2>Step 2: Database Configuration</h2>
                    <p>Configure your MySQL database connection.</p>
                </div>
                
                <?php if (file_exists('config/database.php')): ?>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                        <p>✅ Database connection has already been configured!</p>
                        <a href="?step=3" class="btn">Continue to Next Step</a>
                    </div>
                <?php else: ?>
                    <form method="POST">
                        <div class="form-group">
                            <label>Database Host:</label>
                            <input type="text" name="db_host" value="localhost" required>
                        </div>

                        <div class="form-group">
                            <label>Database Name:</label>
                            <input type="text" name="db_name" value="shahid_db" required>
                        </div>

                        <div class="form-group">
                            <label>Database Username:</label>
                            <input type="text" name="db_user" value="root" required>
                        </div>

                        <div class="form-group">
                            <label>Database Password:</label>
                            <input type="password" name="db_pass">
                        </div>

                        <button type="submit" class="btn">Test Connection & Continue</button>
                    </form>
                <?php endif; ?>
                
            <?php elseif ($step == 3): ?>
                <div class="step">
                    <h2>Step 3: Create Database Tables</h2>
                    <p>Create the required database tables.</p>
                </div>

                <?php if (file_exists('config/tables_created.lock')): ?>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                        <p>✅ Database tables have already been created!</p>
                        <a href="?step=4" class="btn">Continue to Next Step</a>
                    </div>
                <?php else: ?>
                    <form method="POST" action="?step=3">
                        <p>Click the button below to create the database tables:</p>
                        <input type="hidden" name="step" value="3">
                        <button type="submit" class="btn">Create Tables</button>
                    </form>
                <?php endif; ?>
                
            <?php elseif ($step == 4): ?>
                <div class="step">
                    <h2>Step 4: Admin Account</h2>
                    <p>Create your administrator account.</p>
                </div>

                <?php if (file_exists('config/admin_created.lock')): ?>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                        <p>✅ Admin account has already been created!</p>
                        <a href="?step=5" class="btn">Continue to Next Step</a>
                    </div>
                <?php else: ?>
                    <form method="POST" action="?step=4">
                        <input type="hidden" name="step" value="4">
                        <div class="form-group">
                            <label>Admin Name:</label>
                            <input type="text" name="admin_name" required>
                        </div>

                        <div class="form-group">
                            <label>Admin Email:</label>
                            <input type="email" name="admin_email" required>
                        </div>

                        <div class="form-group">
                            <label>Admin Password:</label>
                            <input type="password" name="admin_password" required>
                        </div>

                        <div class="form-group">
                            <label>Confirm Password:</label>
                            <input type="password" name="confirm_password" required>
                        </div>

                        <button type="submit" class="btn">Create Admin Account</button>
                    </form>
                <?php endif; ?>
                
            <?php elseif ($step == 5): ?>
                <div class="step">
                    <h2>Step 5: Site Configuration</h2>
                    <p>Configure your site settings and integrations.</p>
                </div>

                <?php if (file_exists('config/installed.lock')): ?>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                        <p>✅ Installation has already been completed!</p>
                        <a href="?step=6" class="btn">View Completion</a>
                    </div>
                <?php else: ?>
                    <form method="POST" action="?step=5">
                        <input type="hidden" name="step" value="5">
                    <h3>Site Settings</h3>
                    <div class="form-group">
                        <label>Site Name:</label>
                        <input type="text" name="site_name" value="Shahid">
                    </div>
                    
                    <div class="form-group">
                        <label>Site URL:</label>
                        <input type="url" name="site_url" placeholder="https://yoursite.com">
                    </div>
                    
                    <div class="form-group">
                        <label>Site Description:</label>
                        <textarea name="site_description" rows="3">Professional Video Streaming Platform</textarea>
                    </div>
                    
                    <h3>Payment Settings (Optional)</h3>
                    <div class="form-group">
                        <label>Stripe Public Key:</label>
                        <input type="text" name="stripe_public_key">
                    </div>
                    
                    <div class="form-group">
                        <label>Stripe Secret Key:</label>
                        <input type="text" name="stripe_secret_key">
                    </div>
                    
                    <div class="form-group">
                        <label>PayPal Client ID:</label>
                        <input type="text" name="paypal_client_id">
                    </div>
                    
                    <div class="form-group">
                        <label>PayPal Client Secret:</label>
                        <input type="text" name="paypal_client_secret">
                    </div>
                    
                    <h3>SEO Settings (Optional)</h3>
                    <div class="form-group">
                        <label>Google Analytics ID:</label>
                        <input type="text" name="google_analytics_id" placeholder="G-XXXXXXXXXX">
                    </div>
                    
                    <div class="form-group">
                        <label>Google Search Console Code:</label>
                        <input type="text" name="google_search_console">
                    </div>
                    
                    <button type="submit" class="btn">Complete Installation</button>
                </form>
                <?php endif; ?>

            <?php elseif ($step == 6): ?>
                <div class="step">
                    <h2>🎉 Installation Complete!</h2>
                    <p>Shahid has been successfully installed.</p>
                </div>
                
                <div style="background: #e8f5e8; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
                    <h3>Next Steps:</h3>
                    <ul style="margin-left: 20px;">
                        <li>Visit your <a href="../">website homepage</a></li>
                        <li>Access the <a href="../admin">admin panel</a></li>
                        <li>Start uploading your content</li>
                        <li>Configure payment settings</li>
                        <li>Customize your theme</li>
                    </ul>
                </div>
                
                <div style="background: #fff3cd; padding: 20px; border-radius: 5px;">
                    <h3>Security Notice:</h3>
                    <p>For security reasons, please delete or rename the <code>install.php</code> file after installation.</p>
                </div>
                
            <?php endif; ?>
        </div>
    </div>

    <script>
    // Prevent double form submission
    document.addEventListener('DOMContentLoaded', function() {
        const forms = document.querySelectorAll('form');
        forms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = 'Processing...';

                    // Re-enable after 5 seconds in case of error
                    setTimeout(function() {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = submitBtn.getAttribute('data-original-text') || 'Submit';
                    }, 5000);
                }
            });
        });

        // Store original button text
        const buttons = document.querySelectorAll('button[type="submit"]');
        buttons.forEach(function(btn) {
            btn.setAttribute('data-original-text', btn.innerHTML);
        });
    });
    </script>
</body>
</html>
