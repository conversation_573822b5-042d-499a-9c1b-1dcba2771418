<?php
/**
 * Shahid - Admin Controller
 * Professional Video Streaming Platform
 */

require_once 'models/Movie.php';
require_once 'models/Series.php';
require_once 'models/User.php';

class AdminController extends Controller {
    
    public function __construct() {
        parent::__construct();
        $this->requireAdmin();
    }
    
    public function dashboard() {
        try {
            // Get statistics
            $stats = $this->getDashboardStats();
            
            // Get recent activities
            $recentMovies = (new Movie())->getLatest(5);
            $recentSeries = (new Series())->getLatest(5);
            $recentUsers = $this->getRecentUsers(5);
            
            // Get revenue data for charts
            $revenueData = $this->getRevenueData();
            
            $data = [
                'page_title' => 'Dashboard - Admin Panel',
                'stats' => $stats,
                'recent_movies' => $recentMovies,
                'recent_series' => $recentSeries,
                'recent_users' => $recentUsers,
                'revenue_data' => $revenueData,
                'user' => $this->auth->getCurrentUser()
            ];
            
            $this->view('admin/dashboard', $data);
            
        } catch (Exception $e) {
            error_log('Admin Dashboard Error: ' . $e->getMessage());
            $this->view('errors/500', ['error' => 'Unable to load dashboard']);
        }
    }
    
    public function login() {
        // If already logged in as admin, redirect to dashboard
        if ($this->auth->isAdmin()) {
            $this->redirect('/admin');
            return;
        }
        
        if ($_POST) {
            $this->validateCSRF();
            
            $email = $this->sanitizeInput($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            
            $validation = $this->validateInput($_POST, [
                'email' => ['required' => true, 'email' => true],
                'password' => ['required' => true, 'min_length' => 6]
            ]);
            
            if (empty($validation)) {
                $result = $this->auth->login($email, $password);
                
                if ($result['success'] && $result['user']['role'] === 'admin') {
                    $this->redirect('/admin');
                    return;
                } else {
                    $error = 'Invalid admin credentials';
                }
            } else {
                $error = 'Please check your input and try again.';
            }
        }
        
        $data = [
            'page_title' => 'Admin Login',
            'error' => $error ?? null,
            'csrf_token' => $this->security->generateCSRFToken()
        ];
        
        $this->view('admin/login', $data);
    }
    
    public function content() {
        $type = $_GET['type'] ?? 'movies';
        $page = max(1, intval($_GET['page'] ?? 1));
        $search = $this->sanitizeInput($_GET['search'] ?? '');
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        try {
            if ($type === 'movies') {
                $movieModel = new Movie();
                if ($search) {
                    $content = $movieModel->search($search, $limit, $offset);
                    $totalContent = count($content);
                } else {
                    $content = $movieModel->findAll($limit, $offset);
                    $totalContent = $movieModel->count();
                }
            } else {
                $seriesModel = new Series();
                if ($search) {
                    $content = $seriesModel->search($search, $limit, $offset);
                    $totalContent = count($content);
                } else {
                    $content = $seriesModel->findAll($limit, $offset);
                    $totalContent = $seriesModel->count();
                }
            }
            
            $totalPages = ceil($totalContent / $limit);
            
            $data = [
                'page_title' => 'Content Management - Admin Panel',
                'type' => $type,
                'content' => $content,
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_content' => $totalContent,
                'search' => $search,
                'user' => $this->auth->getCurrentUser()
            ];
            
            $this->view('admin/content', $data);
            
        } catch (Exception $e) {
            error_log('Admin Content Error: ' . $e->getMessage());
            $this->view('errors/500', ['error' => 'Unable to load content']);
        }
    }
    
    public function users() {
        $page = max(1, intval($_GET['page'] ?? 1));
        $search = $this->sanitizeInput($_GET['search'] ?? '');
        $role = $this->sanitizeInput($_GET['role'] ?? '');
        $status = $this->sanitizeInput($_GET['status'] ?? '');
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        try {
            $userModel = new User();
            $conditions = [];
            
            if ($search) {
                $sql = "SELECT * FROM users 
                        WHERE (name LIKE :search OR email LIKE :search)";
                $params = [':search' => "%$search%"];
            } else {
                $sql = "SELECT * FROM users WHERE 1=1";
                $params = [];
            }
            
            if ($role) {
                $sql .= " AND role = :role";
                $params[':role'] = $role;
            }
            
            if ($status) {
                $sql .= " AND status = :status";
                $params[':status'] = $status;
            }
            
            $sql .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
            $params[':limit'] = $limit;
            $params[':offset'] = $offset;
            
            $stmt = $this->db->prepare($sql);
            foreach ($params as $key => $value) {
                if ($key === ':limit' || $key === ':offset') {
                    $stmt->bindValue($key, $value, PDO::PARAM_INT);
                } else {
                    $stmt->bindValue($key, $value);
                }
            }
            $stmt->execute();
            $users = $stmt->fetchAll();
            
            // Get total count
            $countSql = str_replace(['ORDER BY created_at DESC', 'LIMIT :limit OFFSET :offset'], '', $sql);
            $countParams = array_diff_key($params, [':limit' => '', ':offset' => '']);
            $stmt = $this->db->prepare($countSql);
            foreach ($countParams as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            $totalUsers = $stmt->rowCount();
            
            $totalPages = ceil($totalUsers / $limit);
            
            $data = [
                'page_title' => 'User Management - Admin Panel',
                'users' => $users,
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_users' => $totalUsers,
                'search' => $search,
                'role' => $role,
                'status' => $status,
                'user' => $this->auth->getCurrentUser()
            ];
            
            $this->view('admin/users', $data);
            
        } catch (Exception $e) {
            error_log('Admin Users Error: ' . $e->getMessage());
            $this->view('errors/500', ['error' => 'Unable to load users']);
        }
    }
    
    public function subscriptions() {
        try {
            // Get subscription plans
            $sql = "SELECT * FROM subscriptions ORDER BY price ASC";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $plans = $stmt->fetchAll();
            
            // Get active subscriptions
            $sql = "SELECT us.*, u.name as user_name, u.email as user_email, s.name as plan_name 
                    FROM user_subscriptions us
                    JOIN users u ON us.user_id = u.id
                    JOIN subscriptions s ON us.subscription_id = s.id
                    WHERE us.status = 'active'
                    ORDER BY us.created_at DESC
                    LIMIT 20";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $activeSubscriptions = $stmt->fetchAll();
            
            // Get subscription statistics
            $subscriptionStats = $this->getSubscriptionStats();
            
            $data = [
                'page_title' => 'Subscription Management - Admin Panel',
                'plans' => $plans,
                'active_subscriptions' => $activeSubscriptions,
                'subscription_stats' => $subscriptionStats,
                'user' => $this->auth->getCurrentUser()
            ];
            
            $this->view('admin/subscriptions', $data);
            
        } catch (Exception $e) {
            error_log('Admin Subscriptions Error: ' . $e->getMessage());
            $this->view('errors/500', ['error' => 'Unable to load subscriptions']);
        }
    }
    
    public function payments() {
        $page = max(1, intval($_GET['page'] ?? 1));
        $status = $this->sanitizeInput($_GET['status'] ?? '');
        $method = $this->sanitizeInput($_GET['method'] ?? '');
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        try {
            $sql = "SELECT p.*, u.name as user_name, u.email as user_email, s.name as plan_name 
                    FROM payments p
                    JOIN users u ON p.user_id = u.id
                    JOIN subscriptions s ON p.subscription_id = s.id
                    WHERE 1=1";
            $params = [];
            
            if ($status) {
                $sql .= " AND p.status = :status";
                $params[':status'] = $status;
            }
            
            if ($method) {
                $sql .= " AND p.payment_method = :method";
                $params[':method'] = $method;
            }
            
            $sql .= " ORDER BY p.created_at DESC LIMIT :limit OFFSET :offset";
            $params[':limit'] = $limit;
            $params[':offset'] = $offset;
            
            $stmt = $this->db->prepare($sql);
            foreach ($params as $key => $value) {
                if ($key === ':limit' || $key === ':offset') {
                    $stmt->bindValue($key, $value, PDO::PARAM_INT);
                } else {
                    $stmt->bindValue($key, $value);
                }
            }
            $stmt->execute();
            $payments = $stmt->fetchAll();
            
            // Get payment statistics
            $paymentStats = $this->getPaymentStats();
            
            $data = [
                'page_title' => 'Payment Management - Admin Panel',
                'payments' => $payments,
                'current_page' => $page,
                'status' => $status,
                'method' => $method,
                'payment_stats' => $paymentStats,
                'user' => $this->auth->getCurrentUser()
            ];
            
            $this->view('admin/payments', $data);
            
        } catch (Exception $e) {
            error_log('Admin Payments Error: ' . $e->getMessage());
            $this->view('errors/500', ['error' => 'Unable to load payments']);
        }
    }
    
    public function settings() {
        if ($_POST) {
            $this->validateCSRF();
            
            try {
                foreach ($_POST as $key => $value) {
                    if ($key !== 'csrf_token') {
                        $this->updateSetting($key, $value);
                    }
                }
                
                $_SESSION['flash_message'] = 'Settings updated successfully';
                $_SESSION['flash_type'] = 'success';
                $this->redirect('/admin/settings');
                return;
                
            } catch (Exception $e) {
                error_log('Settings Update Error: ' . $e->getMessage());
                $error = 'Failed to update settings';
            }
        }
        
        try {
            // Get all settings
            $sql = "SELECT * FROM settings ORDER BY `key`";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $settings = $stmt->fetchAll();
            
            $settingsArray = [];
            foreach ($settings as $setting) {
                $settingsArray[$setting['key']] = $setting['value'];
            }
            
            $data = [
                'page_title' => 'Settings - Admin Panel',
                'settings' => $settingsArray,
                'error' => $error ?? null,
                'csrf_token' => $this->security->generateCSRFToken(),
                'user' => $this->auth->getCurrentUser()
            ];
            
            $this->view('admin/settings', $data);
            
        } catch (Exception $e) {
            error_log('Admin Settings Error: ' . $e->getMessage());
            $this->view('errors/500', ['error' => 'Unable to load settings']);
        }
    }
    
    private function getDashboardStats() {
        $stats = [];
        
        // Total users
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
        $stmt->execute();
        $stats['total_users'] = $stmt->fetch()['count'];
        
        // Total movies
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM movies WHERE status = 'active'");
        $stmt->execute();
        $stats['total_movies'] = $stmt->fetch()['count'];
        
        // Total series
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM series WHERE status = 'active'");
        $stmt->execute();
        $stats['total_series'] = $stmt->fetch()['count'];
        
        // Active subscriptions
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM user_subscriptions WHERE status = 'active'");
        $stmt->execute();
        $stats['active_subscriptions'] = $stmt->fetch()['count'];
        
        // Monthly revenue
        $stmt = $this->db->prepare("SELECT SUM(amount) as revenue FROM payments WHERE status = 'completed' AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())");
        $stmt->execute();
        $stats['monthly_revenue'] = $stmt->fetch()['revenue'] ?? 0;
        
        // Total views
        $stmt = $this->db->prepare("SELECT SUM(views) as total_views FROM (SELECT views FROM movies UNION ALL SELECT views FROM series) as combined");
        $stmt->execute();
        $stats['total_views'] = $stmt->fetch()['total_views'] ?? 0;
        
        return $stats;
    }
    
    private function getRecentUsers($limit = 5) {
        $sql = "SELECT * FROM users ORDER BY created_at DESC LIMIT :limit";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    private function getRevenueData() {
        // Get last 12 months revenue
        $sql = "SELECT 
                    DATE_FORMAT(created_at, '%Y-%m') as month,
                    SUM(amount) as revenue
                FROM payments 
                WHERE status = 'completed' 
                AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                ORDER BY month";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    private function getSubscriptionStats() {
        $stats = [];
        
        // Subscriptions by plan
        $sql = "SELECT s.name, COUNT(us.id) as count 
                FROM subscriptions s
                LEFT JOIN user_subscriptions us ON s.id = us.subscription_id AND us.status = 'active'
                GROUP BY s.id, s.name";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['by_plan'] = $stmt->fetchAll();
        
        return $stats;
    }
    
    private function getPaymentStats() {
        $stats = [];
        
        // Payments by method
        $sql = "SELECT payment_method, COUNT(*) as count, SUM(amount) as total 
                FROM payments 
                WHERE status = 'completed'
                GROUP BY payment_method";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['by_method'] = $stmt->fetchAll();
        
        // Payments by status
        $sql = "SELECT status, COUNT(*) as count 
                FROM payments 
                GROUP BY status";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['by_status'] = $stmt->fetchAll();
        
        return $stats;
    }
    
    private function updateSetting($key, $value) {
        $sql = "INSERT INTO settings (`key`, `value`) VALUES (:key, :value) 
                ON DUPLICATE KEY UPDATE `value` = :value";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':key', $key);
        $stmt->bindParam(':value', $value);
        return $stmt->execute();
    }
}
?>
