<?php
/**
 * Shahid - Database Connection Class
 * Professional Video Streaming Platform
 */

class Database {
    private $host;
    private $db_name;
    private $username;
    private $password;
    private $charset = 'utf8mb4';
    public $conn;

    public function __construct() {
        // Load configuration from database config file
        $config = include __DIR__ . '/database.php';
        $this->host = $config['host'];
        $this->db_name = $config['name'];
        $this->username = $config['username'];
        $this->password = $config['password'];
    }

    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            $this->conn->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
        } catch(PDOException $exception) {
            throw new Exception("Database connection error: " . $exception->getMessage());
        }
        
        return $this->conn;
    }

    public function closeConnection() {
        $this->conn = null;
    }

    // Test database connection
    public static function testConnection($host, $dbname, $username, $password) {
        try {
            $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            return true;
        } catch(PDOException $e) {
            return false;
        }
    }

    // Create database if not exists
    public static function createDatabase($host, $dbname, $username, $password) {
        try {
            $dsn = "mysql:host=$host;charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $sql = "CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $pdo->exec($sql);
            
            return true;
        } catch(PDOException $e) {
            throw new Exception("Failed to create database: " . $e->getMessage());
        }
    }

    // Save database configuration
    public static function saveConfig($host, $name, $username, $password) {
        $configContent = "<?php\n/**\n * Shahid - Database Configuration\n * Professional Video Streaming Platform\n * \n * This file is generated by the installation process\n * DO NOT EDIT MANUALLY\n */\n\nreturn [\n    'host' => '$host',\n    'name' => '$name',\n    'username' => '$username',\n    'password' => '$password'\n];";
        
        $configFile = __DIR__ . '/database.php';
        return file_put_contents($configFile, $configContent) !== false;
    }

    // Check if tables exist
    public function tablesExist() {
        try {
            $this->getConnection();
            $stmt = $this->conn->query("SHOW TABLES LIKE 'users'");
            return $stmt->rowCount() > 0;
        } catch(Exception $e) {
            return false;
        }
    }

    // Execute SQL file
    public function executeSqlFile($sqlFile) {
        try {
            if (!file_exists($sqlFile)) {
                throw new Exception("SQL file not found: $sqlFile");
            }

            $this->getConnection();
            $sql = file_get_contents($sqlFile);
            
            // Split SQL into individual statements
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    $this->conn->exec($statement);
                }
            }
            
            return true;
        } catch(Exception $e) {
            throw new Exception("Failed to execute SQL file: " . $e->getMessage());
        }
    }
}
?>
