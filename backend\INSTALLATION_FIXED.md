# إصلاح مشكلة التثبيت - Shahid Platform

## المشكلة الأصلية

كانت هناك أخطاء في ملف `install.php`:
```
Warning: Undefined array key "host" in install.php on line 80
Warning: Undefined array key "name" in install.php on line 80
Warning: Undefined array key "username" in install.php on line 81
Warning: Undefined array key "password" in install.php on line 81
Failed to create tables: SQLSTATE[HY000] [1045] Access denied for user ''@'localhost' (using password: NO)
```

## الحل المطبق

تم إنشاء ملف تثبيت مبسط وموثوق: `install_simple.php`

### الإصلاحات الرئيسية:

1. **قراءة إعدادات قاعدة البيانات بشكل صحيح**
   - حفظ الإعدادات في ملف `config/database.php`
   - قراءة الإعدادات من الملف بدلاً من SESSION

2. **التحقق من وجود الملفات**
   - فحص وجود ملف التكوين قبل المتابعة
   - رسائل خطأ واضحة عند عدم وجود الملفات

3. **إنشاء المجلدات تلقائياً**
   - إنشاء مجلد `config` إذا لم يكن موجوداً
   - التحقق من صلاحيات الكتابة

4. **ملف SQL مبسط**
   - `database/schema_simple.sql` مع جداول أساسية
   - بيانات تجريبية للاختبار

## كيفية الاستخدام

### الطريقة الجديدة (المُوصى بها):
```
http://your-domain.com/backend/install_simple.php
```

### الطريقة القديمة (إذا أردت إصلاحها):
```
http://your-domain.com/backend/install.php
```

## خطوات التثبيت

### 1. الخطوة الأولى - فحص المتطلبات
- ✅ فحص إصدار PHP
- ✅ فحص امتداد MySQL
- ✅ فحص صلاحيات الكتابة

### 2. الخطوة الثانية - تكوين قاعدة البيانات
```
Database Host: localhost
Database Name: shahid_db
Database Username: root
Database Password: [كلمة مرور قاعدة البيانات]
```

### 3. الخطوة الثالثة - إنشاء الجداول
- إنشاء جداول المستخدمين والأفلام والمسلسلات
- إدراج بيانات تجريبية

### 4. الخطوة الرابعة - إنشاء حساب المدير
```
Admin Name: [اسم المدير]
Admin Email: <EMAIL>
Admin Password: [كلمة مرور قوية]
```

### 5. الخطوة الخامسة - إكمال التثبيت
- إنشاء ملف `installed.lock`
- التوجه للموقع أو لوحة التحكم

## الملفات الجديدة

### 1. `install_simple.php`
- ملف تثبيت مبسط وموثوق
- واجهة مستخدم محسنة
- معالجة أخطاء شاملة

### 2. `database/schema_simple.sql`
- جداول قاعدة البيانات الأساسية
- بيانات تجريبية للاختبار
- خطط اشتراك افتراضية

### 3. `config/database.php` (يتم إنشاؤه تلقائياً)
```php
<?php
return [
    'host' => 'localhost',
    'name' => 'shahid_db',
    'username' => 'root',
    'password' => 'your_password'
];
```

## الجداول المُنشأة

### الجداول الأساسية:
- `users` - المستخدمون
- `movies` - الأفلام
- `series` - المسلسلات
- `episodes` - الحلقات
- `subscriptions` - خطط الاشتراك
- `user_subscriptions` - اشتراكات المستخدمين
- `user_favorites` - المفضلة
- `user_watchlist` - قائمة المشاهدة
- `watch_history` - تاريخ المشاهدة

### البيانات التجريبية:
- 3 خطط اشتراك (Basic, Premium, Ultimate)
- 3 أفلام عينة (The Matrix, Inception, Interstellar)
- 3 مسلسلات عينة (Breaking Bad, Game of Thrones, Stranger Things)
- 3 حلقات من Breaking Bad

## استكشاف الأخطاء

### إذا ظهر خطأ "Access denied":
1. تأكد من صحة بيانات قاعدة البيانات
2. تأكد من وجود قاعدة البيانات
3. تأكد من صلاحيات المستخدم

### إذا ظهر خطأ "Permission denied":
1. تأكد من صلاحيات الكتابة في مجلد `config`
2. قم بتشغيل: `chmod 755 config/`

### إذا ظهر خطأ "SQL file not found":
1. تأكد من وجود ملف `database/schema_simple.sql`
2. تأكد من المسار الصحيح

## الاختبار

بعد التثبيت الناجح:

1. **اختبر تسجيل الدخول للمدير:**
   ```
   Email: [البريد الذي أدخلته]
   Password: [كلمة المرور التي أدخلتها]
   ```

2. **اختبر API:**
   ```
   GET /api/movies
   GET /api/series
   GET /api/auth/login
   ```

3. **اختبر قاعدة البيانات:**
   ```sql
   SELECT * FROM users WHERE role = 'admin';
   SELECT * FROM movies;
   SELECT * FROM series;
   ```

## الأمان

### بعد التثبيت:
1. احذف ملفات التثبيت:
   ```bash
   rm install.php
   rm install_simple.php
   ```

2. غيّر صلاحيات مجلد config:
   ```bash
   chmod 644 config/
   ```

3. أضف `.htaccess` لحماية مجلد config:
   ```apache
   Deny from all
   ```

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف الأخطاء: `error_log`
2. تأكد من إعدادات PHP
3. تحقق من صلاحيات الملفات

الآن يجب أن يعمل التثبيت بسلاسة! 🎉
