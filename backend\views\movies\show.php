<?php include 'views/layout/header.php'; ?>

<!-- Movie Hero Section -->
<div class="movie-hero" style="background-image: url('<?= $movie['backdrop'] ?? $movie['poster'] ?>');">
    <div class="movie-hero-overlay"></div>
    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-4">
                <div class="movie-poster-large">
                    <img src="<?= $movie['poster'] ?? '/assets/images/no-poster.jpg' ?>" 
                         alt="<?= htmlspecialchars($movie['title']) ?>" 
                         class="img-fluid">
                    <?php if ($movie['premium']): ?>
                        <span class="premium-badge-large">
                            <i class="fas fa-crown"></i>
                            Premium
                        </span>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="col-lg-8">
                <div class="movie-details">
                    <h1 class="movie-title"><?= htmlspecialchars($movie['title']) ?></h1>
                    
                    <div class="movie-meta-large">
                        <?php if ($movie['year']): ?>
                            <span class="meta-item">
                                <i class="fas fa-calendar me-2"></i>
                                <?= $movie['year'] ?>
                            </span>
                        <?php endif; ?>
                        
                        <?php if ($movie['duration']): ?>
                            <span class="meta-item">
                                <i class="fas fa-clock me-2"></i>
                                <?= floor($movie['duration'] / 60) ?>س <?= $movie['duration'] % 60 ?>د
                            </span>
                        <?php endif; ?>
                        
                        <?php if ($movie['rating']): ?>
                            <span class="meta-item">
                                <i class="fas fa-star me-2"></i>
                                <?= $movie['rating'] ?>/10
                            </span>
                        <?php endif; ?>
                        
                        <?php if ($movie['imdb_rating']): ?>
                            <span class="meta-item">
                                <i class="fab fa-imdb me-2"></i>
                                <?= $movie['imdb_rating'] ?>/10
                            </span>
                        <?php endif; ?>
                        
                        <span class="meta-item">
                            <i class="fas fa-eye me-2"></i>
                            <?= number_format($movie['views']) ?> مشاهدة
                        </span>
                    </div>
                    
                    <?php if (!empty($movie['genres'])): ?>
                        <div class="movie-genres-large">
                            <?php foreach ($movie['genres'] as $genre): ?>
                                <span class="genre-badge"><?= htmlspecialchars($genre) ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($movie['description']): ?>
                        <p class="movie-description"><?= htmlspecialchars($movie['description']) ?></p>
                    <?php endif; ?>
                    
                    <div class="movie-actions">
                        <?php if ($has_access): ?>
                            <a href="/watch/<?= $movie['id'] ?>?type=movie" class="btn btn-primary btn-lg">
                                <i class="fas fa-play me-2"></i>
                                <?php if ($watch_progress && $watch_progress['progress'] > 0): ?>
                                    متابعة المشاهدة
                                <?php else: ?>
                                    مشاهدة الآن
                                <?php endif; ?>
                            </a>
                        <?php else: ?>
                            <a href="/subscriptions" class="btn btn-primary btn-lg">
                                <i class="fas fa-crown me-2"></i>
                                اشترك للمشاهدة
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($movie['trailer_url']): ?>
                            <button class="btn btn-outline-light btn-lg me-3" data-bs-toggle="modal" data-bs-target="#trailerModal">
                                <i class="fas fa-play-circle me-2"></i>
                                المقطع الدعائي
                            </button>
                        <?php endif; ?>
                        
                        <button class="btn btn-outline-light favorite-btn" 
                                data-id="<?= $movie['id'] ?>" 
                                data-type="movie">
                            <i class="far fa-heart me-2"></i>
                            إضافة للمفضلة
                        </button>
                        
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-outline-light dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-share-alt me-2"></i>
                                مشاركة
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item share-btn" data-platform="facebook">
                                        <i class="fab fa-facebook-f me-2"></i>Facebook
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item share-btn" data-platform="twitter">
                                        <i class="fab fa-twitter me-2"></i>Twitter
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item share-btn" data-platform="whatsapp">
                                        <i class="fab fa-whatsapp me-2"></i>WhatsApp
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item copy-link-btn">
                                        <i class="fas fa-link me-2"></i>نسخ الرابط
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <?php if ($watch_progress && $watch_progress['progress'] > 0): ?>
                        <div class="watch-progress">
                            <div class="progress-info">
                                <span>تقدم المشاهدة: <?= round(($watch_progress['progress'] / $watch_progress['duration']) * 100) ?>%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" 
                                     style="width: <?= ($watch_progress['progress'] / $watch_progress['duration']) * 100 ?>%"></div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Movie Information -->
<div class="movie-info-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- Movie Details -->
                <div class="info-card">
                    <h3>تفاصيل الفيلم</h3>
                    <div class="row">
                        <?php if ($movie['director']): ?>
                            <div class="col-md-6 mb-3">
                                <strong>المخرج:</strong>
                                <span><?= htmlspecialchars($movie['director']) ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($movie['cast'])): ?>
                            <div class="col-md-6 mb-3">
                                <strong>الممثلون:</strong>
                                <span><?= htmlspecialchars(implode(', ', array_slice($movie['cast'], 0, 3))) ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($movie['country']): ?>
                            <div class="col-md-6 mb-3">
                                <strong>البلد:</strong>
                                <span><?= htmlspecialchars($movie['country']) ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($movie['language']): ?>
                            <div class="col-md-6 mb-3">
                                <strong>اللغة:</strong>
                                <span><?= htmlspecialchars($movie['language']) ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Available Qualities -->
                <?php if (!empty($movie['video_quality'])): ?>
                    <div class="info-card">
                        <h3>الجودات المتاحة</h3>
                        <div class="quality-badges">
                            <?php foreach ($movie['video_quality'] as $quality): ?>
                                <span class="badge bg-primary me-2"><?= htmlspecialchars($quality) ?></span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Subtitles -->
                <?php if (!empty($movie['subtitles'])): ?>
                    <div class="info-card">
                        <h3>الترجمات المتاحة</h3>
                        <div class="subtitle-list">
                            <?php foreach ($movie['subtitles'] as $subtitle): ?>
                                <span class="badge bg-secondary me-2">
                                    <?= htmlspecialchars($subtitle['language_name']) ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="col-lg-4">
                <!-- Movie Stats -->
                <div class="info-card">
                    <h3>إحصائيات</h3>
                    <div class="stats-list">
                        <div class="stat-item">
                            <span class="stat-label">المشاهدات:</span>
                            <span class="stat-value"><?= number_format($movie['views']) ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">الإعجابات:</span>
                            <span class="stat-value"><?= number_format($movie['likes']) ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">تاريخ الإضافة:</span>
                            <span class="stat-value"><?= date('d/m/Y', strtotime($movie['created_at'])) ?></span>
                        </div>
                    </div>
                </div>
                
                <!-- Report Issue -->
                <div class="info-card">
                    <h3>الإبلاغ عن مشكلة</h3>
                    <p class="text-muted">واجهت مشكلة في الفيلم؟</p>
                    <button class="btn btn-outline-danger btn-sm" data-bs-toggle="modal" data-bs-target="#reportModal">
                        <i class="fas fa-flag me-2"></i>
                        إبلاغ عن مشكلة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Related Movies -->
<?php if (!empty($related_movies)): ?>
    <div class="related-section">
        <div class="container">
            <h3 class="section-title">أفلام مشابهة</h3>
            <div class="row">
                <?php foreach ($related_movies as $related): ?>
                    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
                        <div class="content-card">
                            <div class="card-image">
                                <img src="<?= $related['poster'] ?? '/assets/images/no-poster.jpg' ?>" 
                                     alt="<?= htmlspecialchars($related['title']) ?>" 
                                     class="img-fluid">
                                <div class="card-overlay">
                                    <div class="card-actions">
                                        <a href="/watch/<?= $related['id'] ?>?type=movie" class="btn btn-primary btn-sm">
                                            <i class="fas fa-play"></i>
                                        </a>
                                        <a href="/movies/<?= $related['slug'] ?>" class="btn btn-outline-light btn-sm">
                                            <i class="fas fa-info"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="card-info">
                                <h6 class="card-title">
                                    <a href="/movies/<?= $related['slug'] ?>"><?= htmlspecialchars($related['title']) ?></a>
                                </h6>
                                <div class="card-meta">
                                    <span class="year"><?= $related['year'] ?></span>
                                    <?php if ($related['rating']): ?>
                                        <span class="rating">
                                            <i class="fas fa-star text-warning"></i> <?= $related['rating'] ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Trailer Modal -->
<?php if ($movie['trailer_url']): ?>
    <div class="modal fade" id="trailerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">المقطع الدعائي - <?= htmlspecialchars($movie['title']) ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="ratio ratio-16x9">
                        <iframe src="<?= htmlspecialchars($movie['trailer_url']) ?>" 
                                allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Report Modal -->
<div class="modal fade" id="reportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">الإبلاغ عن مشكلة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="reportForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">نوع المشكلة</label>
                        <select class="form-select" name="reason" required>
                            <option value="">اختر نوع المشكلة</option>
                            <option value="broken_link">رابط معطل</option>
                            <option value="poor_quality">جودة ضعيفة</option>
                            <option value="wrong_subtitle">ترجمة خاطئة</option>
                            <option value="inappropriate">محتوى غير مناسب</option>
                            <option value="copyright">انتهاك حقوق الطبع</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">تفاصيل المشكلة</label>
                        <textarea class="form-control" name="description" rows="3" 
                                  placeholder="اشرح المشكلة بالتفصيل..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">إرسال البلاغ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Favorite functionality
$('.favorite-btn').click(function() {
    const btn = $(this);
    const movieId = btn.data('id');
    const icon = btn.find('i');
    
    <?php if (isset($user) && $user): ?>
    $.post('/movies/toggle-favorite', {
        movie_id: movieId,
        action: icon.hasClass('fas') ? 'remove' : 'add',
        csrf_token: $('meta[name="csrf-token"]').attr('content')
    })
    .done(function(response) {
        if (response.success) {
            icon.toggleClass('far fas');
            const text = btn.find('.btn-text');
            if (text.length) {
                text.text(icon.hasClass('fas') ? 'إزالة من المفضلة' : 'إضافة للمفضلة');
            }
            showToast(response.message, 'success');
        }
    })
    .fail(function() {
        showToast('حدث خطأ، حاول مرة أخرى', 'error');
    });
    <?php else: ?>
    window.location.href = '/login';
    <?php endif; ?>
});

// Share functionality
$('.share-btn').click(function() {
    const platform = $(this).data('platform');
    const url = window.location.href;
    const title = '<?= htmlspecialchars($movie['title']) ?>';
    const text = `شاهد فيلم "${title}" على Shahid`;
    
    let shareUrl = '';
    
    switch (platform) {
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            break;
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
            break;
        case 'whatsapp':
            shareUrl = `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`;
            break;
    }
    
    if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
    }
});

// Copy link functionality
$('.copy-link-btn').click(function() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        showToast('تم نسخ الرابط بنجاح', 'success');
    });
});

// Report form
$('#reportForm').submit(function(e) {
    e.preventDefault();
    
    <?php if (isset($user) && $user): ?>
    const formData = new FormData(this);
    formData.append('movie_id', <?= $movie['id'] ?>);
    formData.append('csrf_token', $('meta[name="csrf-token"]').attr('content'));
    
    $.post('/report', formData, {
        processData: false,
        contentType: false
    })
    .done(function(response) {
        if (response.success) {
            $('#reportModal').modal('hide');
            showToast('تم إرسال البلاغ بنجاح', 'success');
            $('#reportForm')[0].reset();
        }
    })
    .fail(function() {
        showToast('حدث خطأ، حاول مرة أخرى', 'error');
    });
    <?php else: ?>
    window.location.href = '/login';
    <?php endif; ?>
});

function showToast(message, type) {
    const toast = $(`
        <div class="toast-notification toast-${type}">
            ${message}
        </div>
    `);
    
    $('body').append(toast);
    toast.fadeIn().delay(3000).fadeOut(function() {
        $(this).remove();
    });
}
</script>

<?php 
$additional_css = ['/assets/css/movie-details.css'];
include 'views/layout/footer.php'; 
?>
