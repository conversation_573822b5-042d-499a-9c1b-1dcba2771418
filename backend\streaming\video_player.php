<?php
/**
 * Shahid Platform - Advanced Video Player
 * Professional Video Streaming System
 */

session_start();
require_once '../config/database.php';
require_once '../config/auth.php';

// Check user authentication
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit;
}

// Get video ID from URL
$video_id = $_GET['id'] ?? null;
$type = $_GET['type'] ?? 'movie'; // movie or series

if (!$video_id) {
    header('Location: ../index.php');
    exit;
}

// Database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get video details
if ($type === 'movie') {
    $stmt = $pdo->prepare("SELECT * FROM movies WHERE id = ? AND status = 'active'");
    $stmt->execute([$video_id]);
    $video = $stmt->fetch(PDO::FETCH_ASSOC);
} else {
    $stmt = $pdo->prepare("SELECT * FROM series WHERE id = ? AND status IN ('active', 'ongoing', 'completed')");
    $stmt->execute([$video_id]);
    $video = $stmt->fetch(PDO::FETCH_ASSOC);
}

if (!$video) {
    header('Location: ../index.php');
    exit;
}

// Check subscription access
$user_id = $_SESSION['user_id'];
$stmt = $pdo->prepare("SELECT subscription_type, subscription_end FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

$has_access = true;
if ($user['subscription_type'] === 'free') {
    // Free users can only watch trailers
    $has_access = false;
} elseif ($user['subscription_type'] === 'basic' && $video['rating'] > 8.0) {
    // Basic users can't watch premium content
    $has_access = false;
} elseif ($user['subscription_end'] && strtotime($user['subscription_end']) < time()) {
    // Subscription expired
    $has_access = false;
}

// Record watch history
if ($has_access) {
    $stmt = $pdo->prepare("
        INSERT INTO watch_history (user_id, movie_id, series_id, watch_time, duration, created_at) 
        VALUES (?, ?, ?, 0, ?, NOW())
        ON DUPLICATE KEY UPDATE watch_time = 0, updated_at = NOW()
    ");
    
    if ($type === 'movie') {
        $stmt->execute([$user_id, $video_id, null, $video['duration']]);
    } else {
        $stmt->execute([$user_id, null, $video_id, 45]); // Default episode duration
    }
}

// Get related content
$related_content = [];
if ($type === 'movie') {
    $stmt = $pdo->prepare("
        SELECT * FROM movies 
        WHERE genre LIKE ? AND id != ? AND status = 'active' 
        ORDER BY rating DESC 
        LIMIT 6
    ");
    $genre = explode(',', $video['genre'])[0];
    $stmt->execute(["%$genre%", $video_id]);
    $related_content = $stmt->fetchAll(PDO::FETCH_ASSOC);
} else {
    $stmt = $pdo->prepare("
        SELECT * FROM series 
        WHERE genre LIKE ? AND id != ? AND status IN ('active', 'ongoing', 'completed') 
        ORDER BY rating DESC 
        LIMIT 6
    ");
    $genre = explode(',', $video['genre'])[0];
    $stmt->execute(["%$genre%", $video_id]);
    $related_content = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get user's watch progress
$watch_progress = 0;
$stmt = $pdo->prepare("
    SELECT watch_time, duration FROM watch_history 
    WHERE user_id = ? AND " . ($type === 'movie' ? 'movie_id' : 'series_id') . " = ? 
    ORDER BY created_at DESC LIMIT 1
");
$stmt->execute([$user_id, $video_id]);
$progress = $stmt->fetch(PDO::FETCH_ASSOC);
if ($progress && $progress['duration'] > 0) {
    $watch_progress = ($progress['watch_time'] / $progress['duration']) * 100;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($video['title_ar'] ?? $video['title']); ?> - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: #000;
            color: #fff;
            line-height: 1.6;
        }
        
        .video-container {
            position: relative;
            width: 100%;
            height: 100vh;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .video-player {
            width: 100%;
            height: 100%;
            max-width: 1920px;
            max-height: 1080px;
            background: #000;
            position: relative;
        }
        
        .video-element {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .video-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 2rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .video-container:hover .video-controls {
            opacity: 1;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin-bottom: 1rem;
            cursor: pointer;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: #E50914;
            border-radius: 3px;
            width: 0%;
            transition: width 0.1s ease;
        }
        
        .controls-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .controls-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .controls-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .control-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
            transform: scale(1.1);
        }
        
        .play-btn {
            font-size: 2rem;
        }
        
        .time-display {
            font-size: 0.9rem;
            color: #ccc;
        }
        
        .volume-control {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .volume-slider {
            width: 80px;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            cursor: pointer;
        }
        
        .quality-selector {
            background: rgba(0,0,0,0.8);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .video-info {
            position: absolute;
            top: 2rem;
            left: 2rem;
            right: 2rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .video-container:hover .video-info {
            opacity: 1;
        }
        
        .video-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .video-meta {
            display: flex;
            gap: 1rem;
            color: #ccc;
            font-size: 0.9rem;
        }
        
        .back-btn {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(0,0,0,0.8);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            z-index: 100;
        }
        
        .back-btn:hover {
            background: #E50914;
            color: white;
        }
        
        .access-denied {
            text-align: center;
            padding: 4rem 2rem;
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            margin: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.3);
        }
        
        .access-denied h2 {
            color: #E50914;
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        .access-denied p {
            color: #ccc;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }
        
        .upgrade-btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .upgrade-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
            color: white;
        }
        
        .related-content {
            padding: 2rem;
            background: #111;
        }
        
        .related-content h3 {
            color: #E50914;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }
        
        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .related-item {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
        }
        
        .related-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(229, 9, 20, 0.3);
            color: white;
        }
        
        .related-poster {
            width: 100%;
            height: 280px;
            background: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 3rem;
        }
        
        .related-info {
            padding: 1rem;
        }
        
        .related-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .related-meta {
            color: #ccc;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .video-info {
                top: 1rem;
                left: 1rem;
                right: 1rem;
            }
            
            .video-title {
                font-size: 1.5rem;
            }
            
            .video-controls {
                padding: 1rem;
            }
            
            .controls-row {
                flex-direction: column;
                gap: 1rem;
            }
            
            .back-btn {
                top: 1rem;
                right: 1rem;
                padding: 0.5rem 1rem;
            }
        }
    </style>
</head>
<body>
    <a href="../index.php" class="back-btn">← العودة</a>
    
    <?php if ($has_access): ?>
    <div class="video-container" id="videoContainer">
        <div class="video-player">
            <video class="video-element" id="videoElement" poster="<?php echo htmlspecialchars($video['poster'] ?? ''); ?>">
                <source src="<?php echo htmlspecialchars($video['video_url'] ?? 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'); ?>" type="video/mp4">
                <source src="<?php echo htmlspecialchars($video['video_url'] ?? 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'); ?>" type="video/webm">
                متصفحك لا يدعم تشغيل الفيديو.
            </video>
            
            <div class="video-info">
                <div class="video-title"><?php echo htmlspecialchars($video['title_ar'] ?? $video['title']); ?></div>
                <div class="video-meta">
                    <span><?php echo $video['year']; ?></span>
                    <?php if ($type === 'movie'): ?>
                        <span><?php echo $video['duration']; ?> دقيقة</span>
                    <?php else: ?>
                        <span><?php echo $video['seasons']; ?> مواسم</span>
                        <span><?php echo $video['episodes']; ?> حلقة</span>
                    <?php endif; ?>
                    <span>⭐ <?php echo $video['rating']; ?></span>
                    <span><?php echo htmlspecialchars($video['genre']); ?></span>
                </div>
            </div>
            
            <div class="video-controls">
                <div class="progress-bar" id="progressBar">
                    <div class="progress-fill" id="progressFill" style="width: <?php echo $watch_progress; ?>%"></div>
                </div>
                <div class="controls-row">
                    <div class="controls-left">
                        <button class="control-btn play-btn" id="playBtn">▶️</button>
                        <button class="control-btn" id="prevBtn">⏮️</button>
                        <button class="control-btn" id="nextBtn">⏭️</button>
                        <div class="time-display">
                            <span id="currentTime">00:00</span> / <span id="duration">00:00</span>
                        </div>
                    </div>
                    <div class="controls-right">
                        <div class="volume-control">
                            <button class="control-btn" id="muteBtn">🔊</button>
                            <div class="volume-slider" id="volumeSlider">
                                <div style="width: 70%; height: 100%; background: #E50914; border-radius: 2px;"></div>
                            </div>
                        </div>
                        <select class="quality-selector" id="qualitySelector">
                            <option value="auto">تلقائي</option>
                            <option value="1080p">1080p</option>
                            <option value="720p">720p</option>
                            <option value="480p">480p</option>
                        </select>
                        <button class="control-btn" id="fullscreenBtn">⛶</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <div class="access-denied">
        <h2>🔒 المحتوى غير متاح</h2>
        <p>
            <?php if ($user['subscription_type'] === 'free'): ?>
                يتطلب هذا المحتوى اشتراك مدفوع للمشاهدة.
            <?php elseif ($user['subscription_type'] === 'basic'): ?>
                هذا المحتوى المميز متاح فقط للمشتركين في الباقة المميزة.
            <?php else: ?>
                انتهت صلاحية اشتراكك. يرجى تجديد الاشتراك للمتابعة.
            <?php endif; ?>
        </p>
        <a href="../subscription/plans.php" class="upgrade-btn">ترقية الاشتراك</a>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($related_content)): ?>
    <div class="related-content">
        <h3>محتوى مشابه</h3>
        <div class="related-grid">
            <?php foreach ($related_content as $item): ?>
            <a href="video_player.php?id=<?php echo $item['id']; ?>&type=<?php echo $type; ?>" class="related-item">
                <div class="related-poster">
                    <?php if ($item['poster']): ?>
                        <img src="<?php echo htmlspecialchars($item['poster']); ?>" alt="<?php echo htmlspecialchars($item['title']); ?>" style="width: 100%; height: 100%; object-fit: cover;">
                    <?php else: ?>
                        🎬
                    <?php endif; ?>
                </div>
                <div class="related-info">
                    <div class="related-title"><?php echo htmlspecialchars($item['title_ar'] ?? $item['title']); ?></div>
                    <div class="related-meta">
                        <?php echo $item['year']; ?> • ⭐ <?php echo $item['rating']; ?>
                    </div>
                </div>
            </a>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <script>
        // Video player functionality
        const video = document.getElementById('videoElement');
        const playBtn = document.getElementById('playBtn');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const currentTimeEl = document.getElementById('currentTime');
        const durationEl = document.getElementById('duration');
        const muteBtn = document.getElementById('muteBtn');
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        const videoContainer = document.getElementById('videoContainer');

        // Play/Pause functionality
        playBtn.addEventListener('click', () => {
            if (video.paused) {
                video.play();
                playBtn.textContent = '⏸️';
            } else {
                video.pause();
                playBtn.textContent = '▶️';
            }
        });

        // Progress bar
        video.addEventListener('timeupdate', () => {
            const progress = (video.currentTime / video.duration) * 100;
            progressFill.style.width = progress + '%';
            currentTimeEl.textContent = formatTime(video.currentTime);
            
            // Save progress to server
            if (video.currentTime > 0) {
                saveProgress(video.currentTime);
            }
        });

        video.addEventListener('loadedmetadata', () => {
            durationEl.textContent = formatTime(video.duration);
        });

        // Progress bar click
        progressBar.addEventListener('click', (e) => {
            const rect = progressBar.getBoundingClientRect();
            const pos = (e.clientX - rect.left) / rect.width;
            video.currentTime = pos * video.duration;
        });

        // Mute/Unmute
        muteBtn.addEventListener('click', () => {
            video.muted = !video.muted;
            muteBtn.textContent = video.muted ? '🔇' : '🔊';
        });

        // Fullscreen
        fullscreenBtn.addEventListener('click', () => {
            if (document.fullscreenElement) {
                document.exitFullscreen();
            } else {
                videoContainer.requestFullscreen();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            switch(e.code) {
                case 'Space':
                    e.preventDefault();
                    playBtn.click();
                    break;
                case 'ArrowLeft':
                    video.currentTime -= 10;
                    break;
                case 'ArrowRight':
                    video.currentTime += 10;
                    break;
                case 'KeyM':
                    muteBtn.click();
                    break;
                case 'KeyF':
                    fullscreenBtn.click();
                    break;
            }
        });

        // Format time helper
        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        // Save progress to server
        function saveProgress(currentTime) {
            // Throttle progress saving
            if (!saveProgress.lastSave || Date.now() - saveProgress.lastSave > 5000) {
                saveProgress.lastSave = Date.now();
                
                fetch('../api/save_progress.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        video_id: <?php echo $video_id; ?>,
                        type: '<?php echo $type; ?>',
                        current_time: currentTime,
                        duration: video.duration
                    })
                });
            }
        }

        // Auto-hide controls
        let controlsTimeout;
        videoContainer.addEventListener('mousemove', () => {
            clearTimeout(controlsTimeout);
            controlsTimeout = setTimeout(() => {
                if (!video.paused) {
                    videoContainer.style.cursor = 'none';
                }
            }, 3000);
        });

        video.addEventListener('play', () => {
            videoContainer.style.cursor = 'none';
        });

        video.addEventListener('pause', () => {
            videoContainer.style.cursor = 'default';
        });

        console.log('🎬 Advanced Video Player loaded successfully!');
    </script>
</body>
</html>
