<?php
/**
 * Shahid Platform - Database Update Script
 * تحديث قاعدة البيانات على الخادم المحلي
 */

echo "🗄️ بدء تحديث قاعدة البيانات...\n\n";

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = ''; // كلمة مرور فارغة لـ XAMPP
$database = 'shahid_platform';

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    echo "📡 الاتصال بخادم MySQL...\n";
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ تم الاتصال بنجاح!\n\n";

    // حذف قاعدة البيانات إذا كانت موجودة
    echo "🗑️ حذف قاعدة البيانات القديمة...\n";
    $pdo->exec("DROP DATABASE IF EXISTS $database");
    echo "✅ تم حذف قاعدة البيانات القديمة!\n\n";

    // إنشاء قاعدة البيانات الجديدة
    echo "🆕 إنشاء قاعدة البيانات الجديدة...\n";
    $pdo->exec("CREATE DATABASE $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء قاعدة البيانات بنجاح!\n\n";

    // الاتصال بقاعدة البيانات الجديدة
    echo "🔗 الاتصال بقاعدة البيانات الجديدة...\n";
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ تم الاتصال بقاعدة البيانات الجديدة!\n\n";

    // تنفيذ ملف Schema
    echo "📋 تنفيذ ملف Schema...\n";
    $schemaFile = __DIR__ . '/schema.sql';
    if (file_exists($schemaFile)) {
        $schema = file_get_contents($schemaFile);
        
        // تقسيم الاستعلامات
        $queries = explode(';', $schema);
        $successCount = 0;
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                try {
                    $pdo->exec($query);
                    $successCount++;
                } catch (Exception $e) {
                    echo "⚠️ خطأ في الاستعلام: " . substr($query, 0, 50) . "...\n";
                    echo "   الخطأ: " . $e->getMessage() . "\n";
                }
            }
        }
        echo "✅ تم تنفيذ $successCount استعلام من ملف Schema!\n\n";
    } else {
        echo "❌ ملف Schema غير موجود!\n\n";
    }

    // تنفيذ ملف البيانات الإنتاجية
    echo "📊 تنفيذ ملف البيانات الإنتاجية...\n";
    $dataFile = __DIR__ . '/production_data.sql';
    if (file_exists($dataFile)) {
        $data = file_get_contents($dataFile);
        
        // تقسيم الاستعلامات
        $queries = explode(';', $data);
        $successCount = 0;
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                try {
                    $pdo->exec($query);
                    $successCount++;
                } catch (Exception $e) {
                    echo "⚠️ خطأ في البيانات: " . substr($query, 0, 50) . "...\n";
                    echo "   الخطأ: " . $e->getMessage() . "\n";
                }
            }
        }
        echo "✅ تم إدراج $successCount مجموعة بيانات!\n\n";
    } else {
        echo "❌ ملف البيانات الإنتاجية غير موجود!\n\n";
    }

    // إنشاء فهارس للأداء
    echo "⚡ إنشاء فهارس الأداء...\n";
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
        "CREATE INDEX IF NOT EXISTS idx_users_subscription ON users(subscription_type, subscription_end)",
        "CREATE INDEX IF NOT EXISTS idx_movies_status ON movies(status)",
        "CREATE INDEX IF NOT EXISTS idx_movies_genre ON movies(genre)",
        "CREATE INDEX IF NOT EXISTS idx_movies_rating ON movies(rating)",
        "CREATE INDEX IF NOT EXISTS idx_series_status ON series(status)",
        "CREATE INDEX IF NOT EXISTS idx_watch_history_user ON watch_history(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_watch_history_date ON watch_history(created_at)",
        "CREATE INDEX IF NOT EXISTS idx_ratings_content ON ratings(movie_id, series_id)",
        "CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status, created_at)",
        "CREATE INDEX IF NOT EXISTS idx_security_logs_type ON security_logs(event_type, created_at)"
    ];

    $indexCount = 0;
    foreach ($indexes as $index) {
        try {
            $pdo->exec($index);
            $indexCount++;
        } catch (Exception $e) {
            // الفهرس موجود بالفعل أو خطأ آخر
        }
    }
    echo "✅ تم إنشاء $indexCount فهرس للأداء!\n\n";

    // التحقق من البيانات
    echo "🔍 التحقق من البيانات المدرجة...\n";
    
    $tables = ['users', 'movies', 'series', 'categories', 'ratings', 'favorites', 'watch_history', 'subscriptions', 'payments'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "  📊 $table: $count سجل\n";
        } catch (Exception $e) {
            echo "  ❌ $table: خطأ في القراءة\n";
        }
    }

    echo "\n🎉 تم تحديث قاعدة البيانات بنجاح!\n\n";

    // معلومات الاتصال
    echo "📋 معلومات قاعدة البيانات:\n";
    echo "  🏠 الخادم: $host\n";
    echo "  🗄️ قاعدة البيانات: $database\n";
    echo "  👤 المستخدم: $username\n";
    echo "  🔑 كلمة المرور: " . (empty($password) ? 'فارغة (XAMPP افتراضي)' : 'محددة') . "\n\n";

    echo "🔗 روابط مفيدة:\n";
    echo "  📊 phpMyAdmin: http://localhost/phpmyadmin/\n";
    echo "  🌐 الموقع: http://localhost/amr2/flutter_module_1/backend/homepage.php\n";
    echo "  🎛️ لوحة الإدارة: http://localhost/amr2/flutter_module_1/backend/admin/dashboard.php\n";
    echo "  🔗 API: http://localhost/amr2/flutter_module_1/backend/api/test.php\n\n";

} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n\n";
    
    echo "🔧 حلول مقترحة:\n";
    echo "  1. تأكد من تشغيل XAMPP\n";
    echo "  2. تأكد من تشغيل خدمة MySQL\n";
    echo "  3. تحقق من إعدادات الاتصال\n";
    echo "  4. تأكد من عدم وجود كلمة مرور لـ root في XAMPP\n\n";
    
    exit(1);
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    exit(1);
}
?>
