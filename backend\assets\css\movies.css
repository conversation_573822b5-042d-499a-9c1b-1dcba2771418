/* Shahid Movies Page Stylesheet */

/* Page Header */
.page-header-section {
    background: linear-gradient(135deg, var(--dark-color) 0%, var(--secondary-color) 100%);
    padding: 2rem 0;
    margin-top: 76px;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: var(--text-muted);
    font-size: 1.1rem;
    margin-bottom: 0;
}

.page-stats {
    text-align: right;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    color: var(--text-color);
    font-weight: 600;
    display: inline-block;
}

/* Filters Section */
.filters-section {
    background: var(--secondary-color);
    padding: 2rem 0;
    border-bottom: 1px solid var(--border-color);
}

.filters-card {
    background: var(--dark-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow);
}

.filter-label {
    color: var(--text-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
}

.form-select {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-select:focus {
    background-color: var(--secondary-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    color: var(--text-color);
}

.form-select option {
    background-color: var(--secondary-color);
    color: var(--text-color);
}

/* Movie Cards */
.movie-card {
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.movie-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

.movie-poster {
    position: relative;
    overflow: hidden;
    aspect-ratio: 2/3;
}

.movie-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.movie-card:hover .movie-poster img {
    transform: scale(1.05);
}

.movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1rem;
    opacity: 0;
    transition: var(--transition);
}

.movie-card:hover .movie-overlay {
    opacity: 1;
}

.movie-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
}

.movie-actions .btn {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 1.1rem;
    transition: var(--transition);
}

.btn-play {
    background: var(--primary-color);
    color: white;
    border: none;
}

.btn-play:hover {
    background: #b20710;
    transform: scale(1.1);
}

.btn-favorite {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-favorite:hover {
    background: rgba(255, 255, 255, 0.3);
    color: #ff6b6b;
}

.btn-info {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-info:hover {
    background: rgba(255, 255, 255, 0.3);
}

.premium-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.movie-rating {
    position: absolute;
    bottom: 0.5rem;
    left: 0.5rem;
    background: rgba(0, 0, 0, 0.8);
    color: #ffc107;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.movie-info {
    padding: 1rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.movie-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    flex: 1;
}

.movie-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.movie-title a:hover {
    color: var(--primary-color);
}

.movie-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.movie-year,
.movie-duration,
.movie-views {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
}

.movie-genres {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.genre-tag {
    background: var(--primary-color);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 500;
}

/* Pagination */
.pagination-section {
    margin-top: 3rem;
    padding: 2rem 0;
}

.pagination {
    --bs-pagination-bg: var(--secondary-color);
    --bs-pagination-border-color: var(--border-color);
    --bs-pagination-color: var(--text-color);
    --bs-pagination-hover-bg: var(--primary-color);
    --bs-pagination-hover-border-color: var(--primary-color);
    --bs-pagination-hover-color: white;
    --bs-pagination-active-bg: var(--primary-color);
    --bs-pagination-active-border-color: var(--primary-color);
    --bs-pagination-active-color: white;
    --bs-pagination-disabled-bg: var(--secondary-color);
    --bs-pagination-disabled-color: var(--text-muted);
}

.page-link {
    border-radius: 0.5rem !important;
    margin: 0 0.2rem;
    font-weight: 600;
}

.pagination-info {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* No Content */
.no-content {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-muted);
}

.no-content-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-content h3 {
    color: var(--text-color);
    margin-bottom: 1rem;
}

/* Toast Notifications */
.toast-notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 600;
    z-index: 1050;
    display: none;
    box-shadow: var(--shadow);
    min-width: 300px;
}

.toast-success {
    background: var(--admin-success);
}

.toast-error {
    background: var(--admin-danger);
}

.toast-warning {
    background: var(--admin-warning);
    color: #212529;
}

.toast-info {
    background: var(--admin-info);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .movie-card {
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .filters-card {
        padding: 1.5rem;
    }
    
    .filters-form .row > div {
        margin-bottom: 1rem;
    }
    
    .movie-actions .btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .movie-info {
        padding: 0.75rem;
    }
    
    .movie-title {
        font-size: 0.9rem;
    }
    
    .movie-meta {
        font-size: 0.75rem;
    }
    
    .toast-notification {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        min-width: auto;
    }
}

@media (max-width: 576px) {
    .page-header-section {
        padding: 1.5rem 0;
    }
    
    .filters-section {
        padding: 1.5rem 0;
    }
    
    .filters-card {
        padding: 1rem;
    }
    
    .movie-poster {
        aspect-ratio: 3/4;
    }
    
    .movie-actions {
        gap: 0.25rem;
    }
    
    .movie-actions .btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
    
    .premium-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
    }
    
    .movie-rating {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
    
    .genre-tag {
        font-size: 0.65rem;
        padding: 0.15rem 0.4rem;
    }
}

/* Loading States */
.movie-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.movie-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Lazy Loading */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .movie-card {
        border: 2px solid var(--text-color);
    }
    
    .movie-overlay {
        background: rgba(0, 0, 0, 0.9);
    }
    
    .btn {
        border: 2px solid currentColor;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .movie-card,
    .movie-poster img,
    .movie-overlay,
    .btn {
        transition: none;
    }
    
    .movie-card:hover {
        transform: none;
    }
    
    .movie-card:hover .movie-poster img {
        transform: none;
    }
}
