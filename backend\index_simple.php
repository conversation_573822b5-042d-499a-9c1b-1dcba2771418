<?php
/**
 * Shahid - Simple Homepage
 * Professional Video Streaming Platform
 */

// Check if installation is complete
if (!file_exists('config/installed.lock')) {
    header('Location: install_simple.php');
    exit;
}

// Start session
session_start();

// Set timezone
date_default_timezone_set('Asia/Riyadh');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shahid - منصة البث الاحترافية</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%); color: white; min-height: 100vh; }
        .header { background: rgba(0,0,0,0.9); padding: 20px 0; position: fixed; width: 100%; top: 0; z-index: 1000; backdrop-filter: blur(10px); }
        .nav { max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center; padding: 0 20px; }
        .logo { display: flex; align-items: center; }
        .logo-icon { width: 40px; height: 40px; background: linear-gradient(45deg, #E50914, #B20710); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-left: 15px; }
        .logo-text { font-size: 24px; font-weight: bold; color: #E50914; }
        .nav-links { display: flex; gap: 30px; }
        .nav-links a { color: white; text-decoration: none; font-weight: 500; transition: color 0.3s; }
        .nav-links a:hover { color: #E50914; }
        .auth-buttons { display: flex; gap: 15px; }
        .btn { padding: 10px 20px; border-radius: 6px; text-decoration: none; font-weight: 500; transition: all 0.3s; }
        .btn-primary { background: linear-gradient(45deg, #E50914, #B20710); color: white; }
        .btn-secondary { border: 2px solid #E50914; color: #E50914; }
        .btn:hover { transform: translateY(-2px); }
        .hero { margin-top: 80px; padding: 100px 20px; text-align: center; }
        .hero h1 { font-size: 48px; margin-bottom: 20px; background: linear-gradient(45deg, #E50914, #ff6b6b); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
        .hero p { font-size: 20px; color: #ccc; margin-bottom: 40px; max-width: 600px; margin-left: auto; margin-right: auto; }
        .features { max-width: 1200px; margin: 0 auto; padding: 80px 20px; display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 40px; }
        .feature { background: rgba(255,255,255,0.05); padding: 40px; border-radius: 15px; text-align: center; backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.1); }
        .feature-icon { width: 60px; height: 60px; background: linear-gradient(45deg, #E50914, #B20710); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 24px; }
        .feature h3 { font-size: 24px; margin-bottom: 15px; color: #E50914; }
        .feature p { color: #ccc; line-height: 1.6; }
        .status { max-width: 800px; margin: 0 auto; padding: 40px 20px; }
        .status-card { background: rgba(255,255,255,0.05); padding: 30px; border-radius: 15px; backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.1); }
        .status-item { display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid rgba(255,255,255,0.1); }
        .status-item:last-child { border-bottom: none; }
        .status-label { font-weight: 500; }
        .status-value { color: #4CAF50; }
        .footer { text-align: center; padding: 40px 20px; color: #666; border-top: 1px solid rgba(255,255,255,0.1); }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav">
            <div class="logo">
                <div class="logo-icon">🎬</div>
                <div class="logo-text">Shahid</div>
            </div>
            <div class="nav-links">
                <a href="#features">الميزات</a>
                <a href="#status">الحالة</a>
                <a href="admin/">الإدارة</a>
                <a href="api/">API</a>
            </div>
            <div class="auth-buttons">
                <a href="admin/" class="btn btn-secondary">لوحة التحكم</a>
                <a href="api/" class="btn btn-primary">API</a>
            </div>
        </nav>
    </header>

    <section class="hero">
        <h1>مرحباً بك في Shahid</h1>
        <p>منصة البث الاحترافية - تم تثبيت النظام بنجاح وهو جاهز للاستخدام</p>
        <div style="margin-top: 30px;">
            <a href="admin/" class="btn btn-primary" style="margin: 0 10px;">دخول الإدارة</a>
            <a href="api/" class="btn btn-secondary" style="margin: 0 10px;">استكشاف API</a>
        </div>
    </section>

    <section id="features" class="features">
        <div class="feature">
            <div class="feature-icon">🎥</div>
            <h3>إدارة المحتوى</h3>
            <p>نظام شامل لإدارة الأفلام والمسلسلات مع دعم الترجمة وتعدد الجودات</p>
        </div>
        <div class="feature">
            <div class="feature-icon">👥</div>
            <h3>إدارة المستخدمين</h3>
            <p>نظام متقدم لإدارة المستخدمين والاشتراكات مع دعم المدفوعات</p>
        </div>
        <div class="feature">
            <div class="feature-icon">📱</div>
            <h3>تطبيق Flutter</h3>
            <p>تطبيق موبايل احترافي مع دعم التحميل والمشاهدة بدون إنترنت</p>
        </div>
        <div class="feature">
            <div class="feature-icon">🔒</div>
            <h3>الأمان</h3>
            <p>حماية متقدمة مع تشفير البيانات ومنع القرصنة</p>
        </div>
        <div class="feature">
            <div class="feature-icon">💳</div>
            <h3>المدفوعات</h3>
            <p>دعم Stripe و PayPal مع نظام اشتراكات متقدم</p>
        </div>
        <div class="feature">
            <div class="feature-icon">📊</div>
            <h3>التحليلات</h3>
            <p>تقارير مفصلة عن المشاهدات والإيرادات</p>
        </div>
    </section>

    <section id="status" class="status">
        <div class="status-card">
            <h2 style="text-align: center; margin-bottom: 30px; color: #E50914;">حالة النظام</h2>
            
            <?php
            // Check system status
            $dbStatus = '❌ غير متصل';
            $tablesStatus = '❌ غير موجودة';
            $adminStatus = '❌ غير موجود';
            
            try {
                if (file_exists('config/database.php')) {
                    $config = include 'config/database.php';
                    $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", $config['username'], $config['password']);
                    $dbStatus = '✅ متصل';
                    
                    // Check tables
                    $stmt = $pdo->query("SHOW TABLES");
                    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    if (count($tables) > 0) {
                        $tablesStatus = '✅ موجودة (' . count($tables) . ' جدول)';
                    }
                    
                    // Check admin
                    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'admin'");
                    $adminCount = $stmt->fetchColumn();
                    if ($adminCount > 0) {
                        $adminStatus = '✅ موجود (' . $adminCount . ' مدير)';
                    }
                }
            } catch (Exception $e) {
                // Ignore errors
            }
            ?>
            
            <div class="status-item">
                <span class="status-label">قاعدة البيانات:</span>
                <span class="status-value"><?php echo $dbStatus; ?></span>
            </div>
            <div class="status-item">
                <span class="status-label">الجداول:</span>
                <span class="status-value"><?php echo $tablesStatus; ?></span>
            </div>
            <div class="status-item">
                <span class="status-label">حساب المدير:</span>
                <span class="status-value"><?php echo $adminStatus; ?></span>
            </div>
            <div class="status-item">
                <span class="status-label">التثبيت:</span>
                <span class="status-value">✅ مكتمل</span>
            </div>
            <div class="status-item">
                <span class="status-label">إصدار PHP:</span>
                <span class="status-value">✅ <?php echo PHP_VERSION; ?></span>
            </div>
        </div>
    </section>

    <footer class="footer">
        <p>&copy; 2024 Shahid - منصة البث الاحترافية. جميع الحقوق محفوظة.</p>
        <p>تم التطوير بواسطة فريق Shahid</p>
        <p style="margin-top: 10px;">
            <a href="test_db_config.php" style="color: #E50914; text-decoration: none;">اختبار قاعدة البيانات</a> | 
            <a href="fix_db_config.php" style="color: #E50914; text-decoration: none;">إصلاح التكوين</a> |
            <a href="install_simple.php" style="color: #E50914; text-decoration: none;">إعادة التثبيت</a>
        </p>
    </footer>
</body>
</html>
