<?php
/**
 * نظام تحسين محركات البحث (SEO) المتقدم
 * يتضمن Google Analytics، Search Console، Structured Data، وتحسين الصفحات
 */

class SEOSystem {
    private $pdo;
    private $siteUrl;
    private $siteName;
    private $siteDescription;
    
    public function __construct() {
        try {
            $this->pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            throw new Exception("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
        
        $this->siteUrl = 'https://shahid.com';
        $this->siteName = 'Shahid - منصة البث الاحترافية';
        $this->siteDescription = 'منصة بث فيديو احترافية تقدم أفضل الأفلام والمسلسلات العربية والعالمية بجودة عالية';
    }
    
    /**
     * إنشاء Meta Tags للصفحة
     */
    public function generateMetaTags($pageType, $contentId = null) {
        $metaTags = [
            'title' => $this->siteName,
            'description' => $this->siteDescription,
            'keywords' => 'أفلام, مسلسلات, بث مباشر, فيديو, ترفيه',
            'og:title' => $this->siteName,
            'og:description' => $this->siteDescription,
            'og:image' => $this->siteUrl . '/assets/images/logo.png',
            'og:url' => $this->siteUrl,
            'og:type' => 'website',
            'og:site_name' => $this->siteName,
            'twitter:card' => 'summary_large_image',
            'twitter:title' => $this->siteName,
            'twitter:description' => $this->siteDescription,
            'twitter:image' => $this->siteUrl . '/assets/images/logo.png'
        ];
        
        switch ($pageType) {
            case 'movie':
                if ($contentId) {
                    $movie = $this->getMovieData($contentId);
                    if ($movie) {
                        $metaTags['title'] = $movie['title'] . ' - ' . $this->siteName;
                        $metaTags['description'] = $movie['description'];
                        $metaTags['keywords'] = $movie['genre'] . ', ' . $movie['title'] . ', فيلم';
                        $metaTags['og:title'] = $movie['title'];
                        $metaTags['og:description'] = $movie['description'];
                        $metaTags['og:image'] = $movie['poster'];
                        $metaTags['og:type'] = 'video.movie';
                        $metaTags['twitter:title'] = $movie['title'];
                        $metaTags['twitter:description'] = $movie['description'];
                        $metaTags['twitter:image'] = $movie['poster'];
                    }
                }
                break;
                
            case 'series':
                if ($contentId) {
                    $series = $this->getSeriesData($contentId);
                    if ($series) {
                        $metaTags['title'] = $series['title'] . ' - ' . $this->siteName;
                        $metaTags['description'] = $series['description'];
                        $metaTags['keywords'] = $series['genre'] . ', ' . $series['title'] . ', مسلسل';
                        $metaTags['og:title'] = $series['title'];
                        $metaTags['og:description'] = $series['description'];
                        $metaTags['og:image'] = $series['poster'];
                        $metaTags['og:type'] = 'video.tv_show';
                        $metaTags['twitter:title'] = $series['title'];
                        $metaTags['twitter:description'] = $series['description'];
                        $metaTags['twitter:image'] = $series['poster'];
                    }
                }
                break;
                
            case 'movies':
                $metaTags['title'] = 'الأفلام - ' . $this->siteName;
                $metaTags['description'] = 'تصفح مجموعة واسعة من الأفلام العربية والعالمية بجودة عالية';
                $metaTags['keywords'] = 'أفلام, سينما, أفلام عربية, أفلام أجنبية';
                break;
                
            case 'series_list':
                $metaTags['title'] = 'المسلسلات - ' . $this->siteName;
                $metaTags['description'] = 'شاهد أحدث المسلسلات العربية والعالمية بجودة عالية';
                $metaTags['keywords'] = 'مسلسلات, دراما, مسلسلات عربية, مسلسلات أجنبية';
                break;
        }
        
        return $metaTags;
    }
    
    /**
     * إنشاء Structured Data (JSON-LD)
     */
    public function generateStructuredData($pageType, $contentId = null) {
        $structuredData = [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => $this->siteName,
            'url' => $this->siteUrl,
            'description' => $this->siteDescription,
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => $this->siteUrl . '/search?q={search_term_string}',
                'query-input' => 'required name=search_term_string'
            ]
        ];
        
        switch ($pageType) {
            case 'movie':
                if ($contentId) {
                    $movie = $this->getMovieData($contentId);
                    if ($movie) {
                        $structuredData = [
                            '@context' => 'https://schema.org',
                            '@type' => 'Movie',
                            'name' => $movie['title'],
                            'description' => $movie['description'],
                            'image' => $movie['poster'],
                            'datePublished' => $movie['year'],
                            'genre' => explode('،', $movie['genre']),
                            'duration' => 'PT' . $movie['duration'],
                            'aggregateRating' => [
                                '@type' => 'AggregateRating',
                                'ratingValue' => $movie['rating'],
                                'ratingCount' => $movie['rating_count'],
                                'bestRating' => 10,
                                'worstRating' => 1
                            ]
                        ];
                        
                        if ($movie['director']) {
                            $structuredData['director'] = [
                                '@type' => 'Person',
                                'name' => $movie['director']
                            ];
                        }
                        
                        if ($movie['cast']) {
                            $actors = explode('،', $movie['cast']);
                            $structuredData['actor'] = array_map(function($actor) {
                                return [
                                    '@type' => 'Person',
                                    'name' => trim($actor)
                                ];
                            }, $actors);
                        }
                    }
                }
                break;
                
            case 'series':
                if ($contentId) {
                    $series = $this->getSeriesData($contentId);
                    if ($series) {
                        $structuredData = [
                            '@context' => 'https://schema.org',
                            '@type' => 'TVSeries',
                            'name' => $series['title'],
                            'description' => $series['description'],
                            'image' => $series['poster'],
                            'datePublished' => $series['year'],
                            'genre' => explode('،', $series['genre']),
                            'numberOfSeasons' => $series['seasons'],
                            'numberOfEpisodes' => $series['episodes'],
                            'aggregateRating' => [
                                '@type' => 'AggregateRating',
                                'ratingValue' => $series['rating'],
                                'ratingCount' => $series['rating_count'],
                                'bestRating' => 10,
                                'worstRating' => 1
                            ]
                        ];
                    }
                }
                break;
        }
        
        return $structuredData;
    }
    
    /**
     * إنشاء Sitemap XML
     */
    public function generateSitemap() {
        $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        // الصفحة الرئيسية
        $sitemap .= $this->addSitemapUrl($this->siteUrl, '1.0', 'daily');
        
        // صفحات ثابتة
        $pages = ['movies', 'series', 'search', 'about', 'contact'];
        foreach ($pages as $page) {
            $sitemap .= $this->addSitemapUrl($this->siteUrl . '/' . $page, '0.8', 'weekly');
        }
        
        // صفحات الأفلام
        $movies = $this->getAllMovies();
        foreach ($movies as $movie) {
            $url = $this->siteUrl . '/movie/' . $movie['id'] . '/' . $this->slugify($movie['title']);
            $sitemap .= $this->addSitemapUrl($url, '0.9', 'monthly');
        }
        
        // صفحات المسلسلات
        $series = $this->getAllSeries();
        foreach ($series as $serie) {
            $url = $this->siteUrl . '/series/' . $serie['id'] . '/' . $this->slugify($serie['title']);
            $sitemap .= $this->addSitemapUrl($url, '0.9', 'monthly');
        }
        
        $sitemap .= '</urlset>';
        
        return $sitemap;
    }
    
    /**
     * إضافة URL للـ Sitemap
     */
    private function addSitemapUrl($url, $priority, $changefreq) {
        $xml = "  <url>\n";
        $xml .= "    <loc>" . htmlspecialchars($url) . "</loc>\n";
        $xml .= "    <lastmod>" . date('Y-m-d') . "</lastmod>\n";
        $xml .= "    <changefreq>" . $changefreq . "</changefreq>\n";
        $xml .= "    <priority>" . $priority . "</priority>\n";
        $xml .= "  </url>\n";
        
        return $xml;
    }
    
    /**
     * إنشاء Robots.txt
     */
    public function generateRobotsTxt() {
        $robots = "User-agent: *\n";
        $robots .= "Allow: /\n";
        $robots .= "Disallow: /admin/\n";
        $robots .= "Disallow: /api/\n";
        $robots .= "Disallow: /uploads/\n";
        $robots .= "Disallow: /config/\n";
        $robots .= "\n";
        $robots .= "Sitemap: " . $this->siteUrl . "/sitemap.xml\n";
        
        return $robots;
    }
    
    /**
     * تحليل الكلمات المفتاحية
     */
    public function analyzeKeywords($content) {
        // إزالة الكلمات الشائعة
        $stopWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'التي', 'الذي', 'كان', 'كانت'];
        
        // تنظيف النص
        $content = strip_tags($content);
        $content = preg_replace('/[^\p{Arabic}\p{L}\s]/u', ' ', $content);
        $words = preg_split('/\s+/', $content);
        
        // إزالة الكلمات القصيرة والشائعة
        $keywords = array_filter($words, function($word) use ($stopWords) {
            return strlen($word) > 2 && !in_array($word, $stopWords);
        });
        
        // حساب تكرار الكلمات
        $wordCount = array_count_values($keywords);
        arsort($wordCount);
        
        return array_slice($wordCount, 0, 10);
    }
    
    /**
     * تحسين الصور للـ SEO
     */
    public function optimizeImageSEO($imagePath, $altText, $title = '') {
        return [
            'src' => $imagePath,
            'alt' => $altText,
            'title' => $title ?: $altText,
            'loading' => 'lazy',
            'decoding' => 'async'
        ];
    }
    
    /**
     * إنشاء Breadcrumbs
     */
    public function generateBreadcrumbs($pageType, $contentId = null) {
        $breadcrumbs = [
            [
                'name' => 'الرئيسية',
                'url' => $this->siteUrl
            ]
        ];
        
        switch ($pageType) {
            case 'movies':
                $breadcrumbs[] = [
                    'name' => 'الأفلام',
                    'url' => $this->siteUrl . '/movies'
                ];
                break;
                
            case 'movie':
                $breadcrumbs[] = [
                    'name' => 'الأفلام',
                    'url' => $this->siteUrl . '/movies'
                ];
                if ($contentId) {
                    $movie = $this->getMovieData($contentId);
                    if ($movie) {
                        $breadcrumbs[] = [
                            'name' => $movie['title'],
                            'url' => $this->siteUrl . '/movie/' . $contentId
                        ];
                    }
                }
                break;
                
            case 'series_list':
                $breadcrumbs[] = [
                    'name' => 'المسلسلات',
                    'url' => $this->siteUrl . '/series'
                ];
                break;
                
            case 'series':
                $breadcrumbs[] = [
                    'name' => 'المسلسلات',
                    'url' => $this->siteUrl . '/series'
                ];
                if ($contentId) {
                    $series = $this->getSeriesData($contentId);
                    if ($series) {
                        $breadcrumbs[] = [
                            'name' => $series['title'],
                            'url' => $this->siteUrl . '/series/' . $contentId
                        ];
                    }
                }
                break;
        }
        
        return $breadcrumbs;
    }
    
    /**
     * إنشاء Canonical URL
     */
    public function generateCanonicalUrl($pageType, $contentId = null) {
        switch ($pageType) {
            case 'movie':
                return $this->siteUrl . '/movie/' . $contentId;
            case 'series':
                return $this->siteUrl . '/series/' . $contentId;
            case 'movies':
                return $this->siteUrl . '/movies';
            case 'series_list':
                return $this->siteUrl . '/series';
            default:
                return $this->siteUrl;
        }
    }
    
    /**
     * الحصول على بيانات الفيلم
     */
    private function getMovieData($movieId) {
        $stmt = $this->pdo->prepare("SELECT * FROM movies WHERE id = ? AND status = 'active'");
        $stmt->execute([$movieId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * الحصول على بيانات المسلسل
     */
    private function getSeriesData($seriesId) {
        $stmt = $this->pdo->prepare("SELECT * FROM series WHERE id = ? AND status = 'active'");
        $stmt->execute([$seriesId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * الحصول على جميع الأفلام
     */
    private function getAllMovies() {
        $stmt = $this->pdo->query("SELECT id, title FROM movies WHERE status = 'active'");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * الحصول على جميع المسلسلات
     */
    private function getAllSeries() {
        $stmt = $this->pdo->query("SELECT id, title FROM series WHERE status = 'active'");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * تحويل النص إلى slug
     */
    private function slugify($text) {
        // تحويل الأحرف العربية إلى إنجليزية
        $arabic = ['ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'];
        $english = ['a', 'b', 't', 'th', 'j', 'h', 'kh', 'd', 'th', 'r', 'z', 's', 'sh', 's', 'd', 't', 'th', 'a', 'gh', 'f', 'q', 'k', 'l', 'm', 'n', 'h', 'w', 'y'];
        
        $text = str_replace($arabic, $english, $text);
        $text = preg_replace('/[^a-zA-Z0-9\s]/', '', $text);
        $text = preg_replace('/\s+/', '-', trim($text));
        $text = strtolower($text);
        
        return $text;
    }
}

// إنشاء ملفات SEO
if (isset($_GET['action'])) {
    $seo = new SEOSystem();
    
    switch ($_GET['action']) {
        case 'sitemap':
            header('Content-Type: application/xml');
            echo $seo->generateSitemap();
            break;
            
        case 'robots':
            header('Content-Type: text/plain');
            echo $seo->generateRobotsTxt();
            break;
            
        default:
            echo json_encode(['error' => 'Invalid action']);
    }
    exit;
}
?>
