<?php
/**
 * Shahid - Subscription Controller
 * Professional Video Streaming Platform
 */

require_once 'models/User.php';
require_once 'models/Subscription.php';
require_once 'models/Payment.php';

class SubscriptionController extends Controller {
    
    public function index() {
        try {
            $subscriptionModel = new Subscription();
            $plans = $subscriptionModel->getActivePlans();
            
            $currentSubscription = null;
            if ($this->auth->isLoggedIn()) {
                $user = $this->auth->getCurrentUser();
                $userModel = new User();
                $currentSubscription = $userModel->getCurrentSubscription($user['id']);
            }
            
            $data = [
                'page_title' => 'خطط الاشتراك - ' . $this->config['site']['name'],
                'plans' => $plans,
                'current_subscription' => $currentSubscription,
                'user' => $this->auth->getCurrentUser()
            ];
            
            $this->view('subscriptions/index', $data);
            
        } catch (Exception $e) {
            error_log('Subscription Index Error: ' . $e->getMessage());
            $this->view('errors/500', ['error' => 'Unable to load subscription plans']);
        }
    }
    
    public function subscribe() {
        $this->requireAuth();
        
        if ($_POST) {
            $this->validateCSRF();
            
            $planId = intval($_POST['plan_id'] ?? 0);
            $paymentMethod = $this->sanitizeInput($_POST['payment_method'] ?? '');
            
            if ($planId <= 0) {
                $this->redirect('/subscriptions?error=invalid_plan');
                return;
            }
            
            try {
                $subscriptionModel = new Subscription();
                $plan = $subscriptionModel->findById($planId);
                
                if (!$plan || $plan['status'] !== 'active') {
                    $this->redirect('/subscriptions?error=plan_not_found');
                    return;
                }
                
                $user = $this->auth->getCurrentUser();
                
                // Check if user already has an active subscription
                $userModel = new User();
                $currentSubscription = $userModel->getCurrentSubscription($user['id']);
                
                if ($currentSubscription && $currentSubscription['status'] === 'active') {
                    $this->redirect('/subscriptions?error=already_subscribed');
                    return;
                }
                
                // Create payment intent
                $paymentData = [
                    'user_id' => $user['id'],
                    'subscription_id' => $planId,
                    'amount' => $plan['price'],
                    'currency' => $plan['currency'],
                    'payment_method' => $paymentMethod,
                    'status' => 'pending'
                ];
                
                $paymentModel = new Payment();
                $paymentId = $paymentModel->create($paymentData);
                
                // Redirect to payment processor
                $this->processPayment($paymentId, $paymentMethod);
                
            } catch (Exception $e) {
                error_log('Subscription Error: ' . $e->getMessage());
                $this->redirect('/subscriptions?error=subscription_failed');
            }
        } else {
            $this->redirect('/subscriptions');
        }
    }
    
    public function cancel() {
        $this->requireAuth();
        
        if ($_POST) {
            $this->validateCSRF();
            
            try {
                $user = $this->auth->getCurrentUser();
                $userModel = new User();
                
                $result = $userModel->cancelSubscription($user['id']);
                
                if ($result) {
                    $_SESSION['flash_message'] = 'تم إلغاء الاشتراك بنجاح';
                    $_SESSION['flash_type'] = 'success';
                } else {
                    $_SESSION['flash_message'] = 'فشل في إلغاء الاشتراك';
                    $_SESSION['flash_type'] = 'error';
                }
                
            } catch (Exception $e) {
                error_log('Cancel Subscription Error: ' . $e->getMessage());
                $_SESSION['flash_message'] = 'حدث خطأ أثناء إلغاء الاشتراك';
                $_SESSION['flash_type'] = 'error';
            }
        }
        
        $this->redirect('/subscriptions');
    }
    
    public function upgrade() {
        $this->requireAuth();
        
        if ($_POST) {
            $this->validateCSRF();
            
            $newPlanId = intval($_POST['new_plan_id'] ?? 0);
            
            try {
                $user = $this->auth->getCurrentUser();
                $userModel = new User();
                $subscriptionModel = new Subscription();
                
                $currentSubscription = $userModel->getCurrentSubscription($user['id']);
                $newPlan = $subscriptionModel->findById($newPlanId);
                
                if (!$currentSubscription || !$newPlan) {
                    $this->redirect('/subscriptions?error=invalid_upgrade');
                    return;
                }
                
                // Calculate prorated amount
                $proratedAmount = $this->calculateProratedAmount($currentSubscription, $newPlan);
                
                if ($proratedAmount > 0) {
                    // Create payment for upgrade
                    $paymentData = [
                        'user_id' => $user['id'],
                        'subscription_id' => $newPlanId,
                        'amount' => $proratedAmount,
                        'currency' => $newPlan['currency'],
                        'payment_method' => $currentSubscription['payment_method'],
                        'status' => 'pending',
                        'type' => 'upgrade'
                    ];
                    
                    $paymentModel = new Payment();
                    $paymentId = $paymentModel->create($paymentData);
                    
                    $this->processPayment($paymentId, $currentSubscription['payment_method']);
                } else {
                    // Free upgrade (downgrade)
                    $result = $userModel->upgradeSubscription($user['id'], $newPlanId);
                    
                    if ($result) {
                        $_SESSION['flash_message'] = 'تم تحديث الاشتراك بنجاح';
                        $_SESSION['flash_type'] = 'success';
                    } else {
                        $_SESSION['flash_message'] = 'فشل في تحديث الاشتراك';
                        $_SESSION['flash_type'] = 'error';
                    }
                    
                    $this->redirect('/subscriptions');
                }
                
            } catch (Exception $e) {
                error_log('Upgrade Subscription Error: ' . $e->getMessage());
                $this->redirect('/subscriptions?error=upgrade_failed');
            }
        } else {
            $this->redirect('/subscriptions');
        }
    }
    
    public function paymentSuccess() {
        $paymentId = $_GET['payment_id'] ?? '';
        $sessionId = $_GET['session_id'] ?? '';
        
        if (empty($paymentId)) {
            $this->redirect('/subscriptions?error=invalid_payment');
            return;
        }
        
        try {
            $paymentModel = new Payment();
            $payment = $paymentModel->findById($paymentId);
            
            if (!$payment) {
                $this->redirect('/subscriptions?error=payment_not_found');
                return;
            }
            
            // Verify payment with payment processor
            $verified = $this->verifyPayment($payment, $sessionId);
            
            if ($verified) {
                // Update payment status
                $paymentModel->update($paymentId, ['status' => 'completed']);
                
                // Activate subscription
                $userModel = new User();
                $userModel->activateSubscription($payment['user_id'], $payment['subscription_id']);
                
                $_SESSION['flash_message'] = 'تم تفعيل الاشتراك بنجاح!';
                $_SESSION['flash_type'] = 'success';
                
                $this->redirect('/subscriptions');
            } else {
                $paymentModel->update($paymentId, ['status' => 'failed']);
                $this->redirect('/subscriptions?error=payment_verification_failed');
            }
            
        } catch (Exception $e) {
            error_log('Payment Success Error: ' . $e->getMessage());
            $this->redirect('/subscriptions?error=payment_processing_error');
        }
    }
    
    public function paymentCancel() {
        $paymentId = $_GET['payment_id'] ?? '';
        
        if (!empty($paymentId)) {
            try {
                $paymentModel = new Payment();
                $paymentModel->update($paymentId, ['status' => 'cancelled']);
            } catch (Exception $e) {
                error_log('Payment Cancel Error: ' . $e->getMessage());
            }
        }
        
        $_SESSION['flash_message'] = 'تم إلغاء عملية الدفع';
        $_SESSION['flash_type'] = 'warning';
        
        $this->redirect('/subscriptions');
    }
    
    private function processPayment($paymentId, $paymentMethod) {
        $paymentModel = new Payment();
        $payment = $paymentModel->findById($paymentId);
        
        switch ($paymentMethod) {
            case 'stripe':
                $this->processStripePayment($payment);
                break;
            case 'paypal':
                $this->processPayPalPayment($payment);
                break;
            case 'bank_transfer':
                $this->processBankTransfer($payment);
                break;
            default:
                throw new Exception('Unsupported payment method');
        }
    }
    
    private function processStripePayment($payment) {
        // Initialize Stripe
        require_once '../vendor/stripe/stripe-php/init.php';
        \Stripe\Stripe::setApiKey($this->config['stripe']['secret_key']);
        
        try {
            $session = \Stripe\Checkout\Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => $payment['currency'],
                        'product_data' => [
                            'name' => 'Shahid Subscription',
                        ],
                        'unit_amount' => $payment['amount'] * 100, // Convert to cents
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'payment',
                'success_url' => $this->config['site']['url'] . '/subscriptions/payment-success?payment_id=' . $payment['id'] . '&session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => $this->config['site']['url'] . '/subscriptions/payment-cancel?payment_id=' . $payment['id'],
                'metadata' => [
                    'payment_id' => $payment['id'],
                    'user_id' => $payment['user_id']
                ]
            ]);
            
            // Update payment with session ID
            $paymentModel = new Payment();
            $paymentModel->update($payment['id'], ['external_id' => $session->id]);
            
            // Redirect to Stripe Checkout
            header('Location: ' . $session->url);
            exit;
            
        } catch (\Stripe\Exception\ApiErrorException $e) {
            error_log('Stripe Error: ' . $e->getMessage());
            throw new Exception('Payment processing failed');
        }
    }
    
    private function processPayPalPayment($payment) {
        // PayPal integration would go here
        // For now, redirect to a placeholder
        $this->redirect('/subscriptions?error=paypal_not_implemented');
    }
    
    private function processBankTransfer($payment) {
        // Bank transfer instructions
        $this->redirect('/subscriptions/bank-transfer?payment_id=' . $payment['id']);
    }
    
    private function verifyPayment($payment, $sessionId) {
        switch ($payment['payment_method']) {
            case 'stripe':
                return $this->verifyStripePayment($payment, $sessionId);
            case 'paypal':
                return $this->verifyPayPalPayment($payment, $sessionId);
            default:
                return false;
        }
    }
    
    private function verifyStripePayment($payment, $sessionId) {
        require_once '../vendor/stripe/stripe-php/init.php';
        \Stripe\Stripe::setApiKey($this->config['stripe']['secret_key']);
        
        try {
            $session = \Stripe\Checkout\Session::retrieve($sessionId);
            
            return $session->payment_status === 'paid' && 
                   $session->metadata['payment_id'] == $payment['id'];
                   
        } catch (\Stripe\Exception\ApiErrorException $e) {
            error_log('Stripe Verification Error: ' . $e->getMessage());
            return false;
        }
    }
    
    private function verifyPayPalPayment($payment, $sessionId) {
        // PayPal verification would go here
        return false;
    }
    
    private function calculateProratedAmount($currentSubscription, $newPlan) {
        $currentPlan = (new Subscription())->findById($currentSubscription['subscription_id']);
        
        if (!$currentPlan) {
            return $newPlan['price'];
        }
        
        // Calculate remaining days in current subscription
        $now = new DateTime();
        $expiresAt = new DateTime($currentSubscription['expires_at']);
        $remainingDays = $now->diff($expiresAt)->days;
        
        // Calculate daily rates
        $currentDailyRate = $currentPlan['price'] / 30; // Assuming monthly billing
        $newDailyRate = $newPlan['price'] / 30;
        
        // Calculate prorated amount
        $refund = $remainingDays * $currentDailyRate;
        $newCharge = $remainingDays * $newDailyRate;
        
        return max(0, $newCharge - $refund);
    }
}
?>
