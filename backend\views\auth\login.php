<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title ?? 'تسجيل الدخول - Shahid') ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="/assets/css/auth.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="auth-body">
    
    <!-- Background Video -->
    <div class="auth-background">
        <video autoplay muted loop>
            <source src="/assets/videos/auth-bg.mp4" type="video/mp4">
        </video>
        <div class="auth-overlay"></div>
    </div>
    
    <!-- Navigation -->
    <nav class="auth-navbar">
        <div class="container">
            <a href="/" class="navbar-brand">
                <img src="/assets/images/logo.png" alt="Shahid" height="40">
            </a>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="auth-card">
                        <div class="auth-header">
                            <h2>تسجيل الدخول</h2>
                            <p>مرحباً بك مرة أخرى في Shahid</p>
                        </div>
                        
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?= htmlspecialchars($error) ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="auth-form">
                            <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                            
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" 
                                           class="form-control" 
                                           id="email" 
                                           name="email" 
                                           placeholder="أدخل بريدك الإلكتروني"
                                           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>"
                                           required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="password">كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" 
                                           class="form-control" 
                                           id="password" 
                                           name="password" 
                                           placeholder="أدخل كلمة المرور"
                                           required>
                                    <button type="button" class="btn btn-outline-secondary toggle-password">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                    <label class="form-check-label" for="remember">
                                        تذكرني
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </form>
                        
                        <div class="auth-divider">
                            <span>أو</span>
                        </div>
                        
                        <div class="social-login">
                            <button class="btn btn-social btn-google">
                                <i class="fab fa-google me-2"></i>
                                تسجيل الدخول بـ Google
                            </button>
                            <button class="btn btn-social btn-facebook">
                                <i class="fab fa-facebook-f me-2"></i>
                                تسجيل الدخول بـ Facebook
                            </button>
                        </div>
                        
                        <div class="auth-footer">
                            <div class="text-center">
                                <a href="/forgot-password" class="forgot-link">
                                    نسيت كلمة المرور؟
                                </a>
                            </div>
                            <div class="text-center mt-3">
                                <span>ليس لديك حساب؟</span>
                                <a href="/register" class="register-link">إنشاء حساب جديد</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/jquery.min.js"></script>
    
    <script>
        // Toggle password visibility
        $('.toggle-password').click(function() {
            const passwordInput = $('#password');
            const icon = $(this).find('i');
            
            if (passwordInput.attr('type') === 'password') {
                passwordInput.attr('type', 'text');
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                passwordInput.attr('type', 'password');
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });
        
        // Form validation
        $('.auth-form').on('submit', function(e) {
            const email = $('#email').val();
            const password = $('#password').val();
            
            if (!email || !password) {
                e.preventDefault();
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return false;
            }
            
            if (!isValidEmail(email)) {
                e.preventDefault();
                showAlert('يرجى إدخال بريد إلكتروني صحيح', 'danger');
                return false;
            }
            
            // Show loading
            const submitBtn = $(this).find('button[type="submit"]');
            submitBtn.prop('disabled', true);
            submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...');
        });
        
        // Social login handlers
        $('.btn-google').click(function() {
            // Implement Google OAuth
            window.location.href = '/auth/google';
        });
        
        $('.btn-facebook').click(function() {
            // Implement Facebook OAuth
            window.location.href = '/auth/facebook';
        });
        
        // Helper functions
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            $('.auth-form').before(alertHtml);
            
            // Auto hide after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        }
        
        // Auto-focus first input
        $(document).ready(function() {
            $('#email').focus();
        });
        
        // Enter key navigation
        $('#email').keypress(function(e) {
            if (e.which === 13) {
                $('#password').focus();
            }
        });
        
        $('#password').keypress(function(e) {
            if (e.which === 13) {
                $('.auth-form').submit();
            }
        });
    </script>
    
</body>
</html>
