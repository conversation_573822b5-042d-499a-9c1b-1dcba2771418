<?php
/**
 * Shahid Admin Panel
 * Professional Video Streaming Platform
 */

session_start();

// Check if installation is complete
if (!file_exists('../config/installed.lock')) {
    header('Location: ../install_simple.php');
    exit();
}

// Check if user is logged in as admin
$isLoggedIn = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;

// Handle login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    // Simple admin credentials (in production, use database)
    if ($username === 'admin' && $password === 'admin123') {
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_username'] = $username;
        header('Location: index.php');
        exit();
    } else {
        $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit();
}

// Get database connection
try {
    $config = include '../config/database.php';
    $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", 
                   $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    $dbError = 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
}

// Get statistics if logged in
$stats = [];
if ($isLoggedIn && !isset($dbError)) {
    try {
        // Get movies count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM movies");
        $stats['movies'] = $stmt->fetch()['count'];
        
        // Get series count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM series");
        $stats['series'] = $stmt->fetch()['count'];
        
        // Get users count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $stats['users'] = $stmt->fetch()['count'];
        
        // Get episodes count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM episodes");
        $stats['episodes'] = $stmt->fetch()['count'];
        
        // Get recent movies
        $stmt = $pdo->query("SELECT title, created_at FROM movies ORDER BY created_at DESC LIMIT 5");
        $stats['recent_movies'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get recent users
        $stmt = $pdo->query("SELECT name, email, created_at FROM users ORDER BY created_at DESC LIMIT 5");
        $stats['recent_users'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        $statsError = 'خطأ في جلب الإحصائيات: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة إدارة Shahid</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .header {
            background: #E50914;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .header h1 {
            display: inline-block;
            font-size: 2rem;
            font-weight: bold;
        }
        
        .header .user-info {
            float: left;
            margin-top: 0.5rem;
        }
        
        .header .user-info a {
            color: #fff;
            text-decoration: none;
            margin-left: 1rem;
            padding: 0.5rem 1rem;
            border: 1px solid #fff;
            border-radius: 5px;
            transition: all 0.3s;
        }
        
        .header .user-info a:hover {
            background: #fff;
            color: #E50914;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .login-form {
            max-width: 400px;
            margin: 5rem auto;
            background: #2d2d2d;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        
        .login-form h2 {
            text-align: center;
            margin-bottom: 2rem;
            color: #E50914;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #555;
            border-radius: 5px;
            background: #1a1a1a;
            color: #fff;
            font-size: 1rem;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #E50914;
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: #E50914;
            color: #fff;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #b8070f;
        }
        
        .error {
            background: #dc3545;
            color: #fff;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: #2d2d2d;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            transition: transform 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #E50914;
        }
        
        .stat-card .number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-card .label {
            font-size: 1.1rem;
            color: #ccc;
        }
        
        .recent-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .recent-card {
            background: #2d2d2d;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .recent-card h3 {
            color: #E50914;
            margin-bottom: 1.5rem;
            font-size: 1.3rem;
        }
        
        .recent-item {
            padding: 1rem;
            border-bottom: 1px solid #555;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .recent-item:last-child {
            border-bottom: none;
        }
        
        .recent-item .name {
            font-weight: bold;
        }
        
        .recent-item .date {
            color: #ccc;
            font-size: 0.9rem;
        }
        
        .nav-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .nav-link {
            display: block;
            background: #E50914;
            color: #fff;
            text-decoration: none;
            padding: 1rem;
            border-radius: 5px;
            text-align: center;
            transition: background 0.3s;
        }
        
        .nav-link:hover {
            background: #b8070f;
        }
        
        @media (max-width: 768px) {
            .recent-section {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 0 1rem;
            }
        }
    </style>
</head>
<body>
    <?php if (!$isLoggedIn): ?>
        <!-- Login Form -->
        <div class="login-form">
            <h2>🔐 تسجيل دخول الإدارة</h2>
            
            <?php if (isset($error)): ?>
                <div class="error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>
            
            <?php if (isset($dbError)): ?>
                <div class="error"><?php echo htmlspecialchars($dbError); ?></div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" name="login" class="btn">تسجيل الدخول</button>
            </form>
            
            <div style="margin-top: 2rem; text-align: center; color: #ccc;">
                <p><strong>بيانات الدخول التجريبية:</strong></p>
                <p>اسم المستخدم: admin</p>
                <p>كلمة المرور: admin123</p>
            </div>
        </div>
    <?php else: ?>
        <!-- Admin Dashboard -->
        <div class="header">
            <h1>🎬 لوحة إدارة Shahid</h1>
            <div class="user-info">
                <span>مرحباً، <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                <a href="?logout=1">تسجيل الخروج</a>
            </div>
            <div style="clear: both;"></div>
        </div>
        
        <div class="container">
            <?php if (isset($dbError)): ?>
                <div class="error"><?php echo htmlspecialchars($dbError); ?></div>
            <?php elseif (isset($statsError)): ?>
                <div class="error"><?php echo htmlspecialchars($statsError); ?></div>
            <?php else: ?>
                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="icon">🎬</div>
                        <div class="number"><?php echo number_format($stats['movies']); ?></div>
                        <div class="label">الأفلام</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="icon">📺</div>
                        <div class="number"><?php echo number_format($stats['series']); ?></div>
                        <div class="label">المسلسلات</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="icon">🎭</div>
                        <div class="number"><?php echo number_format($stats['episodes']); ?></div>
                        <div class="label">الحلقات</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="icon">👥</div>
                        <div class="number"><?php echo number_format($stats['users']); ?></div>
                        <div class="label">المستخدمون</div>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="recent-section">
                    <div class="recent-card">
                        <h3>🎬 أحدث الأفلام</h3>
                        <?php if (!empty($stats['recent_movies'])): ?>
                            <?php foreach ($stats['recent_movies'] as $movie): ?>
                                <div class="recent-item">
                                    <div class="name"><?php echo htmlspecialchars($movie['title']); ?></div>
                                    <div class="date"><?php echo date('Y-m-d', strtotime($movie['created_at'])); ?></div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p style="color: #ccc; text-align: center;">لا توجد أفلام</p>
                        <?php endif; ?>
                    </div>
                    
                    <div class="recent-card">
                        <h3>👥 أحدث المستخدمين</h3>
                        <?php if (!empty($stats['recent_users'])): ?>
                            <?php foreach ($stats['recent_users'] as $user): ?>
                                <div class="recent-item">
                                    <div>
                                        <div class="name"><?php echo htmlspecialchars($user['name']); ?></div>
                                        <div style="font-size: 0.8rem; color: #999;"><?php echo htmlspecialchars($user['email']); ?></div>
                                    </div>
                                    <div class="date"><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p style="color: #ccc; text-align: center;">لا يوجد مستخدمون</p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Navigation Links -->
            <div class="nav-links">
                <a href="movies.php" class="nav-link">🎬 إدارة الأفلام</a>
                <a href="series.php" class="nav-link">📺 إدارة المسلسلات</a>
                <a href="users.php" class="nav-link">👥 إدارة المستخدمين</a>
                <a href="settings.php" class="nav-link">⚙️ الإعدادات</a>
                <a href="../api/test_api.php" class="nav-link" target="_blank">🔗 اختبار API</a>
                <a href="../index_simple.php" class="nav-link" target="_blank">🏠 الموقع الرئيسي</a>
            </div>
        </div>
    <?php endif; ?>
</body>
</html>
