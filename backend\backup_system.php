<?php
/**
 * نظام النسخ الاحتياطي التلقائي
 * يقوم بإنشاء نسخ احتياطية من قاعدة البيانات والملفات
 */

class BackupSystem {
    private $dbConfig;
    private $backupPath;
    private $maxBackups;
    
    public function __construct() {
        $this->dbConfig = [
            'host' => 'localhost',
            'username' => 'root',
            'password' => '',
            'database' => 'shahid_platform'
        ];
        
        $this->backupPath = __DIR__ . '/backups/';
        $this->maxBackups = 10; // الاحتفاظ بآخر 10 نسخ احتياطية
        
        // إنشاء مجلد النسخ الاحتياطية
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
    }
    
    /**
     * إنشاء نسخة احتياطية كاملة
     */
    public function createFullBackup() {
        $timestamp = date('Y-m-d_H-i-s');
        $backupDir = $this->backupPath . "backup_{$timestamp}/";
        
        if (!mkdir($backupDir, 0755, true)) {
            throw new Exception('فشل في إنشاء مجلد النسخة الاحتياطية');
        }
        
        $results = [
            'timestamp' => $timestamp,
            'database' => $this->backupDatabase($backupDir),
            'files' => $this->backupFiles($backupDir),
            'size' => 0,
            'status' => 'success'
        ];
        
        // حساب حجم النسخة الاحتياطية
        $results['size'] = $this->calculateDirectorySize($backupDir);
        
        // إنشاء ملف معلومات النسخة الاحتياطية
        $this->createBackupInfo($backupDir, $results);
        
        // ضغط النسخة الاحتياطية
        $zipFile = $this->compressBackup($backupDir, $timestamp);
        if ($zipFile) {
            $results['zip_file'] = $zipFile;
            $results['zip_size'] = filesize($zipFile);
            
            // حذف المجلد غير المضغوط
            $this->deleteDirectory($backupDir);
        }
        
        // تنظيف النسخ القديمة
        $this->cleanOldBackups();
        
        return $results;
    }
    
    /**
     * نسخ احتياطي لقاعدة البيانات
     */
    private function backupDatabase($backupDir) {
        $filename = $backupDir . 'database_' . date('Y-m-d_H-i-s') . '.sql';
        
        try {
            $pdo = new PDO(
                "mysql:host={$this->dbConfig['host']};dbname={$this->dbConfig['database']};charset=utf8mb4",
                $this->dbConfig['username'],
                $this->dbConfig['password']
            );
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $sql = "-- نسخة احتياطية من قاعدة بيانات Shahid\n";
            $sql .= "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n\n";
            $sql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
            
            // الحصول على قائمة الجداول
            $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
            
            foreach ($tables as $table) {
                $sql .= $this->exportTable($pdo, $table);
            }
            
            $sql .= "\nSET FOREIGN_KEY_CHECKS=1;\n";
            
            if (file_put_contents($filename, $sql) === false) {
                throw new Exception('فشل في كتابة ملف قاعدة البيانات');
            }
            
            return [
                'status' => 'success',
                'filename' => basename($filename),
                'size' => filesize($filename),
                'tables_count' => count($tables)
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * تصدير جدول واحد
     */
    private function exportTable($pdo, $table) {
        $sql = "\n-- جدول: {$table}\n";
        
        // هيكل الجدول
        $createTable = $pdo->query("SHOW CREATE TABLE `{$table}`")->fetch();
        $sql .= "DROP TABLE IF EXISTS `{$table}`;\n";
        $sql .= $createTable['Create Table'] . ";\n\n";
        
        // بيانات الجدول
        $rows = $pdo->query("SELECT * FROM `{$table}`")->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            $columns = array_keys($rows[0]);
            $sql .= "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES\n";
            
            $values = [];
            foreach ($rows as $row) {
                $rowValues = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $rowValues[] = 'NULL';
                    } else {
                        $rowValues[] = "'" . addslashes($value) . "'";
                    }
                }
                $values[] = '(' . implode(', ', $rowValues) . ')';
            }
            
            $sql .= implode(",\n", $values) . ";\n";
        }
        
        return $sql;
    }
    
    /**
     * نسخ احتياطي للملفات
     */
    private function backupFiles($backupDir) {
        $filesDir = $backupDir . 'files/';
        if (!mkdir($filesDir, 0755, true)) {
            return ['status' => 'error', 'error' => 'فشل في إنشاء مجلد الملفات'];
        }
        
        $sourceDirectories = [
            'uploads' => __DIR__ . '/uploads/',
            'logs' => __DIR__ . '/logs/',
            'temp' => __DIR__ . '/temp/',
            'config' => __DIR__ . '/config/'
        ];
        
        $results = [
            'status' => 'success',
            'directories' => [],
            'total_files' => 0,
            'total_size' => 0
        ];
        
        foreach ($sourceDirectories as $name => $path) {
            if (is_dir($path)) {
                $targetPath = $filesDir . $name . '/';
                $result = $this->copyDirectory($path, $targetPath);
                $results['directories'][$name] = $result;
                $results['total_files'] += $result['files_count'];
                $results['total_size'] += $result['size'];
            }
        }
        
        return $results;
    }
    
    /**
     * نسخ مجلد بالكامل
     */
    private function copyDirectory($source, $destination) {
        if (!is_dir($source)) {
            return ['status' => 'error', 'error' => 'المجلد المصدر غير موجود'];
        }
        
        if (!mkdir($destination, 0755, true)) {
            return ['status' => 'error', 'error' => 'فشل في إنشاء المجلد الهدف'];
        }
        
        $filesCount = 0;
        $totalSize = 0;
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $target = $destination . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                if (!is_dir($target)) {
                    mkdir($target, 0755, true);
                }
            } else {
                if (copy($item, $target)) {
                    $filesCount++;
                    $totalSize += filesize($item);
                }
            }
        }
        
        return [
            'status' => 'success',
            'files_count' => $filesCount,
            'size' => $totalSize
        ];
    }
    
    /**
     * ضغط النسخة الاحتياطية
     */
    private function compressBackup($backupDir, $timestamp) {
        if (!class_exists('ZipArchive')) {
            return false;
        }
        
        $zipFile = $this->backupPath . "shahid_backup_{$timestamp}.zip";
        $zip = new ZipArchive();
        
        if ($zip->open($zipFile, ZipArchive::CREATE) !== TRUE) {
            return false;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($backupDir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $relativePath = substr($file->getPathname(), strlen($backupDir));
                $zip->addFile($file->getPathname(), $relativePath);
            }
        }
        
        $zip->close();
        
        return $zipFile;
    }
    
    /**
     * إنشاء ملف معلومات النسخة الاحتياطية
     */
    private function createBackupInfo($backupDir, $results) {
        $info = [
            'backup_info' => [
                'version' => '1.0',
                'platform' => 'Shahid',
                'created_at' => date('Y-m-d H:i:s'),
                'php_version' => PHP_VERSION,
                'server' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
            ],
            'database' => $results['database'],
            'files' => $results['files'],
            'total_size' => $results['size']
        ];
        
        file_put_contents(
            $backupDir . 'backup_info.json',
            json_encode($info, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)
        );
    }
    
    /**
     * حساب حجم المجلد
     */
    private function calculateDirectorySize($directory) {
        $size = 0;
        
        if (is_dir($directory)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $size += $file->getSize();
                }
            }
        }
        
        return $size;
    }
    
    /**
     * حذف مجلد بالكامل
     */
    private function deleteDirectory($directory) {
        if (!is_dir($directory)) {
            return false;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isDir()) {
                rmdir($file->getPathname());
            } else {
                unlink($file->getPathname());
            }
        }
        
        return rmdir($directory);
    }
    
    /**
     * تنظيف النسخ الاحتياطية القديمة
     */
    private function cleanOldBackups() {
        $backups = glob($this->backupPath . 'shahid_backup_*.zip');
        
        if (count($backups) > $this->maxBackups) {
            // ترتيب حسب تاريخ التعديل
            usort($backups, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // حذف النسخ الأقدم
            $toDelete = array_slice($backups, 0, count($backups) - $this->maxBackups);
            foreach ($toDelete as $backup) {
                unlink($backup);
            }
        }
    }
    
    /**
     * الحصول على قائمة النسخ الاحتياطية
     */
    public function getBackupsList() {
        $backups = glob($this->backupPath . 'shahid_backup_*.zip');
        $list = [];
        
        foreach ($backups as $backup) {
            $filename = basename($backup);
            $timestamp = str_replace(['shahid_backup_', '.zip'], '', $filename);
            
            $list[] = [
                'filename' => $filename,
                'timestamp' => $timestamp,
                'date' => date('Y-m-d H:i:s', filemtime($backup)),
                'size' => filesize($backup),
                'size_formatted' => $this->formatBytes(filesize($backup))
            ];
        }
        
        // ترتيب حسب التاريخ (الأحدث أولاً)
        usort($list, function($a, $b) {
            return strcmp($b['timestamp'], $a['timestamp']);
        });
        
        return $list;
    }
    
    /**
     * استعادة نسخة احتياطية
     */
    public function restoreBackup($backupFile) {
        $backupPath = $this->backupPath . $backupFile;
        
        if (!file_exists($backupPath)) {
            throw new Exception('ملف النسخة الاحتياطية غير موجود');
        }
        
        if (!class_exists('ZipArchive')) {
            throw new Exception('مكتبة ZipArchive غير متوفرة');
        }
        
        $zip = new ZipArchive();
        if ($zip->open($backupPath) !== TRUE) {
            throw new Exception('فشل في فتح ملف النسخة الاحتياطية');
        }
        
        $tempDir = $this->backupPath . 'temp_restore_' . time() . '/';
        
        if (!$zip->extractTo($tempDir)) {
            throw new Exception('فشل في استخراج النسخة الاحتياطية');
        }
        
        $zip->close();
        
        // استعادة قاعدة البيانات
        $sqlFiles = glob($tempDir . 'database_*.sql');
        if (!empty($sqlFiles)) {
            $this->restoreDatabase($sqlFiles[0]);
        }
        
        // استعادة الملفات
        $filesDir = $tempDir . 'files/';
        if (is_dir($filesDir)) {
            $this->restoreFiles($filesDir);
        }
        
        // تنظيف المجلد المؤقت
        $this->deleteDirectory($tempDir);
        
        return [
            'status' => 'success',
            'message' => 'تم استعادة النسخة الاحتياطية بنجاح'
        ];
    }
    
    /**
     * استعادة قاعدة البيانات
     */
    private function restoreDatabase($sqlFile) {
        $pdo = new PDO(
            "mysql:host={$this->dbConfig['host']};dbname={$this->dbConfig['database']};charset=utf8mb4",
            $this->dbConfig['username'],
            $this->dbConfig['password']
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $sql = file_get_contents($sqlFile);
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
    }
    
    /**
     * استعادة الملفات
     */
    private function restoreFiles($filesDir) {
        $directories = [
            'uploads' => __DIR__ . '/uploads/',
            'logs' => __DIR__ . '/logs/',
            'temp' => __DIR__ . '/temp/',
            'config' => __DIR__ . '/config/'
        ];
        
        foreach ($directories as $name => $targetPath) {
            $sourcePath = $filesDir . $name . '/';
            if (is_dir($sourcePath)) {
                // حذف المجلد الحالي
                if (is_dir($targetPath)) {
                    $this->deleteDirectory($targetPath);
                }
                
                // نسخ الملفات المستعادة
                $this->copyDirectory($sourcePath, $targetPath);
            }
        }
    }
    
    /**
     * تنسيق حجم الملف
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

// معالجة طلبات API
if ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'GET') {
    header('Content-Type: application/json');
    
    $action = $_REQUEST['action'] ?? '';
    
    if ($action) {
        $backup = new BackupSystem();
        
        try {
            switch ($action) {
                case 'create_backup':
                    $result = $backup->createFullBackup();
                    echo json_encode([
                        'success' => true,
                        'data' => $result,
                        'message' => 'تم إنشاء النسخة الاحتياطية بنجاح'
                    ]);
                    break;
                    
                case 'list_backups':
                    $backups = $backup->getBackupsList();
                    echo json_encode([
                        'success' => true,
                        'data' => $backups,
                        'count' => count($backups)
                    ]);
                    break;
                    
                case 'restore_backup':
                    $backupFile = $_POST['backup_file'] ?? '';
                    if (empty($backupFile)) {
                        throw new Exception('اسم ملف النسخة الاحتياطية مطلوب');
                    }
                    
                    $result = $backup->restoreBackup($backupFile);
                    echo json_encode($result);
                    break;
                    
                default:
                    echo json_encode([
                        'success' => false,
                        'error' => 'إجراء غير صالح'
                    ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
        exit;
    }
}
?>
