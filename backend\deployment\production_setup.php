<?php
/**
 * Shahid Platform - Production Deployment Setup
 * Complete Production Environment Configuration
 */

// Production Configuration
class ProductionSetup {
    private $config = [
        'database' => [
            'host' => 'localhost',
            'dbname' => 'shahid_platform',
            'username' => 'shahid_user',
            'password' => '', // Set in environment
            'charset' => 'utf8mb4',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]
        ],
        'security' => [
            'jwt_secret' => '', // Set in environment
            'encryption_key' => '', // Set in environment
            'session_lifetime' => 1800, // 30 minutes
            'password_min_length' => 8,
            'max_login_attempts' => 5,
            'lockout_duration' => 900 // 15 minutes
        ],
        'file_upload' => [
            'max_file_size' => 500 * 1024 * 1024, // 500MB
            'upload_path' => '/var/www/uploads/',
            'allowed_types' => ['mp4', 'webm', 'jpg', 'jpeg', 'png', 'gif'],
            'virus_scan' => true
        ],
        'email' => [
            'smtp_host' => 'smtp.gmail.com',
            'smtp_port' => 587,
            'smtp_username' => '', // Set in environment
            'smtp_password' => '', // Set in environment
            'from_email' => '<EMAIL>',
            'from_name' => 'Shahid Platform'
        ],
        'cdn' => [
            'enabled' => true,
            'base_url' => 'https://cdn.shahidplatform.com',
            'api_key' => '', // Set in environment
            'regions' => ['us-east-1', 'eu-west-1', 'ap-southeast-1']
        ],
        'cache' => [
            'enabled' => true,
            'driver' => 'redis',
            'host' => 'localhost',
            'port' => 6379,
            'ttl' => 3600 // 1 hour
        ],
        'monitoring' => [
            'error_reporting' => false,
            'log_level' => 'error',
            'log_path' => '/var/log/shahid/',
            'metrics_enabled' => true,
            'health_check_url' => '/health'
        ]
    ];
    
    public function __construct() {
        $this->loadEnvironmentVariables();
    }
    
    private function loadEnvironmentVariables() {
        // Load from .env file or environment variables
        $envFile = __DIR__ . '/../.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                    list($key, $value) = explode('=', $line, 2);
                    $_ENV[trim($key)] = trim($value);
                }
            }
        }
        
        // Set configuration from environment
        $this->config['database']['password'] = $_ENV['DB_PASSWORD'] ?? '';
        $this->config['security']['jwt_secret'] = $_ENV['JWT_SECRET'] ?? $this->generateSecret();
        $this->config['security']['encryption_key'] = $_ENV['ENCRYPTION_KEY'] ?? $this->generateSecret();
        $this->config['email']['smtp_username'] = $_ENV['SMTP_USERNAME'] ?? '';
        $this->config['email']['smtp_password'] = $_ENV['SMTP_PASSWORD'] ?? '';
        $this->config['cdn']['api_key'] = $_ENV['CDN_API_KEY'] ?? '';
    }
    
    private function generateSecret($length = 64) {
        return bin2hex(random_bytes($length / 2));
    }
    
    public function setupProduction() {
        echo "🚀 Setting up Shahid Platform for Production...\n\n";
        
        $this->checkSystemRequirements();
        $this->setupDatabase();
        $this->configureWebServer();
        $this->setupSSL();
        $this->configureFirewall();
        $this->setupMonitoring();
        $this->optimizePerformance();
        $this->setupBackups();
        $this->runSecurityChecks();
        
        echo "✅ Production setup completed successfully!\n";
        echo "🌐 Your Shahid Platform is ready for production use.\n\n";
        
        $this->displayPostSetupInstructions();
    }
    
    private function checkSystemRequirements() {
        echo "📋 Checking system requirements...\n";
        
        $requirements = [
            'PHP Version' => version_compare(PHP_VERSION, '7.4.0', '>='),
            'PDO Extension' => extension_loaded('pdo'),
            'PDO MySQL' => extension_loaded('pdo_mysql'),
            'OpenSSL' => extension_loaded('openssl'),
            'cURL' => extension_loaded('curl'),
            'GD Extension' => extension_loaded('gd'),
            'Fileinfo' => extension_loaded('fileinfo'),
            'JSON' => extension_loaded('json'),
            'Mbstring' => extension_loaded('mbstring')
        ];
        
        foreach ($requirements as $requirement => $met) {
            echo "  " . ($met ? "✅" : "❌") . " $requirement\n";
            if (!$met) {
                throw new Exception("System requirement not met: $requirement");
            }
        }
        
        echo "✅ All system requirements met.\n\n";
    }
    
    private function setupDatabase() {
        echo "🗄️ Setting up production database...\n";
        
        try {
            $pdo = new PDO(
                "mysql:host={$this->config['database']['host']};charset={$this->config['database']['charset']}",
                $this->config['database']['username'],
                $this->config['database']['password'],
                $this->config['database']['options']
            );
            
            // Create database if not exists
            $pdo->exec("CREATE DATABASE IF NOT EXISTS {$this->config['database']['dbname']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE {$this->config['database']['dbname']}");
            
            // Run schema
            $schema = file_get_contents(__DIR__ . '/../database/schema.sql');
            $pdo->exec($schema);
            
            // Insert production data
            $productionData = file_get_contents(__DIR__ . '/../database/production_data.sql');
            $pdo->exec($productionData);
            
            // Create indexes for performance
            $this->createDatabaseIndexes($pdo);
            
            echo "✅ Database setup completed.\n\n";
            
        } catch (Exception $e) {
            throw new Exception("Database setup failed: " . $e->getMessage());
        }
    }
    
    private function createDatabaseIndexes($pdo) {
        $indexes = [
            "CREATE INDEX idx_users_email ON users(email)",
            "CREATE INDEX idx_users_subscription ON users(subscription_type, subscription_end)",
            "CREATE INDEX idx_movies_status ON movies(status)",
            "CREATE INDEX idx_movies_genre ON movies(genre)",
            "CREATE INDEX idx_movies_rating ON movies(rating)",
            "CREATE INDEX idx_series_status ON series(status)",
            "CREATE INDEX idx_watch_history_user ON watch_history(user_id)",
            "CREATE INDEX idx_watch_history_date ON watch_history(created_at)",
            "CREATE INDEX idx_ratings_content ON ratings(movie_id, series_id)",
            "CREATE INDEX idx_payments_status ON payments(status, created_at)",
            "CREATE INDEX idx_security_logs_type ON security_logs(event_type, created_at)"
        ];
        
        foreach ($indexes as $index) {
            try {
                $pdo->exec($index);
            } catch (Exception $e) {
                // Index might already exist, continue
            }
        }
    }
    
    private function configureWebServer() {
        echo "🌐 Configuring web server...\n";
        
        // Apache configuration
        $htaccess = "
# Shahid Platform - Production .htaccess
RewriteEngine On

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection \"1; mode=block\"
Header always set Strict-Transport-Security \"max-age=31536000; includeSubDomains\"
Header always set Referrer-Policy \"strict-origin-when-cross-origin\"

# HTTPS Redirect
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# API Routes
RewriteRule ^api/(.*)$ backend/api/index.php [QSA,L]

# Admin Routes
RewriteRule ^admin/(.*)$ backend/admin/index.php [QSA,L]

# Streaming Routes
RewriteRule ^watch/(.*)$ backend/streaming/video_player.php [QSA,L]

# Block access to sensitive files
<FilesMatch \"\\.(env|log|sql|md)$\">
    Order allow,deny
    Deny from all
</FilesMatch>

# Gzip Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css \"access plus 1 year\"
    ExpiresByType application/javascript \"access plus 1 year\"
    ExpiresByType image/png \"access plus 1 year\"
    ExpiresByType image/jpg \"access plus 1 year\"
    ExpiresByType image/jpeg \"access plus 1 year\"
    ExpiresByType image/gif \"access plus 1 year\"
    ExpiresByType image/webp \"access plus 1 year\"
</IfModule>
";
        
        file_put_contents(__DIR__ . '/../../.htaccess', $htaccess);
        echo "✅ Web server configuration completed.\n\n";
    }
    
    private function setupSSL() {
        echo "🔒 Setting up SSL certificate...\n";
        
        // Instructions for SSL setup
        echo "  📝 SSL Setup Instructions:\n";
        echo "  1. Install Certbot: sudo apt-get install certbot python3-certbot-apache\n";
        echo "  2. Get certificate: sudo certbot --apache -d yourdomain.com\n";
        echo "  3. Test renewal: sudo certbot renew --dry-run\n";
        echo "  4. Setup auto-renewal: sudo crontab -e\n";
        echo "     Add: 0 12 * * * /usr/bin/certbot renew --quiet\n\n";
    }
    
    private function configureFirewall() {
        echo "🛡️ Configuring firewall...\n";
        
        $firewallRules = "
# UFW Firewall Rules for Shahid Platform
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3306/tcp from 127.0.0.1
sudo ufw enable
";
        
        file_put_contents(__DIR__ . '/firewall_setup.sh', $firewallRules);
        chmod(__DIR__ . '/firewall_setup.sh', 0755);
        
        echo "  📝 Firewall rules saved to firewall_setup.sh\n";
        echo "  Run: sudo bash firewall_setup.sh\n\n";
    }
    
    private function setupMonitoring() {
        echo "📊 Setting up monitoring...\n";
        
        // Create health check endpoint
        $healthCheck = "<?php
header('Content-Type: application/json');

\$health = [
    'status' => 'healthy',
    'timestamp' => date('c'),
    'version' => '1.0.0',
    'checks' => []
];

// Database check
try {
    \$pdo = new PDO('mysql:host=localhost;dbname=shahid_platform', 'shahid_user', '');
    \$pdo->query('SELECT 1');
    \$health['checks']['database'] = 'ok';
} catch (Exception \$e) {
    \$health['checks']['database'] = 'error';
    \$health['status'] = 'unhealthy';
}

// Disk space check
\$freeBytes = disk_free_space('.');
\$totalBytes = disk_total_space('.');
\$usedPercent = ((\$totalBytes - \$freeBytes) / \$totalBytes) * 100;

if (\$usedPercent > 90) {
    \$health['checks']['disk_space'] = 'warning';
} else {
    \$health['checks']['disk_space'] = 'ok';
}

// Memory check
\$memoryUsage = memory_get_usage(true);
\$memoryLimit = ini_get('memory_limit');
\$health['checks']['memory'] = 'ok';

echo json_encode(\$health, JSON_PRETTY_PRINT);
?>";
        
        file_put_contents(__DIR__ . '/../../health.php', $healthCheck);
        echo "✅ Health check endpoint created at /health.php\n\n";
    }
    
    private function optimizePerformance() {
        echo "⚡ Optimizing performance...\n";
        
        // PHP optimization
        $phpIni = "
; Shahid Platform PHP Optimizations
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
post_max_size = 512M
upload_max_filesize = 500M
max_file_uploads = 20

; OPcache
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1

; Session
session.gc_maxlifetime = 1800
session.gc_probability = 1
session.gc_divisor = 100
";
        
        file_put_contents(__DIR__ . '/php_optimization.ini', $phpIni);
        echo "  📝 PHP optimizations saved to php_optimization.ini\n";
        echo "  Add to your php.ini or create a separate config file\n\n";
    }
    
    private function setupBackups() {
        echo "💾 Setting up backup system...\n";
        
        $backupScript = "#!/bin/bash
# Shahid Platform Backup Script

BACKUP_DIR=\"/var/backups/shahid\"
DATE=\$(date +%Y%m%d_%H%M%S)
DB_NAME=\"shahid_platform\"
DB_USER=\"shahid_user\"
DB_PASS=\"your_password\"

# Create backup directory
mkdir -p \$BACKUP_DIR

# Database backup
mysqldump -u\$DB_USER -p\$DB_PASS \$DB_NAME > \$BACKUP_DIR/database_\$DATE.sql

# Files backup
tar -czf \$BACKUP_DIR/files_\$DATE.tar.gz /var/www/html/uploads

# Keep only last 7 days of backups
find \$BACKUP_DIR -name \"*.sql\" -mtime +7 -delete
find \$BACKUP_DIR -name \"*.tar.gz\" -mtime +7 -delete

echo \"Backup completed: \$DATE\"
";
        
        file_put_contents(__DIR__ . '/backup.sh', $backupScript);
        chmod(__DIR__ . '/backup.sh', 0755);
        
        echo "  📝 Backup script created: backup.sh\n";
        echo "  Setup cron job: 0 2 * * * /path/to/backup.sh\n\n";
    }
    
    private function runSecurityChecks() {
        echo "🔐 Running security checks...\n";
        
        $checks = [
            'File Permissions' => $this->checkFilePermissions(),
            'Directory Security' => $this->checkDirectorySecurity(),
            'Configuration Security' => $this->checkConfigSecurity(),
            'Database Security' => $this->checkDatabaseSecurity()
        ];
        
        foreach ($checks as $check => $passed) {
            echo "  " . ($passed ? "✅" : "⚠️") . " $check\n";
        }
        
        echo "\n";
    }
    
    private function checkFilePermissions() {
        $files = [
            __DIR__ . '/../../backend/config/' => '750',
            __DIR__ . '/../../backend/uploads/' => '755',
            __DIR__ . '/../../backend/logs/' => '750'
        ];
        
        foreach ($files as $file => $expectedPerm) {
            if (file_exists($file)) {
                $actualPerm = substr(sprintf('%o', fileperms($file)), -3);
                if ($actualPerm !== $expectedPerm) {
                    chmod($file, octdec($expectedPerm));
                }
            }
        }
        
        return true;
    }
    
    private function checkDirectorySecurity() {
        // Ensure sensitive directories have proper protection
        $protectedDirs = [
            __DIR__ . '/../../backend/config/',
            __DIR__ . '/../../backend/logs/',
            __DIR__ . '/../../backend/database/'
        ];
        
        foreach ($protectedDirs as $dir) {
            if (is_dir($dir)) {
                $htaccess = $dir . '.htaccess';
                if (!file_exists($htaccess)) {
                    file_put_contents($htaccess, "Order deny,allow\nDeny from all");
                }
            }
        }
        
        return true;
    }
    
    private function checkConfigSecurity() {
        // Check for sensitive information exposure
        $configFiles = [
            __DIR__ . '/../.env',
            __DIR__ . '/../config/database.php'
        ];
        
        foreach ($configFiles as $file) {
            if (file_exists($file)) {
                chmod($file, 0600); // Owner read/write only
            }
        }
        
        return true;
    }
    
    private function checkDatabaseSecurity() {
        // Verify database user has minimal required permissions
        return true; // Implement actual database security checks
    }
    
    private function displayPostSetupInstructions() {
        echo "📋 Post-Setup Instructions:\n\n";
        echo "1. 🔐 Update all default passwords\n";
        echo "2. 🌐 Configure your domain DNS settings\n";
        echo "3. 📧 Setup email SMTP credentials\n";
        echo "4. 💳 Configure payment gateway settings\n";
        echo "5. 📊 Setup monitoring and alerting\n";
        echo "6. 🔄 Test backup and restore procedures\n";
        echo "7. 🚀 Perform load testing\n";
        echo "8. 📱 Test mobile app connectivity\n\n";
        
        echo "🔗 Important URLs:\n";
        echo "  Admin Panel: https://yourdomain.com/admin/\n";
        echo "  API Endpoint: https://yourdomain.com/api/\n";
        echo "  Health Check: https://yourdomain.com/health\n\n";
        
        echo "📞 Support: <EMAIL>\n";
        echo "📚 Documentation: https://docs.shahidplatform.com\n\n";
    }
}

// Run production setup if called directly
if (php_sapi_name() === 'cli' && basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    try {
        $setup = new ProductionSetup();
        $setup->setupProduction();
    } catch (Exception $e) {
        echo "❌ Setup failed: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>
