<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإصلاح الفوري - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
            text-align: center;
        }
        
        .header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #E50914;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .fix-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 1.5rem 2rem;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.3rem;
            font-weight: bold;
            transition: all 0.3s ease;
            width: 100%;
            margin: 1rem 0;
        }
        
        .fix-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        
        .fix-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid #666;
        }
        
        .status.success {
            border-left-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }
        
        .status.error {
            border-left-color: #F44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .status.loading {
            border-left-color: #2196F3;
            background: rgba(33, 150, 243, 0.1);
        }
        
        .admin-info {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid #2196F3;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            text-align: right;
        }
        
        .admin-info h3 {
            color: #2196F3;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .links {
            display: grid;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        
        .link-btn {
            background: linear-gradient(45deg, #555, #333);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        
        .link-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            color: white;
        }
        
        .link-btn.primary {
            background: linear-gradient(45deg, #E50914, #B8070F);
        }
        
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid #2196F3;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ الإصلاح الفوري</h1>
            <p>إصلاح حساب المدير في ثوانٍ</p>
        </div>

        <div id="statusDiv" class="status">
            <h3>🔍 حالة حساب المدير</h3>
            <p>❌ حساب المدير غير موجود</p>
        </div>

        <button id="fixBtn" class="fix-btn" onclick="fixAdmin()">
            👤 إنشاء حساب المدير الآن
        </button>

        <div id="loadingDiv" class="status loading hidden">
            <div class="spinner"></div>
            <h3>🔄 جاري الإصلاح...</h3>
            <p>يرجى الانتظار...</p>
        </div>

        <div id="successDiv" class="status success hidden">
            <h3>🎉 تم الإصلاح بنجاح!</h3>
            <p>حساب المدير جاهز للاستخدام</p>
        </div>

        <div id="errorDiv" class="status error hidden">
            <h3>❌ فشل الإصلاح</h3>
            <p id="errorMessage">حدث خطأ غير متوقع</p>
        </div>

        <div class="admin-info">
            <h3>📋 بيانات تسجيل الدخول</h3>
            <div class="info-row">
                <span><strong>البريد الإلكتروني:</strong></span>
                <span><EMAIL></span>
            </div>
            <div class="info-row">
                <span><strong>كلمة المرور:</strong></span>
                <span>admin123</span>
            </div>
            <div class="info-row">
                <span><strong>الصلاحيات:</strong></span>
                <span>مدير كامل</span>
            </div>
        </div>

        <div class="links">
            <a href="admin/dashboard.php" class="link-btn primary" id="dashboardLink">🎛️ دخول لوحة الإدارة</a>
            <a href="homepage.php" class="link-btn">🏠 العودة للرئيسية</a>
            <a href="fix_database.php" class="link-btn">🔧 إصلاح قاعدة البيانات الكاملة</a>
        </div>
    </div>

    <script>
        async function fixAdmin() {
            const fixBtn = document.getElementById('fixBtn');
            const statusDiv = document.getElementById('statusDiv');
            const loadingDiv = document.getElementById('loadingDiv');
            const successDiv = document.getElementById('successDiv');
            const errorDiv = document.getElementById('errorDiv');
            const errorMessage = document.getElementById('errorMessage');
            
            // إخفاء جميع الرسائل
            statusDiv.classList.add('hidden');
            successDiv.classList.add('hidden');
            errorDiv.classList.add('hidden');
            
            // إظهار التحميل
            loadingDiv.classList.remove('hidden');
            fixBtn.disabled = true;
            fixBtn.textContent = '🔄 جاري الإصلاح...';
            
            try {
                const response = await fetch('quick_admin_fix.php');
                const result = await response.json();
                
                // إخفاء التحميل
                loadingDiv.classList.add('hidden');
                
                if (result.success) {
                    // إظهار النجاح
                    successDiv.classList.remove('hidden');
                    fixBtn.textContent = '✅ تم الإصلاح بنجاح';
                    fixBtn.style.background = 'linear-gradient(45deg, #4CAF50, #45a049)';
                    
                    // تفعيل رابط لوحة الإدارة
                    const dashboardLink = document.getElementById('dashboardLink');
                    dashboardLink.style.background = 'linear-gradient(45deg, #4CAF50, #45a049)';
                    dashboardLink.innerHTML = '🎛️ دخول لوحة الإدارة (جاهز)';
                    
                    console.log('Admin account fixed:', result);
                    
                    // إعادة توجيه تلقائي بعد 3 ثوان
                    setTimeout(() => {
                        if (confirm('تم إنشاء حساب المدير بنجاح! هل تريد الانتقال إلى لوحة الإدارة؟')) {
                            window.location.href = 'admin/dashboard.php';
                        }
                    }, 2000);
                    
                } else {
                    // إظهار الخطأ
                    errorDiv.classList.remove('hidden');
                    errorMessage.textContent = result.message || 'حدث خطأ غير متوقع';
                    fixBtn.textContent = '❌ فشل الإصلاح';
                    fixBtn.style.background = 'linear-gradient(45deg, #F44336, #D32F2F)';
                    fixBtn.disabled = false;
                    
                    console.error('Admin fix failed:', result);
                }
                
            } catch (error) {
                // إخفاء التحميل
                loadingDiv.classList.add('hidden');
                
                // إظهار خطأ الشبكة
                errorDiv.classList.remove('hidden');
                errorMessage.textContent = 'خطأ في الاتصال. تأكد من تشغيل XAMPP.';
                fixBtn.textContent = '🔄 إعادة المحاولة';
                fixBtn.style.background = 'linear-gradient(45deg, #FF9800, #F57C00)';
                fixBtn.disabled = false;
                
                console.error('Network error:', error);
            }
        }
        
        // فحص حالة المدير عند تحميل الصفحة
        async function checkAdminStatus() {
            try {
                const response = await fetch('quick_admin_fix.php');
                const result = await response.json();
                
                if (result.success && result.action === 'exists') {
                    const statusDiv = document.getElementById('statusDiv');
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '<h3>✅ حساب المدير موجود</h3><p>يمكنك تسجيل الدخول مباشرة</p>';
                    
                    const fixBtn = document.getElementById('fixBtn');
                    fixBtn.textContent = '✅ حساب المدير موجود';
                    fixBtn.style.background = 'linear-gradient(45deg, #4CAF50, #45a049)';
                    fixBtn.disabled = true;
                }
            } catch (error) {
                console.log('Could not check admin status:', error);
            }
        }
        
        // فحص الحالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', checkAdminStatus);
        
        console.log('⚡ Instant Fix Page loaded successfully!');
    </script>
</body>
</html>
