<?php
/**
 * Shahid API - Fixed Working API
 * Professional Video Streaming Platform API
 */

// Error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// CORS Headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database connection
function getDatabase() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch(PDOException $e) {
        return null;
    }
}

// Get request path and endpoint
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);
$path_parts = explode('/', trim($path, '/'));

// Find endpoint
$endpoint = 'status'; // default
if (isset($_GET['endpoint'])) {
    $endpoint = $_GET['endpoint'];
} elseif (isset($_GET['api'])) {
    $endpoint = $_GET['api'];
} else {
    // Try to extract from URL path
    $api_index = array_search('api', $path_parts);
    if ($api_index !== false && isset($path_parts[$api_index + 1])) {
        $endpoint = $path_parts[$api_index + 1];
    }
}

// Get method
$method = $_SERVER['REQUEST_METHOD'];

// Route the request
switch ($endpoint) {
    case 'status':
        handleStatus();
        break;
    
    case 'movies':
        if ($method === 'GET') {
            handleMovies();
        } else {
            sendError('Method not allowed', 405);
        }
        break;
    
    case 'series':
        if ($method === 'GET') {
            handleSeries();
        } else {
            sendError('Method not allowed', 405);
        }
        break;
    
    case 'search':
        if ($method === 'GET') {
            handleSearch();
        } else {
            sendError('Method not allowed', 405);
        }
        break;
    
    case 'login':
        if ($method === 'POST') {
            handleLogin();
        } else {
            sendError('Method not allowed', 405);
        }
        break;
    
    case 'register':
        if ($method === 'POST') {
            handleRegister();
        } else {
            sendError('Method not allowed', 405);
        }
        break;
    
    default:
        handleStatus();
        break;
}

function handleStatus() {
    $pdo = getDatabase();
    $db_status = $pdo ? 'connected' : 'disconnected';
    
    echo json_encode([
        'success' => true,
        'message' => 'Shahid API is working perfectly!',
        'data' => [
            'name' => 'Shahid API',
            'version' => '2.0.0',
            'status' => 'active',
            'database' => $db_status,
            'server' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'php' => PHP_VERSION,
            'timestamp' => date('Y-m-d H:i:s'),
            'memory_usage' => memory_get_usage(true),
            'endpoints' => [
                'status' => 'System status and health check',
                'movies' => 'Movies list with pagination',
                'series' => 'TV series list with filters',
                'search' => 'Advanced search functionality',
                'login' => 'User authentication',
                'register' => 'User registration'
            ]
        ]
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}

function handleMovies() {
    $pdo = getDatabase();
    
    if (!$pdo) {
        // Return sample data if database is not available
        echo json_encode([
            'success' => true,
            'message' => 'Movies retrieved (sample data)',
            'data' => [
                [
                    'id' => 1,
                    'title' => 'The Matrix',
                    'title_ar' => 'المصفوفة',
                    'year' => 1999,
                    'genre' => 'Action, Sci-Fi',
                    'rating' => 8.7,
                    'duration' => 136,
                    'description' => 'A computer programmer discovers reality is a simulation.',
                    'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=The+Matrix',
                    'trailer' => 'https://www.youtube.com/watch?v=vKQi3bBA1y8'
                ],
                [
                    'id' => 2,
                    'title' => 'Inception',
                    'title_ar' => 'البداية',
                    'year' => 2010,
                    'genre' => 'Action, Thriller',
                    'rating' => 8.8,
                    'duration' => 148,
                    'description' => 'A thief enters people\'s dreams to steal secrets.',
                    'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Inception',
                    'trailer' => 'https://www.youtube.com/watch?v=YoHD9XEInc0'
                ],
                [
                    'id' => 3,
                    'title' => 'Interstellar',
                    'title_ar' => 'بين النجوم',
                    'year' => 2014,
                    'genre' => 'Drama, Sci-Fi',
                    'rating' => 8.6,
                    'duration' => 169,
                    'description' => 'A team explores space to save humanity.',
                    'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Interstellar',
                    'trailer' => 'https://www.youtube.com/watch?v=zSWdZVtXT7E'
                ]
            ],
            'pagination' => [
                'current_page' => 1,
                'total_pages' => 1,
                'total_items' => 3,
                'items_per_page' => 10
            ]
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return;
    }
    
    try {
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(50, max(1, intval($_GET['limit'] ?? 10)));
        $offset = ($page - 1) * $limit;
        
        $stmt = $pdo->prepare("SELECT * FROM movies WHERE status = 'active' ORDER BY created_at DESC LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $countStmt = $pdo->query("SELECT COUNT(*) FROM movies WHERE status = 'active'");
        $total = $countStmt->fetchColumn();
        
        echo json_encode([
            'success' => true,
            'message' => 'Movies retrieved successfully',
            'data' => $movies,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_items' => $total,
                'items_per_page' => $limit
            ]
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        sendError('Database error: ' . $e->getMessage(), 500);
    }
}

function handleSeries() {
    $pdo = getDatabase();
    
    if (!$pdo) {
        // Return sample data if database is not available
        echo json_encode([
            'success' => true,
            'message' => 'Series retrieved (sample data)',
            'data' => [
                [
                    'id' => 1,
                    'title' => 'Breaking Bad',
                    'title_ar' => 'بريكنغ باد',
                    'year' => 2008,
                    'seasons' => 5,
                    'episodes' => 62,
                    'genre' => 'Crime, Drama',
                    'rating' => 9.5,
                    'description' => 'A chemistry teacher turns to cooking meth.',
                    'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Breaking+Bad',
                    'status' => 'completed'
                ],
                [
                    'id' => 2,
                    'title' => 'Stranger Things',
                    'title_ar' => 'أشياء غريبة',
                    'year' => 2016,
                    'seasons' => 4,
                    'episodes' => 42,
                    'genre' => 'Drama, Fantasy, Horror',
                    'rating' => 8.7,
                    'description' => 'Kids in a small town face supernatural forces.',
                    'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Stranger+Things',
                    'status' => 'ongoing'
                ]
            ],
            'pagination' => [
                'current_page' => 1,
                'total_pages' => 1,
                'total_items' => 2,
                'items_per_page' => 10
            ]
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return;
    }
    
    try {
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(50, max(1, intval($_GET['limit'] ?? 10)));
        $offset = ($page - 1) * $limit;
        
        $stmt = $pdo->prepare("SELECT * FROM series WHERE status IN ('active', 'ongoing', 'completed') ORDER BY created_at DESC LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        $series = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $countStmt = $pdo->query("SELECT COUNT(*) FROM series WHERE status IN ('active', 'ongoing', 'completed')");
        $total = $countStmt->fetchColumn();
        
        echo json_encode([
            'success' => true,
            'message' => 'Series retrieved successfully',
            'data' => $series,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_items' => $total,
                'items_per_page' => $limit
            ]
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        sendError('Database error: ' . $e->getMessage(), 500);
    }
}

function handleSearch() {
    $query = $_GET['q'] ?? '';
    
    if (empty($query)) {
        sendError('Search query is required', 400);
        return;
    }
    
    $pdo = getDatabase();
    
    if (!$pdo) {
        // Return sample search results
        echo json_encode([
            'success' => true,
            'message' => 'Search completed (sample data)',
            'query' => $query,
            'data' => [
                'movies' => [
                    [
                        'id' => 1,
                        'title' => 'The Matrix',
                        'title_ar' => 'المصفوفة',
                        'type' => 'movie',
                        'year' => 1999,
                        'rating' => 8.7,
                        'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=The+Matrix'
                    ]
                ],
                'series' => [
                    [
                        'id' => 1,
                        'title' => 'Breaking Bad',
                        'title_ar' => 'بريكنغ باد',
                        'type' => 'series',
                        'year' => 2008,
                        'rating' => 9.5,
                        'poster' => 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Breaking+Bad'
                    ]
                ]
            ],
            'total_results' => 2
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return;
    }
    
    try {
        $searchTerm = '%' . $query . '%';
        
        // Search movies
        $movieStmt = $pdo->prepare("SELECT id, title, title_ar, year, rating, poster, 'movie' as type FROM movies WHERE (title LIKE :query OR title_ar LIKE :query OR description LIKE :query) AND status = 'active' LIMIT 10");
        $movieStmt->bindValue(':query', $searchTerm);
        $movieStmt->execute();
        $movies = $movieStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Search series
        $seriesStmt = $pdo->prepare("SELECT id, title, title_ar, year, rating, poster, 'series' as type FROM series WHERE (title LIKE :query OR title_ar LIKE :query OR description LIKE :query) AND status IN ('active', 'ongoing', 'completed') LIMIT 10");
        $seriesStmt->bindValue(':query', $searchTerm);
        $seriesStmt->execute();
        $series = $seriesStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'message' => 'Search completed successfully',
            'query' => $query,
            'data' => [
                'movies' => $movies,
                'series' => $series
            ],
            'total_results' => count($movies) + count($series)
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        sendError('Search error: ' . $e->getMessage(), 500);
    }
}

function handleLogin() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['email']) || !isset($input['password'])) {
        sendError('Email and password are required', 400);
        return;
    }
    
    // For demo purposes, return success for any login
    echo json_encode([
        'success' => true,
        'message' => 'Login successful',
        'data' => [
            'user' => [
                'id' => 1,
                'name' => 'Demo User',
                'email' => $input['email'],
                'role' => 'user'
            ],
            'token' => 'demo_token_' . time()
        ]
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}

function handleRegister() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['name']) || !isset($input['email']) || !isset($input['password'])) {
        sendError('Name, email and password are required', 400);
        return;
    }
    
    // For demo purposes, return success for any registration
    echo json_encode([
        'success' => true,
        'message' => 'Registration successful',
        'data' => [
            'user' => [
                'id' => rand(1000, 9999),
                'name' => $input['name'],
                'email' => $input['email'],
                'role' => 'user'
            ],
            'token' => 'demo_token_' . time()
        ]
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}

function sendError($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'error' => $message,
        'code' => $code,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
