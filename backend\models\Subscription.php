<?php
/**
 * Shahid - Subscription Model
 * Professional Video Streaming Platform
 */

class Subscription extends Model {
    protected $table = 'subscriptions';
    
    public function getActivePlans() {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' 
                ORDER BY price ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getPlanById($id) {
        return $this->findById($id);
    }
    
    public function createPlan($data) {
        $requiredFields = ['name', 'description', 'price', 'currency', 'duration_days'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Missing required field: {$field}");
            }
        }
        
        $planData = [
            'name' => $data['name'],
            'description' => $data['description'],
            'price' => floatval($data['price']),
            'currency' => $data['currency'],
            'duration_days' => intval($data['duration_days']),
            'features' => json_encode($data['features'] ?? []),
            'max_devices' => intval($data['max_devices'] ?? 1),
            'max_quality' => $data['max_quality'] ?? '1080p',
            'download_enabled' => isset($data['download_enabled']) ? 1 : 0,
            'ads_free' => isset($data['ads_free']) ? 1 : 0,
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->create($planData);
    }
    
    public function updatePlan($id, $data) {
        $updateData = [];
        
        $allowedFields = [
            'name', 'description', 'price', 'currency', 'duration_days',
            'features', 'max_devices', 'max_quality', 'download_enabled',
            'ads_free', 'status'
        ];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                if ($field === 'features' && is_array($data[$field])) {
                    $updateData[$field] = json_encode($data[$field]);
                } elseif (in_array($field, ['download_enabled', 'ads_free'])) {
                    $updateData[$field] = isset($data[$field]) ? 1 : 0;
                } elseif (in_array($field, ['price'])) {
                    $updateData[$field] = floatval($data[$field]);
                } elseif (in_array($field, ['duration_days', 'max_devices'])) {
                    $updateData[$field] = intval($data[$field]);
                } else {
                    $updateData[$field] = $data[$field];
                }
            }
        }
        
        if (!empty($updateData)) {
            $updateData['updated_at'] = date('Y-m-d H:i:s');
            return $this->update($id, $updateData);
        }
        
        return false;
    }
    
    public function deletePlan($id) {
        // Soft delete - set status to inactive
        return $this->update($id, [
            'status' => 'inactive',
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    public function getPlanFeatures($id) {
        $plan = $this->findById($id);
        
        if (!$plan) {
            return [];
        }
        
        $features = json_decode($plan['features'], true);
        return is_array($features) ? $features : [];
    }
    
    public function getPopularPlans($limit = 3) {
        $sql = "SELECT s.*, COUNT(us.id) as subscriber_count
                FROM {$this->table} s
                LEFT JOIN user_subscriptions us ON s.id = us.subscription_id 
                    AND us.status = 'active'
                WHERE s.status = 'active'
                GROUP BY s.id
                ORDER BY subscriber_count DESC, s.price ASC
                LIMIT :limit";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function getSubscriptionStats() {
        $stats = [];
        
        // Total active subscriptions
        $sql = "SELECT COUNT(*) as total FROM user_subscriptions WHERE status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['total_active'] = $stmt->fetch()['total'];
        
        // Revenue this month
        $sql = "SELECT SUM(p.amount) as revenue 
                FROM payments p 
                WHERE p.status = 'completed' 
                AND MONTH(p.created_at) = MONTH(NOW()) 
                AND YEAR(p.created_at) = YEAR(NOW())";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['monthly_revenue'] = $stmt->fetch()['revenue'] ?? 0;
        
        // Subscriptions by plan
        $sql = "SELECT s.name, s.price, COUNT(us.id) as count
                FROM {$this->table} s
                LEFT JOIN user_subscriptions us ON s.id = us.subscription_id 
                    AND us.status = 'active'
                WHERE s.status = 'active'
                GROUP BY s.id, s.name, s.price
                ORDER BY count DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['by_plan'] = $stmt->fetchAll();
        
        // Growth rate (last 30 days)
        $sql = "SELECT COUNT(*) as new_subscriptions 
                FROM user_subscriptions 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['new_subscriptions_30d'] = $stmt->fetch()['new_subscriptions'];
        
        // Churn rate (cancelled in last 30 days)
        $sql = "SELECT COUNT(*) as cancelled_subscriptions 
                FROM user_subscriptions 
                WHERE status = 'cancelled' 
                AND updated_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['cancelled_subscriptions_30d'] = $stmt->fetch()['cancelled_subscriptions'];
        
        return $stats;
    }
    
    public function validatePlanData($data, $isUpdate = false) {
        $errors = [];
        
        if (!$isUpdate || isset($data['name'])) {
            if (empty($data['name']) || strlen($data['name']) < 2) {
                $errors['name'] = 'Plan name must be at least 2 characters';
            }
        }
        
        if (!$isUpdate || isset($data['price'])) {
            if (!isset($data['price']) || !is_numeric($data['price']) || $data['price'] < 0) {
                $errors['price'] = 'Price must be a valid positive number';
            }
        }
        
        if (!$isUpdate || isset($data['currency'])) {
            $validCurrencies = ['USD', 'EUR', 'SAR', 'AED', 'EGP'];
            if (empty($data['currency']) || !in_array($data['currency'], $validCurrencies)) {
                $errors['currency'] = 'Invalid currency code';
            }
        }
        
        if (!$isUpdate || isset($data['duration_days'])) {
            if (!isset($data['duration_days']) || !is_numeric($data['duration_days']) || $data['duration_days'] <= 0) {
                $errors['duration_days'] = 'Duration must be a positive number';
            }
        }
        
        if (isset($data['max_devices'])) {
            if (!is_numeric($data['max_devices']) || $data['max_devices'] < 1 || $data['max_devices'] > 10) {
                $errors['max_devices'] = 'Max devices must be between 1 and 10';
            }
        }
        
        if (isset($data['max_quality'])) {
            $validQualities = ['360p', '480p', '720p', '1080p', '4K'];
            if (!in_array($data['max_quality'], $validQualities)) {
                $errors['max_quality'] = 'Invalid quality setting';
            }
        }
        
        return $errors;
    }
    
    public function searchPlans($query, $filters = []) {
        $sql = "SELECT * FROM {$this->table} WHERE status = 'active'";
        $params = [];
        
        if (!empty($query)) {
            $sql .= " AND (name LIKE :query OR description LIKE :query)";
            $params[':query'] = "%{$query}%";
        }
        
        if (isset($filters['min_price'])) {
            $sql .= " AND price >= :min_price";
            $params[':min_price'] = $filters['min_price'];
        }
        
        if (isset($filters['max_price'])) {
            $sql .= " AND price <= :max_price";
            $params[':max_price'] = $filters['max_price'];
        }
        
        if (isset($filters['currency'])) {
            $sql .= " AND currency = :currency";
            $params[':currency'] = $filters['currency'];
        }
        
        if (isset($filters['features'])) {
            foreach ($filters['features'] as $feature) {
                $sql .= " AND JSON_CONTAINS(features, :feature_{$feature})";
                $params[":feature_{$feature}"] = json_encode($feature);
            }
        }
        
        $sql .= " ORDER BY price ASC";
        
        $stmt = $this->db->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function getRecommendedPlan($userId) {
        // Get user's viewing habits and recommend a plan
        $sql = "SELECT 
                    COUNT(DISTINCT wh.content_id) as content_watched,
                    AVG(wh.progress / wh.duration) as avg_completion,
                    COUNT(DISTINCT DATE(wh.watched_at)) as active_days
                FROM watch_history wh 
                WHERE wh.user_id = :user_id 
                AND wh.watched_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        $habits = $stmt->fetch();
        
        // Simple recommendation logic
        if ($habits['content_watched'] > 50 || $habits['active_days'] > 20) {
            // Heavy user - recommend premium plan
            $sql = "SELECT * FROM {$this->table} 
                    WHERE status = 'active' 
                    ORDER BY price DESC LIMIT 1";
        } elseif ($habits['content_watched'] > 20 || $habits['active_days'] > 10) {
            // Medium user - recommend mid-tier plan
            $sql = "SELECT * FROM {$this->table} 
                    WHERE status = 'active' 
                    ORDER BY price ASC LIMIT 1 OFFSET 1";
        } else {
            // Light user - recommend basic plan
            $sql = "SELECT * FROM {$this->table} 
                    WHERE status = 'active' 
                    ORDER BY price ASC LIMIT 1";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    public function comparePlans($planIds) {
        if (empty($planIds) || !is_array($planIds)) {
            return [];
        }
        
        $placeholders = str_repeat('?,', count($planIds) - 1) . '?';
        $sql = "SELECT * FROM {$this->table} 
                WHERE id IN ({$placeholders}) 
                AND status = 'active'
                ORDER BY price ASC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($planIds);
        
        $plans = $stmt->fetchAll();
        
        // Add feature comparison
        foreach ($plans as &$plan) {
            $plan['features'] = json_decode($plan['features'], true) ?: [];
        }
        
        return $plans;
    }
}
?>
