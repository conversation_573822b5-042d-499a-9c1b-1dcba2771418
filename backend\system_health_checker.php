<?php
/**
 * نظام فحص صحة النظام الشامل
 * يتحقق من جميع مكونات النظام ويقدم تقرير صحة مفصل
 */

class SystemHealthChecker {
    private $checks = [];
    private $results = [];
    
    public function __construct() {
        $this->initializeChecks();
    }
    
    /**
     * تهيئة فحوصات النظام
     */
    private function initializeChecks() {
        $this->checks = [
            'database' => 'فحص قاعدة البيانات',
            'files' => 'فحص الملفات والمجلدات',
            'permissions' => 'فحص صلاحيات الملفات',
            'php_config' => 'فحص إعدادات PHP',
            'extensions' => 'فحص الإضافات المطلوبة',
            'security' => 'فحص الأمان',
            'performance' => 'فحص الأداء',
            'api' => 'فحص API',
            'storage' => 'فحص مساحة التخزين',
            'logs' => 'فحص السجلات'
        ];
    }
    
    /**
     * تشغيل جميع الفحوصات
     */
    public function runAllChecks() {
        $this->results = [];
        
        foreach ($this->checks as $checkName => $description) {
            $methodName = 'check' . ucfirst($checkName);
            if (method_exists($this, $methodName)) {
                $this->results[$checkName] = $this->$methodName();
            }
        }
        
        return $this->generateReport();
    }
    
    /**
     * فحص قاعدة البيانات
     */
    private function checkDatabase() {
        $result = [
            'status' => 'success',
            'message' => 'قاعدة البيانات تعمل بشكل طبيعي',
            'details' => []
        ];
        
        try {
            $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // فحص الاتصال
            $pdo->query("SELECT 1");
            $result['details']['connection'] = '✅ الاتصال ناجح';
            
            // فحص الجداول المطلوبة
            $requiredTables = [
                'movies', 'series', 'episodes', 'users', 'categories',
                'ratings', 'favorites', 'watch_history', 'notifications',
                'uploaded_files', 'page_views', 'content_views', 'analytics_events'
            ];
            
            $existingTables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
            $missingTables = array_diff($requiredTables, $existingTables);
            
            if (empty($missingTables)) {
                $result['details']['tables'] = '✅ جميع الجداول موجودة (' . count($existingTables) . ' جدول)';
            } else {
                $result['status'] = 'warning';
                $result['details']['tables'] = '⚠️ جداول مفقودة: ' . implode(', ', $missingTables);
            }
            
            // فحص البيانات
            $moviesCount = $pdo->query("SELECT COUNT(*) FROM movies")->fetchColumn();
            $seriesCount = $pdo->query("SELECT COUNT(*) FROM series")->fetchColumn();
            $usersCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
            
            $result['details']['data'] = "✅ البيانات: {$moviesCount} فيلم، {$seriesCount} مسلسل، {$usersCount} مستخدم";
            
        } catch (Exception $e) {
            $result['status'] = 'error';
            $result['message'] = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * فحص الملفات والمجلدات
     */
    private function checkFiles() {
        $result = [
            'status' => 'success',
            'message' => 'جميع الملفات والمجلدات موجودة',
            'details' => []
        ];
        
        $requiredFiles = [
            'api/index.php' => 'API الأساسي',
            'api/advanced.php' => 'API المتقدم',
            'admin/dashboard.php' => 'لوحة التحكم',
            'admin/system_management.php' => 'إدارة النظام',
            'setup_complete_system.php' => 'إعداد النظام',
            'test_advanced_features.php' => 'اختبار الميزات'
        ];
        
        $requiredDirs = [
            'uploads' => 'مجلد الرفع',
            'logs' => 'مجلد السجلات',
            'temp' => 'مجلد مؤقت',
            'backups' => 'مجلد النسخ الاحتياطية'
        ];
        
        $missingFiles = [];
        $missingDirs = [];
        
        foreach ($requiredFiles as $file => $description) {
            if (!file_exists(__DIR__ . '/' . $file)) {
                $missingFiles[] = $file;
            }
        }
        
        foreach ($requiredDirs as $dir => $description) {
            $dirPath = __DIR__ . '/' . $dir;
            if (!is_dir($dirPath)) {
                mkdir($dirPath, 0755, true);
                $result['details'][$dir] = "✅ تم إنشاء {$description}";
            } else {
                $result['details'][$dir] = "✅ {$description} موجود";
            }
        }
        
        if (!empty($missingFiles)) {
            $result['status'] = 'warning';
            $result['details']['missing_files'] = '⚠️ ملفات مفقودة: ' . implode(', ', $missingFiles);
        }
        
        return $result;
    }
    
    /**
     * فحص صلاحيات الملفات
     */
    private function checkPermissions() {
        $result = [
            'status' => 'success',
            'message' => 'صلاحيات الملفات صحيحة',
            'details' => []
        ];
        
        $writableDirs = ['uploads', 'logs', 'temp', 'backups'];
        
        foreach ($writableDirs as $dir) {
            $dirPath = __DIR__ . '/' . $dir;
            if (is_dir($dirPath)) {
                if (is_writable($dirPath)) {
                    $result['details'][$dir] = "✅ {$dir} قابل للكتابة";
                } else {
                    $result['status'] = 'warning';
                    $result['details'][$dir] = "⚠️ {$dir} غير قابل للكتابة";
                }
            }
        }
        
        return $result;
    }
    
    /**
     * فحص إعدادات PHP
     */
    private function checkPhpConfig() {
        $result = [
            'status' => 'success',
            'message' => 'إعدادات PHP مناسبة',
            'details' => []
        ];
        
        // فحص إصدار PHP
        $phpVersion = PHP_VERSION;
        if (version_compare($phpVersion, '8.0', '>=')) {
            $result['details']['version'] = "✅ إصدار PHP: {$phpVersion}";
        } else {
            $result['status'] = 'warning';
            $result['details']['version'] = "⚠️ إصدار PHP قديم: {$phpVersion}";
        }
        
        // فحص حد الذاكرة
        $memoryLimit = ini_get('memory_limit');
        $result['details']['memory'] = "✅ حد الذاكرة: {$memoryLimit}";
        
        // فحص حد وقت التنفيذ
        $maxExecutionTime = ini_get('max_execution_time');
        $result['details']['execution_time'] = "✅ حد وقت التنفيذ: {$maxExecutionTime} ثانية";
        
        // فحص حد رفع الملفات
        $uploadMaxFilesize = ini_get('upload_max_filesize');
        $result['details']['upload_limit'] = "✅ حد رفع الملفات: {$uploadMaxFilesize}";
        
        return $result;
    }
    
    /**
     * فحص الإضافات المطلوبة
     */
    private function checkExtensions() {
        $result = [
            'status' => 'success',
            'message' => 'جميع الإضافات المطلوبة متوفرة',
            'details' => []
        ];
        
        $requiredExtensions = [
            'pdo' => 'قاعدة البيانات',
            'pdo_mysql' => 'MySQL',
            'json' => 'JSON',
            'mbstring' => 'معالجة النصوص',
            'openssl' => 'التشفير',
            'curl' => 'HTTP Requests',
            'gd' => 'معالجة الصور',
            'zip' => 'ضغط الملفات'
        ];
        
        $missingExtensions = [];
        
        foreach ($requiredExtensions as $ext => $description) {
            if (extension_loaded($ext)) {
                $result['details'][$ext] = "✅ {$description}";
            } else {
                $missingExtensions[] = $ext;
                $result['details'][$ext] = "❌ {$description} مفقود";
            }
        }
        
        if (!empty($missingExtensions)) {
            $result['status'] = 'error';
            $result['message'] = 'إضافات مفقودة: ' . implode(', ', $missingExtensions);
        }
        
        return $result;
    }
    
    /**
     * فحص الأمان
     */
    private function checkSecurity() {
        $result = [
            'status' => 'success',
            'message' => 'إعدادات الأمان جيدة',
            'details' => []
        ];
        
        // فحص إعدادات الأمان
        $securitySettings = [
            'display_errors' => 'Off',
            'expose_php' => 'Off',
            'allow_url_fopen' => 'Off',
            'allow_url_include' => 'Off'
        ];
        
        foreach ($securitySettings as $setting => $recommended) {
            $current = ini_get($setting);
            if ($current == $recommended || ($recommended == 'Off' && !$current)) {
                $result['details'][$setting] = "✅ {$setting}: آمن";
            } else {
                $result['status'] = 'warning';
                $result['details'][$setting] = "⚠️ {$setting}: {$current} (يُنصح بـ {$recommended})";
            }
        }
        
        // فحص ملفات الحماية
        $securityFiles = [
            '.htaccess' => 'حماية Apache',
            'error_handler.php' => 'معالج الأخطاء',
            'security_system.php' => 'نظام الأمان'
        ];
        
        foreach ($securityFiles as $file => $description) {
            if (file_exists(__DIR__ . '/' . $file)) {
                $result['details'][$file] = "✅ {$description}";
            } else {
                $result['status'] = 'warning';
                $result['details'][$file] = "⚠️ {$description} مفقود";
            }
        }
        
        return $result;
    }
    
    /**
     * فحص الأداء
     */
    private function checkPerformance() {
        $result = [
            'status' => 'success',
            'message' => 'أداء النظام جيد',
            'details' => []
        ];
        
        // فحص استهلاك الذاكرة
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        
        $result['details']['memory_current'] = "✅ الذاكرة الحالية: " . $this->formatBytes($memoryUsage);
        $result['details']['memory_peak'] = "✅ ذروة الذاكرة: " . $this->formatBytes($memoryPeak);
        
        // فحص مساحة القرص
        $diskTotal = disk_total_space(__DIR__);
        $diskFree = disk_free_space(__DIR__);
        $diskUsed = $diskTotal - $diskFree;
        $diskPercent = round(($diskUsed / $diskTotal) * 100, 2);
        
        if ($diskPercent < 80) {
            $result['details']['disk'] = "✅ مساحة القرص: {$diskPercent}% مستخدمة";
        } else {
            $result['status'] = 'warning';
            $result['details']['disk'] = "⚠️ مساحة القرص منخفضة: {$diskPercent}% مستخدمة";
        }
        
        // فحص حمولة الخادم
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            $result['details']['load'] = "✅ حمولة الخادم: " . number_format($load[0], 2);
        }
        
        return $result;
    }
    
    /**
     * فحص API
     */
    private function checkApi() {
        $result = [
            'status' => 'success',
            'message' => 'API يعمل بشكل طبيعي',
            'details' => []
        ];
        
        try {
            // فحص API الأساسي
            $apiUrl = 'http://localhost' . dirname($_SERVER['REQUEST_URI']) . '/api/?endpoint=status';
            $response = @file_get_contents($apiUrl);
            
            if ($response) {
                $data = json_decode($response, true);
                if ($data && isset($data['status'])) {
                    $result['details']['basic_api'] = "✅ API الأساسي يعمل";
                } else {
                    $result['status'] = 'warning';
                    $result['details']['basic_api'] = "⚠️ API الأساسي لا يستجيب بشكل صحيح";
                }
            } else {
                $result['status'] = 'warning';
                $result['details']['basic_api'] = "⚠️ لا يمكن الوصول للـ API الأساسي";
            }
            
            // فحص ملفات API
            $apiFiles = [
                'api/index.php' => 'API الأساسي',
                'api/advanced.php' => 'API المتقدم'
            ];
            
            foreach ($apiFiles as $file => $description) {
                if (file_exists(__DIR__ . '/' . $file)) {
                    $result['details'][$file] = "✅ {$description}";
                } else {
                    $result['status'] = 'error';
                    $result['details'][$file] = "❌ {$description} مفقود";
                }
            }
            
        } catch (Exception $e) {
            $result['status'] = 'error';
            $result['message'] = 'خطأ في فحص API: ' . $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * فحص مساحة التخزين
     */
    private function checkStorage() {
        $result = [
            'status' => 'success',
            'message' => 'مساحة التخزين كافية',
            'details' => []
        ];
        
        $directories = [
            'uploads' => 'ملفات الرفع',
            'logs' => 'ملفات السجلات',
            'temp' => 'ملفات مؤقتة',
            'backups' => 'النسخ الاحتياطية'
        ];
        
        foreach ($directories as $dir => $description) {
            $dirPath = __DIR__ . '/' . $dir;
            if (is_dir($dirPath)) {
                $size = $this->getDirectorySize($dirPath);
                $result['details'][$dir] = "✅ {$description}: " . $this->formatBytes($size);
            }
        }
        
        return $result;
    }
    
    /**
     * فحص السجلات
     */
    private function checkLogs() {
        $result = [
            'status' => 'success',
            'message' => 'السجلات تعمل بشكل طبيعي',
            'details' => []
        ];
        
        $logDirs = [
            'logs' => 'سجلات عامة',
            'logs/performance' => 'سجلات الأداء'
        ];
        
        foreach ($logDirs as $dir => $description) {
            $dirPath = __DIR__ . '/' . $dir;
            if (is_dir($dirPath)) {
                $files = glob($dirPath . '/*.log');
                $result['details'][$dir] = "✅ {$description}: " . count($files) . " ملف";
            } else {
                $result['status'] = 'warning';
                $result['details'][$dir] = "⚠️ {$description}: مجلد مفقود";
            }
        }
        
        return $result;
    }
    
    /**
     * إنشاء التقرير النهائي
     */
    private function generateReport() {
        $overallStatus = 'success';
        $totalChecks = count($this->results);
        $successCount = 0;
        $warningCount = 0;
        $errorCount = 0;
        
        foreach ($this->results as $check) {
            switch ($check['status']) {
                case 'success':
                    $successCount++;
                    break;
                case 'warning':
                    $warningCount++;
                    if ($overallStatus === 'success') {
                        $overallStatus = 'warning';
                    }
                    break;
                case 'error':
                    $errorCount++;
                    $overallStatus = 'error';
                    break;
            }
        }
        
        return [
            'overall_status' => $overallStatus,
            'summary' => [
                'total_checks' => $totalChecks,
                'success_count' => $successCount,
                'warning_count' => $warningCount,
                'error_count' => $errorCount,
                'success_rate' => round(($successCount / $totalChecks) * 100, 1)
            ],
            'checks' => $this->results,
            'timestamp' => date('Y-m-d H:i:s'),
            'recommendations' => $this->generateRecommendations()
        ];
    }
    
    /**
     * إنشاء التوصيات
     */
    private function generateRecommendations() {
        $recommendations = [];
        
        foreach ($this->results as $checkName => $result) {
            if ($result['status'] === 'error') {
                $recommendations[] = [
                    'type' => 'error',
                    'check' => $checkName,
                    'message' => $result['message'],
                    'priority' => 'high'
                ];
            } elseif ($result['status'] === 'warning') {
                $recommendations[] = [
                    'type' => 'warning',
                    'check' => $checkName,
                    'message' => $result['message'],
                    'priority' => 'medium'
                ];
            }
        }
        
        return $recommendations;
    }
    
    /**
     * تنسيق حجم الملف
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * حساب حجم المجلد
     */
    private function getDirectorySize($directory) {
        $size = 0;
        
        if (is_dir($directory)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $size += $file->getSize();
                }
            }
        }
        
        return $size;
    }
}

// معالجة طلبات API
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    $action = $_GET['action'];
    
    if ($action === 'health_check') {
        $healthChecker = new SystemHealthChecker();
        $report = $healthChecker->runAllChecks();
        
        echo json_encode([
            'success' => true,
            'data' => $report
        ]);
        exit;
    }
}
?>
