<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح قاعدة البيانات - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #E50914;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #E50914;
        }
        
        .success {
            border-left-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }
        
        .error {
            border-left-color: #F44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .warning {
            border-left-color: #FF9800;
            background: rgba(255, 152, 0, 0.1);
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #555, #333);
        }
        
        .progress {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .progress-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .progress-item:last-child {
            border-bottom: none;
        }
        
        .progress-icon {
            margin-left: 1rem;
            font-size: 1.2rem;
        }
        
        .actions {
            text-align: center;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح قاعدة البيانات</h1>
            <p>إصلاح مشاكل الاتصال وإنشاء قاعدة البيانات</p>
        </div>

        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix'])) {
            echo '<div class="progress">';
            echo '<h3>🚀 جاري إصلاح قاعدة البيانات...</h3>';
            
            try {
                // إعدادات قاعدة البيانات
                $host = 'localhost';
                $username = 'root';
                $password = '';
                $database = 'shahid_platform';
                
                // الخطوة 1: الاتصال بـ MySQL
                echo '<div class="progress-item"><span class="progress-icon">📡</span> الاتصال بخادم MySQL...</div>';
                flush();
                
                $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                echo '<div class="progress-item"><span class="progress-icon">✅</span> تم الاتصال بخادم MySQL بنجاح!</div>';
                flush();
                
                // الخطوة 2: إنشاء قاعدة البيانات
                echo '<div class="progress-item"><span class="progress-icon">🗄️</span> إنشاء قاعدة البيانات...</div>';
                flush();
                
                $pdo->exec("DROP DATABASE IF EXISTS $database");
                $pdo->exec("CREATE DATABASE $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                
                echo '<div class="progress-item"><span class="progress-icon">✅</span> تم إنشاء قاعدة البيانات بنجاح!</div>';
                flush();
                
                // الخطوة 3: الاتصال بقاعدة البيانات الجديدة
                $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // الخطوة 4: إنشاء الجداول الأساسية
                echo '<div class="progress-item"><span class="progress-icon">📋</span> إنشاء الجداول الأساسية...</div>';
                flush();
                
                // جدول المستخدمين
                $pdo->exec("
                    CREATE TABLE users (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(255) NOT NULL,
                        email VARCHAR(255) UNIQUE NOT NULL,
                        password VARCHAR(255) NOT NULL,
                        role ENUM('user', 'admin') DEFAULT 'user',
                        subscription_type ENUM('free', 'basic', 'premium') DEFAULT 'free',
                        subscription_end DATETIME NULL,
                        profile_image VARCHAR(255) NULL,
                        status ENUM('active', 'banned', 'deleted') DEFAULT 'active',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )
                ");
                
                // جدول الأفلام
                $pdo->exec("
                    CREATE TABLE movies (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        title VARCHAR(255) NOT NULL,
                        title_ar VARCHAR(255) NULL,
                        description TEXT NULL,
                        description_ar TEXT NULL,
                        year INT NOT NULL,
                        duration INT NOT NULL,
                        rating DECIMAL(3,1) DEFAULT 0.0,
                        genre VARCHAR(255) NULL,
                        poster VARCHAR(255) NULL,
                        trailer VARCHAR(255) NULL,
                        video_url VARCHAR(255) NULL,
                        status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )
                ");
                
                // جدول المسلسلات
                $pdo->exec("
                    CREATE TABLE series (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        title VARCHAR(255) NOT NULL,
                        title_ar VARCHAR(255) NULL,
                        description TEXT NULL,
                        description_ar TEXT NULL,
                        year INT NOT NULL,
                        seasons INT DEFAULT 1,
                        episodes INT DEFAULT 1,
                        rating DECIMAL(3,1) DEFAULT 0.0,
                        genre VARCHAR(255) NULL,
                        poster VARCHAR(255) NULL,
                        trailer VARCHAR(255) NULL,
                        status ENUM('active', 'ongoing', 'completed', 'deleted') DEFAULT 'active',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )
                ");
                
                echo '<div class="progress-item"><span class="progress-icon">✅</span> تم إنشاء الجداول الأساسية!</div>';
                flush();
                
                // الخطوة 5: إنشاء حساب المدير
                echo '<div class="progress-item"><span class="progress-icon">👤</span> إنشاء حساب المدير...</div>';
                flush();
                
                $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
                $pdo->exec("
                    INSERT INTO users (name, email, password, role, subscription_type, subscription_end) 
                    VALUES ('مدير النظام', '<EMAIL>', '$adminPassword', 'admin', 'premium', DATE_ADD(NOW(), INTERVAL 10 YEAR))
                ");
                
                echo '<div class="progress-item"><span class="progress-icon">✅</span> تم إنشاء حساب المدير!</div>';
                flush();
                
                // الخطوة 6: إضافة بيانات تجريبية
                echo '<div class="progress-item"><span class="progress-icon">🎬</span> إضافة محتوى تجريبي...</div>';
                flush();
                
                // إضافة أفلام تجريبية
                $movies = [
                    ['The Shawshank Redemption', 'الخلاص من شاوشانك', 1994, 142, 9.3, 'Drama'],
                    ['The Godfather', 'العراب', 1972, 175, 9.2, 'Crime,Drama'],
                    ['The Dark Knight', 'فارس الظلام', 2008, 152, 9.0, 'Action,Crime'],
                    ['Pulp Fiction', 'لب الخيال', 1994, 154, 8.9, 'Crime,Drama'],
                    ['Forrest Gump', 'فورست غامب', 1994, 142, 8.8, 'Drama,Romance']
                ];
                
                foreach ($movies as $movie) {
                    $pdo->exec("
                        INSERT INTO movies (title, title_ar, year, duration, rating, genre, description, description_ar) 
                        VALUES ('{$movie[0]}', '{$movie[1]}', {$movie[2]}, {$movie[3]}, {$movie[4]}, '{$movie[5]}', 
                                'A great movie', 'فيلم رائع')
                    ");
                }
                
                // إضافة مسلسلات تجريبية
                $series = [
                    ['Breaking Bad', 'بريكنغ باد', 2008, 5, 62, 9.5, 'Crime,Drama'],
                    ['Game of Thrones', 'صراع العروش', 2011, 8, 73, 9.3, 'Action,Adventure'],
                    ['Stranger Things', 'أشياء غريبة', 2016, 4, 42, 8.7, 'Drama,Fantasy'],
                    ['The Crown', 'التاج', 2016, 6, 60, 8.7, 'Biography,Drama'],
                    ['Money Heist', 'بيت المال', 2017, 5, 52, 8.3, 'Action,Crime']
                ];
                
                foreach ($series as $show) {
                    $pdo->exec("
                        INSERT INTO series (title, title_ar, year, seasons, episodes, rating, genre, description, description_ar) 
                        VALUES ('{$show[0]}', '{$show[1]}', {$show[2]}, {$show[3]}, {$show[4]}, {$show[5]}, '{$show[6]}', 
                                'A great series', 'مسلسل رائع')
                    ");
                }
                
                echo '<div class="progress-item"><span class="progress-icon">✅</span> تم إضافة المحتوى التجريبي!</div>';
                flush();
                
                // الخطوة 7: تحديث ملف الإعدادات
                echo '<div class="progress-item"><span class="progress-icon">⚙️</span> تحديث ملف الإعدادات...</div>';
                flush();
                
                $configContent = "<?php
/**
 * Shahid - Database Configuration
 * Professional Video Streaming Platform
 * 
 * This file is generated by the installation process
 * DO NOT EDIT MANUALLY
 */

return [
    'host' => 'localhost',
    'name' => 'shahid_platform',
    'username' => 'root',
    'password' => ''
];";
                
                file_put_contents(__DIR__ . '/config/database.php', $configContent);
                
                echo '<div class="progress-item"><span class="progress-icon">✅</span> تم تحديث ملف الإعدادات!</div>';
                flush();
                
                echo '</div>';
                echo '<div class="status success">';
                echo '<h3>🎉 تم إصلاح قاعدة البيانات بنجاح!</h3>';
                echo '<p><strong>قاعدة البيانات:</strong> shahid_platform</p>';
                echo '<p><strong>حساب المدير:</strong> <EMAIL></p>';
                echo '<p><strong>كلمة المرور:</strong> admin123</p>';
                echo '<p><strong>الأفلام:</strong> 5 أفلام تجريبية</p>';
                echo '<p><strong>المسلسلات:</strong> 5 مسلسلات تجريبية</p>';
                echo '</div>';
                
            } catch (PDOException $e) {
                echo '</div>';
                echo '<div class="status error">';
                echo '<h3>❌ خطأ في قاعدة البيانات</h3>';
                echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
                echo '<h4>حلول مقترحة:</h4>';
                echo '<ul>';
                echo '<li>تأكد من تشغيل XAMPP</li>';
                echo '<li>تأكد من تشغيل خدمة MySQL</li>';
                echo '<li>تحقق من إعدادات الاتصال</li>';
                echo '</ul>';
                echo '</div>';
            }
        } else {
        ?>
        
        <div class="status warning">
            <h3>⚠️ مشكلة في قاعدة البيانات</h3>
            <p>تم اكتشاف المشاكل التالية:</p>
            <ul style="margin: 1rem 0; padding-right: 2rem;">
                <li>❌ قاعدة البيانات غير متصلة</li>
                <li>❌ الجداول غير موجودة</li>
                <li>❌ حساب المدير غير موجود</li>
            </ul>
            <p><strong>سيتم إصلاح جميع هذه المشاكل تلقائياً.</strong></p>
        </div>
        
        <div class="status">
            <h3>📋 ما سيتم إنجازه:</h3>
            <ul style="margin: 1rem 0; padding-right: 2rem;">
                <li>🗄️ إنشاء قاعدة بيانات جديدة</li>
                <li>📋 إنشاء جميع الجداول المطلوبة</li>
                <li>👤 إنشاء حساب المدير</li>
                <li>🎬 إضافة محتوى تجريبي</li>
                <li>⚙️ تحديث ملفات الإعدادات</li>
            </ul>
        </div>
        
        <div class="actions">
            <form method="POST">
                <button type="submit" name="fix" class="btn">🔧 إصلاح قاعدة البيانات</button>
            </form>
            <a href="homepage.php" class="btn btn-secondary">🏠 العودة للرئيسية</a>
        </div>
        
        <?php } ?>
        
        <div class="actions">
            <a href="http://localhost/phpmyadmin/" target="_blank" class="btn btn-secondary">📊 phpMyAdmin</a>
            <a href="admin/dashboard.php" target="_blank" class="btn btn-secondary">🎛️ لوحة الإدارة</a>
            <a href="api/test.php" target="_blank" class="btn btn-secondary">🔗 اختبار API</a>
        </div>
    </div>
</body>
</html>
