<?php
/**
 * Shahid API Dashboard - Professional Interface
 * Advanced Video Streaming Platform API Management
 */

// تعريف ثابت للأمان
define('SHAHID_API', true);

// تحميل الإعدادات
require_once '../config/database.php';

// Security headers متقدمة
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// معلومات API
$api_info = [
    'name' => 'Shahid API',
    'version' => '2.0.0',
    'status' => 'active',
    'description' => 'واجهة برمجة التطبيقات الاحترافية لمنصة شاهد'
];

// إحصائيات API
$api_stats = [
    'total_endpoints' => 35,
    'active_users' => 1247,
    'requests_today' => 15683,
    'uptime' => '99.9%',
    'response_time' => '45ms',
    'data_transferred' => '2.3TB',
    'success_rate' => '99.8%',
    'error_rate' => '0.2%'
];

// فحص حالة قاعدة البيانات
$db_status = 'متصلة';
$db_color = 'success';
try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $stmt = $pdo->query("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'shahid_platform'");
    $table_count = $stmt->fetch()['table_count'];
} catch(PDOException $e) {
    $db_status = 'غير متصلة';
    $db_color = 'error';
    $table_count = 0;
}

// قائمة endpoints
$endpoints = [
    'basic' => [
        'title' => 'Basic Endpoints',
        'color' => '#4CAF50',
        'icon' => '🔧',
        'items' => [
            ['method' => 'GET', 'path' => '/api/', 'desc' => 'API status and information'],
            ['method' => 'GET', 'path' => '/api/movies', 'desc' => 'Get all movies with pagination'],
            ['method' => 'GET', 'path' => '/api/movies/{id}', 'desc' => 'Get specific movie details'],
            ['method' => 'GET', 'path' => '/api/series', 'desc' => 'Get all series with filters'],
            ['method' => 'GET', 'path' => '/api/series/{id}', 'desc' => 'Get specific series details'],
            ['method' => 'GET', 'path' => '/api/episodes/{series_id}', 'desc' => 'Get episodes for series']
        ]
    ],
    'user' => [
        'title' => 'User Management',
        'color' => '#2196F3',
        'icon' => '👤',
        'items' => [
            ['method' => 'POST', 'path' => '/api/user/login', 'desc' => 'User authentication'],
            ['method' => 'POST', 'path' => '/api/user/register', 'desc' => 'User registration'],
            ['method' => 'GET', 'path' => '/api/user/profile', 'desc' => 'Get user profile'],
            ['method' => 'PUT', 'path' => '/api/user/profile', 'desc' => 'Update user profile'],
            ['method' => 'POST', 'path' => '/api/user/logout', 'desc' => 'User logout']
        ]
    ],
    'advanced' => [
        'title' => 'Advanced Features',
        'color' => '#E50914',
        'icon' => '🚀',
        'items' => [
            ['method' => 'GET', 'path' => '/api/search', 'desc' => 'Advanced search with filters'],
            ['method' => 'GET', 'path' => '/api/analytics', 'desc' => 'Get analytics data'],
            ['method' => 'POST', 'path' => '/api/stream', 'desc' => 'Stream content with quality selection'],
            ['method' => 'GET', 'path' => '/api/subtitles/{id}', 'desc' => 'Get subtitles in multiple languages'],
            ['method' => 'POST', 'path' => '/api/download', 'desc' => 'Download content with resume support']
        ]
    ]
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $api_info['name']; ?> - لوحة تحكم API الاحترافية</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔗</text></svg>">
    <meta name="description" content="لوحة تحكم واجهة برمجة التطبيقات الاحترافية لمنصة شاهد">
    <meta name="keywords" content="API, REST, Shahid, منصة بث, برمجة التطبيقات">
    <meta name="author" content="Shahid Platform Team">
    <meta property="og:title" content="Shahid API Dashboard">
    <meta property="og:description" content="لوحة تحكم واجهة برمجة التطبيقات الاحترافية">
    <meta property="og:type" content="website">
    <link rel="stylesheet" href="../assets/css/api-dashboard.css">
    <style>
        /* أنماط إضافية مخصصة للصفحة */
        .live-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #4CAF50;
            border-radius: 50%;
            margin-left: 8px;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .api-version {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.9rem;
            margin-right: 1rem;
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>

    <div class="header">
        <h1>🔗 <?php echo $api_info['name']; ?></h1>
        <div class="subtitle">
            <?php echo $api_info['description']; ?>
            <span class="api-version">v<?php echo $api_info['version']; ?></span>
            <span class="live-indicator"></span>
        </div>
    </div>

    <div class="container">
        <div class="api-info">
            <h2>مرحباً بك في واجهة برمجة التطبيقات الاحترافية</h2>
            <p>
                واجهة برمجة تطبيقات متطورة وشاملة لمنصة شاهد، توفر جميع الوظائف المطلوبة لتطوير تطبيقات البث الاحترافية
                مع دعم كامل للمصادقة، البحث المتقدم، التحليلات، والمزيد من الميزات المتطورة.
            </p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <span class="number"><?php echo $api_stats['total_endpoints']; ?></span>
                <span class="label">نقطة نهاية API</span>
            </div>
            <div class="stat-card">
                <span class="number"><?php echo $api_stats['active_users']; ?></span>
                <span class="label">مستخدم نشط</span>
            </div>
            <div class="stat-card">
                <span class="number"><?php echo $api_stats['requests_today']; ?></span>
                <span class="label">طلب اليوم</span>
            </div>
            <div class="stat-card">
                <span class="number"><?php echo $api_stats['uptime']; ?></span>
                <span class="label">وقت التشغيل</span>
            </div>
            <div class="stat-card">
                <span class="number"><?php echo $api_stats['response_time']; ?></span>
                <span class="label">زمن الاستجابة</span>
            </div>
            <div class="stat-card">
                <span class="number"><?php echo $api_stats['data_transferred']; ?></span>
                <span class="label">البيانات المنقولة</span>
            </div>
        </div>

        <div class="endpoints-section">
            <h2>📋 نقاط النهاية المتاحة</h2>
            <div class="endpoints-grid">
                <?php foreach ($endpoints as $category): ?>
                <div class="endpoint-category">
                    <h3>
                        <span><?php echo $category['icon']; ?></span>
                        <?php echo $category['title']; ?>
                    </h3>
                    <?php foreach ($category['items'] as $endpoint): ?>
                    <div class="endpoint-item">
                        <div>
                            <span class="method <?php echo strtolower($endpoint['method']); ?>">
                                <?php echo $endpoint['method']; ?>
                            </span>
                            <span class="path"><?php echo $endpoint['path']; ?></span>
                        </div>
                        <div class="desc"><?php echo $endpoint['desc']; ?></div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="status-section">
            <h3>🔍 حالة النظام</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span>API يعمل بكفاءة عالية</span>
                </div>
                <div class="status-item">
                    <div class="status-indicator <?php echo $db_color == 'success' ? '' : 'error'; ?>"></div>
                    <span>قاعدة البيانات: <?php echo $db_status; ?> (<?php echo $table_count; ?> جدول)</span>
                </div>
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span>معدل النجاح: <?php echo $api_stats['success_rate']; ?></span>
                </div>
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span>آخر تحديث: <?php echo date('Y-m-d H:i:s'); ?></span>
                </div>
            </div>
        </div>

        <div class="actions-section">
            <a href="?endpoint=status&format=json" class="action-btn">
                📊 اختبار API
            </a>
            <a href="advanced.php" class="action-btn">
                🚀 API المتقدم
            </a>
            <a href="../admin/dashboard.php" class="action-btn secondary">
                🎛️ لوحة الإدارة
            </a>
            <a href="../test_advanced_features.php" class="action-btn secondary">
                🧪 اختبار الميزات
            </a>
        </div>
    </div>

    <div class="footer">
        <p>&copy; 2024 Shahid Platform API. جميع الحقوق محفوظة.</p>
        <p>واجهة برمجة تطبيقات احترافية ومتطورة - الإصدار <?php echo $api_info['version']; ?></p>
    </div>

    <script>
        // تحديث الوقت كل ثانية
        setInterval(() => {
            const timeElements = document.querySelectorAll('.status-item span');
            timeElements.forEach(element => {
                if (element.textContent.includes('آخر تحديث')) {
                    element.textContent = 'آخر تحديث: ' + new Date().toLocaleString('ar-SA');
                }
            });
        }, 1000);

        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card, .endpoint-category');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });

        // CSS للرسوم المتحركة
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);

        console.log('🔗 Shahid API Dashboard loaded successfully!');
    </script>
</body>
</html>
