# 🗄️ **دليل تحديث قاعدة البيانات - Shahid Platform**

## 🚀 **طرق تحديث قاعدة البيانات**

### **الطريقة الأولى: التحديث التلقائي (الأسهل)**

1. **افتح المتصفح وانتقل إلى:**
   ```
   http://localhost/amr2/flutter_module_1/backend/database/quick_update.php
   ```

2. **اضغط على زر "تحديث قاعدة البيانات"**

3. **انتظر حتى اكتمال العملية**

---

### **الطريقة الثانية: من الصفحة الرئيسية**

1. **افتح الصفحة الرئيسية:**
   ```
   http://localhost/amr2/flutter_module_1/backend/homepage.php
   ```

2. **اضغط على زر "🔍 فحص XAMPP"** للتأكد من الإعدادات

3. **اضغط على زر "🗄️ تحديث قاعدة البيانات"**

---

### **الطريقة الثالثة: استخدام ملف Batch (Windows)**

1. **انقر نقراً مزدوجاً على الملف:**
   ```
   update_database.bat
   ```

2. **اتبع التعليمات في النافذة**

---

### **الطريقة الرابعة: سطر الأوامر**

```bash
# انتقل إلى مجلد المشروع
cd C:\xampp\htdocs\amr2\flutter_module_1

# تشغيل سكريبت التحديث
C:\xampp\php\php.exe backend\database\update_database.php
```

---

## 🔍 **فحص XAMPP قبل التحديث**

### **تأكد من تشغيل الخدمات:**
- ✅ **Apache** - يجب أن يكون يعمل
- ✅ **MySQL** - يجب أن يكون يعمل

### **فحص الإعدادات:**
```
http://localhost/amr2/flutter_module_1/backend/database/check_xampp.php
```

---

## 📊 **ما يتم تحديثه في قاعدة البيانات:**

### **🎬 المحتوى:**
- **20 فيلم** عربي وعالمي مشهور
- **15 مسلسل** متنوع
- **15 تصنيف** مختلف

### **👥 المستخدمين:**
- **10 مستخدمين** تجريبيين
- **مدير النظام** (<EMAIL>)
- **اشتراكات متنوعة** (مجاني، أساسي، مميز)

### **📊 البيانات التفاعلية:**
- **تقييمات وآراء** للمحتوى
- **قوائم المفضلة**
- **سجل المشاهدة**
- **إشعارات النظام**

### **💳 المعاملات المالية:**
- **اشتراكات نشطة**
- **مدفوعات مكتملة**
- **فواتير وإيصالات**

### **🔒 الأمان والسجلات:**
- **سجلات الأمان**
- **سجلات الأداء**
- **سجلات الأخطاء**
- **بيانات SEO**

---

## ⚠️ **حل المشاكل الشائعة**

### **❌ خطأ: "Database connection failed"**
**الحل:**
1. تأكد من تشغيل MySQL في XAMPP
2. تحقق من أن المنفذ 3306 غير محجوب
3. أعد تشغيل XAMPP

### **❌ خطأ: "Access denied for user 'root'"**
**الحل:**
1. تأكد من أن كلمة مرور root فارغة في XAMPP
2. أو قم بتحديث إعدادات الاتصال في الملفات

### **❌ خطأ: "File not found"**
**الحل:**
1. تأكد من وجود ملفات schema.sql و production_data.sql
2. تحقق من مسار المشروع

### **❌ خطأ: "PHP extensions missing"**
**الحل:**
1. تأكد من تفعيل extensions في php.ini:
   - extension=pdo_mysql
   - extension=openssl
   - extension=curl
   - extension=gd

---

## 🔗 **روابط مفيدة بعد التحديث**

### **🌐 الواجهات الرئيسية:**
- **الصفحة الرئيسية:** `http://localhost/amr2/flutter_module_1/backend/homepage.php`
- **لوحة الإدارة:** `http://localhost/amr2/flutter_module_1/backend/admin/dashboard.php`
- **اختبار API:** `http://localhost/amr2/flutter_module_1/backend/api/test.php`

### **🛠️ أدوات الإدارة:**
- **phpMyAdmin:** `http://localhost/phpmyadmin/`
- **XAMPP Control:** `http://localhost/xampp/`

### **👤 بيانات تسجيل الدخول:**
- **المدير:** <EMAIL> / password123
- **مستخدم تجريبي:** <EMAIL> / password123

---

## 📈 **التحقق من نجاح التحديث**

### **1. فحص قاعدة البيانات:**
```sql
-- في phpMyAdmin
USE shahid_platform;
SHOW TABLES;
SELECT COUNT(*) FROM movies;
SELECT COUNT(*) FROM users;
```

### **2. اختبار API:**
```
http://localhost/amr2/flutter_module_1/backend/api/test.php
```

### **3. اختبار تسجيل الدخول:**
```
http://localhost/amr2/flutter_module_1/backend/auth/login.php
```

---

## 🎯 **النتائج المتوقعة بعد التحديث**

### ✅ **قاعدة بيانات كاملة مع:**
- 🎬 **35+ محتوى** (أفلام ومسلسلات)
- 👥 **10+ مستخدمين** بأدوار مختلفة
- 💳 **اشتراكات نشطة** ومدفوعات
- 📊 **بيانات تفاعلية** شاملة

### ✅ **نظام جاهز للاستخدام:**
- 🌐 **موقع ويب** يعمل بكامل الميزات
- 🎛️ **لوحة إدارة** احترافية
- 🔗 **API متقدم** للتطبيقات
- 📱 **تطبيق Flutter** متصل

---

## 📞 **الدعم والمساعدة**

إذا واجهت أي مشاكل:

1. **راجع سجلات الأخطاء** في XAMPP
2. **تحقق من إعدادات PHP** و MySQL
3. **تأكد من صحة مسارات الملفات**
4. **أعد تشغيل XAMPP** إذا لزم الأمر

---

<div align="center">

## 🎬 **Shahid Platform - قاعدة بيانات جاهزة للإنتاج!** ✨

[![Database Ready](https://img.shields.io/badge/Database-Ready-4CAF50?style=for-the-badge&logo=mysql)](http://localhost/phpmyadmin/)
[![Content Rich](https://img.shields.io/badge/Content-Rich-E50914?style=for-the-badge&logo=netflix)](http://localhost/amr2/flutter_module_1/backend/homepage.php)
[![Production Ready](https://img.shields.io/badge/Production-Ready-success?style=for-the-badge&logo=checkmarx)](http://localhost/amr2/flutter_module_1/backend/api/test.php)

**🎯 قاعدة بيانات محدثة... نظام جاهز... مشروع مكتمل! 🎯**

</div>
