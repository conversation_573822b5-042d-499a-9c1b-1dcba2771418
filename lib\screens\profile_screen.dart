import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../main.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _userProfile;
  List<Movie> _favoriteMovies = [];
  List<Series> _favoriteSeries = [];

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate loading user data
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock user data
      setState(() {
        _userProfile = {
          'name': 'مستخدم Shahid',
          'email': '<EMAIL>',
          'avatar': null,
          'subscription': 'Premium',
          'joinDate': '2024-01-01',
          'watchTime': '120 ساعة',
          'favoriteGenre': 'دراما',
        };
        
        // Mock favorite content
        _favoriteMovies = [
          Movie(
            id: 1,
            title: 'الفيل الأزرق',
            description: 'فيلم مصري من إخراج مروان حامد',
            year: 2014,
            duration: '170 دقيقة',
            genre: 'دراما، إثارة',
            rating: 8.2,
            ratingCount: 150,
            poster: 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Movie+1',
            status: 'active',
          ),
        ];
        
        _favoriteSeries = [
          Series(
            id: 1,
            title: 'الاختيار',
            description: 'مسلسل مصري يحكي قصص بطولية',
            year: 2020,
            seasons: 3,
            episodes: 90,
            episodeCount: 90,
            genre: 'دراما، تاريخي',
            rating: 9.1,
            ratingCount: 200,
            poster: 'https://via.placeholder.com/300x450/E50914/FFFFFF?text=Series+1',
            status: 'completed',
          ),
        ];
        
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const ShahidAppBar(title: 'الملف الشخصي'),
      body: _isLoading
          ? const LoadingWidget(message: 'جاري تحميل البيانات...')
          : RefreshIndicator(
              onRefresh: _loadUserData,
              color: const Color(0xFFE50914),
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    // Profile Header
                    _buildProfileHeader(),
                    
                    const SizedBox(height: 24),
                    
                    // Stats Section
                    _buildStatsSection(),
                    
                    const SizedBox(height: 24),
                    
                    // Favorites Section
                    _buildFavoritesSection(),
                    
                    const SizedBox(height: 24),
                    
                    // Settings Section
                    _buildSettingsSection(),
                    
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Avatar
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: const LinearGradient(
                colors: [Color(0xFFE50914), Color(0xFF8B0000)],
              ),
              border: Border.all(color: Colors.white, width: 3),
            ),
            child: _userProfile?['avatar'] != null
                ? ClipOval(
                    child: Image.network(
                      _userProfile!['avatar'],
                      fit: BoxFit.cover,
                    ),
                  )
                : const Icon(
                    Icons.person,
                    size: 50,
                    color: Colors.white,
                  ),
          ),
          
          const SizedBox(height: 16),
          
          // Name
          Text(
            _userProfile?['name'] ?? 'مستخدم',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Email
          Text(
            _userProfile?['email'] ?? '',
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Subscription Badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFFE50914),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.star,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  _userProfile?['subscription'] ?? 'Free',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF2F2F2F),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إحصائيات المشاهدة',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.access_time,
                  label: 'وقت المشاهدة',
                  value: _userProfile?['watchTime'] ?? '0 ساعة',
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.favorite,
                  label: 'المفضلة',
                  value: '${_favoriteMovies.length + _favoriteSeries.length}',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.calendar_today,
                  label: 'عضو منذ',
                  value: _userProfile?['joinDate'] ?? '2024',
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.category,
                  label: 'النوع المفضل',
                  value: _userProfile?['favoriteGenre'] ?? 'دراما',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: const Color(0xFFE50914),
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFavoritesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'المفضلة',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to full favorites list
                },
                child: const Text(
                  'عرض الكل',
                  style: TextStyle(color: Color(0xFFE50914)),
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Favorite Movies
        if (_favoriteMovies.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'الأفلام المفضلة',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 180,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _favoriteMovies.length,
              itemBuilder: (context, index) {
                final movie = _favoriteMovies[index];
                return ContentCard(
                  title: movie.title,
                  subtitle: '${movie.year}',
                  imageUrl: movie.poster,
                  rating: movie.rating,
                  width: 120,
                  height: 180,
                  onTap: () {
                    // Show movie details
                  },
                );
              },
            ),
          ),
          const SizedBox(height: 16),
        ],
        
        // Favorite Series
        if (_favoriteSeries.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'المسلسلات المفضلة',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 180,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _favoriteSeries.length,
              itemBuilder: (context, index) {
                final series = _favoriteSeries[index];
                return ContentCard(
                  title: series.title,
                  subtitle: '${series.seasons} مواسم',
                  imageUrl: series.poster,
                  rating: series.rating,
                  width: 120,
                  height: 180,
                  onTap: () {
                    // Show series details
                  },
                );
              },
            ),
          ),
        ],
        
        // Empty state
        if (_favoriteMovies.isEmpty && _favoriteSeries.isEmpty) ...[
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: const Color(0xFF2F2F2F),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Column(
                children: [
                  Icon(
                    Icons.favorite_border,
                    color: Colors.grey,
                    size: 48,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'لا توجد عناصر في المفضلة',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'ابدأ بإضافة الأفلام والمسلسلات المفضلة لديك',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSettingsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF2F2F2F),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildSettingItem(
            icon: Icons.edit,
            title: 'تعديل الملف الشخصي',
            onTap: () {
              _showEditProfileDialog();
            },
          ),
          _buildSettingItem(
            icon: Icons.notifications,
            title: 'الإشعارات',
            onTap: () {
              // Navigate to notifications settings
            },
          ),
          _buildSettingItem(
            icon: Icons.download,
            title: 'التحميلات',
            onTap: () {
              // Navigate to downloads
            },
          ),
          _buildSettingItem(
            icon: Icons.language,
            title: 'اللغة',
            subtitle: 'العربية',
            onTap: () {
              // Show language selection
            },
          ),
          _buildSettingItem(
            icon: Icons.help,
            title: 'المساعدة والدعم',
            onTap: () {
              // Navigate to help
            },
          ),
          _buildSettingItem(
            icon: Icons.info,
            title: 'حول التطبيق',
            onTap: () {
              _showAboutDialog();
            },
          ),
          _buildSettingItem(
            icon: Icons.logout,
            title: 'تسجيل الخروج',
            textColor: Colors.red,
            onTap: () {
              _showLogoutDialog();
            },
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    String? subtitle,
    Color? textColor,
    required VoidCallback onTap,
    bool showDivider = true,
  }) {
    return Column(
      children: [
        ListTile(
          leading: Icon(
            icon,
            color: textColor ?? Colors.white,
          ),
          title: Text(
            title,
            style: TextStyle(
              color: textColor ?? Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          subtitle: subtitle != null
              ? Text(
                  subtitle,
                  style: const TextStyle(color: Colors.grey),
                )
              : null,
          trailing: const Icon(
            Icons.arrow_forward_ios,
            color: Colors.grey,
            size: 16,
          ),
          onTap: onTap,
        ),
        if (showDivider)
          Divider(
            color: Colors.grey[700],
            height: 1,
            indent: 56,
          ),
      ],
    );
  }

  void _showEditProfileDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2F2F2F),
        title: const Text(
          'تعديل الملف الشخصي',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'هذه الميزة ستكون متاحة قريباً',
          style: TextStyle(color: Colors.grey),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'حسناً',
              style: TextStyle(color: Color(0xFFE50914)),
            ),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2F2F2F),
        title: const Text(
          'حول Shahid',
          style: TextStyle(color: Colors.white),
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'منصة البث الاحترافية',
              style: TextStyle(
                color: Color(0xFFE50914),
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'الإصدار: 1.0.0',
              style: TextStyle(color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'تطبيق Shahid يوفر لك أفضل تجربة مشاهدة للأفلام والمسلسلات العربية والعالمية بجودة عالية.',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'حسناً',
              style: TextStyle(color: Color(0xFFE50914)),
            ),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2F2F2F),
        title: const Text(
          'تسجيل الخروج',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'إلغاء',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تسجيل الخروج بنجاح'),
                  backgroundColor: Color(0xFFE50914),
                ),
              );
            },
            child: const Text(
              'تسجيل الخروج',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
