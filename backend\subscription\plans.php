<?php
/**
 * Shahid Platform - Subscription Plans
 * Complete Payment and Subscription System
 */

session_start();
require_once '../config/database.php';

// Database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Get current user subscription if logged in
$current_subscription = null;
if (isset($_SESSION['user_id'])) {
    $stmt = $pdo->prepare("SELECT subscription_type, subscription_end FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $current_subscription = $stmt->fetch(PDO::FETCH_ASSOC);
}

// Handle subscription purchase
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_SESSION['user_id'])) {
    $plan = $_POST['plan'] ?? '';
    $payment_method = $_POST['payment_method'] ?? '';
    
    if (in_array($plan, ['basic', 'premium']) && in_array($payment_method, ['credit_card', 'paypal', 'bank_transfer'])) {
        $result = processPurchase($pdo, $_SESSION['user_id'], $plan, $payment_method);
        if ($result['success']) {
            $message = 'تم تفعيل الاشتراك بنجاح! مرحباً بك في عالم شاهد المميز.';
            // Refresh current subscription
            $stmt = $pdo->prepare("SELECT subscription_type, subscription_end FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $current_subscription = $stmt->fetch(PDO::FETCH_ASSOC);
        } else {
            $error = $result['error'];
        }
    } else {
        $error = 'بيانات غير صحيحة. يرجى المحاولة مرة أخرى.';
    }
}

function processPurchase($pdo, $user_id, $plan, $payment_method) {
    try {
        $pdo->beginTransaction();
        
        // Calculate price and duration
        $prices = ['basic' => 49.99, 'premium' => 99.99];
        $durations = ['basic' => 6, 'premium' => 12]; // months
        
        $price = $prices[$plan];
        $duration = $durations[$plan];
        $end_date = date('Y-m-d H:i:s', strtotime("+$duration months"));
        
        // Update user subscription
        $stmt = $pdo->prepare("
            UPDATE users 
            SET subscription_type = ?, subscription_end = ? 
            WHERE id = ?
        ");
        $stmt->execute([$plan, $end_date, $user_id]);
        
        // Create subscription record
        $stmt = $pdo->prepare("
            INSERT INTO subscriptions (user_id, plan_type, price, start_date, end_date, status, payment_method, created_at) 
            VALUES (?, ?, ?, NOW(), ?, 'active', ?, NOW())
        ");
        $stmt->execute([$user_id, $plan, $price, $end_date, $payment_method]);
        $subscription_id = $pdo->lastInsertId();
        
        // Create payment record
        $transaction_id = 'txn_' . time() . '_' . rand(1000, 9999);
        $stmt = $pdo->prepare("
            INSERT INTO payments (user_id, subscription_id, amount, currency, payment_method, transaction_id, status, created_at) 
            VALUES (?, ?, ?, 'USD', ?, ?, 'completed', NOW())
        ");
        $stmt->execute([$user_id, $subscription_id, $price, $payment_method, $transaction_id]);
        
        $pdo->commit();
        return ['success' => true];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        return ['success' => false, 'error' => 'حدث خطأ أثناء معالجة الدفع: ' . $e->getMessage()];
    }
}

// Get subscription statistics
$stats = [];
$stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE subscription_type = 'premium' AND status = 'active'");
$stats['premium_users'] = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE subscription_type = 'basic' AND status = 'active'");
$stats['basic_users'] = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE subscription_type = 'free' AND status = 'active'");
$stats['free_users'] = $stmt->fetchColumn();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطط الاشتراك - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
            padding: 3rem 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 3rem 2rem;
        }
        
        .current-plan {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 3rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
            text-align: center;
        }
        
        .current-plan h2 {
            color: #E50914;
            margin-bottom: 1rem;
        }
        
        .current-plan-info {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }
        
        .plan-info-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem 2rem;
            border-radius: 10px;
            border: 1px solid rgba(229, 9, 20, 0.3);
        }
        
        .plans-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .plan-card {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 20px;
            padding: 2.5rem;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .plan-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(229, 9, 20, 0.3);
        }
        
        .plan-card.recommended {
            border-color: #E50914;
            background: linear-gradient(135deg, rgba(229, 9, 20, 0.1) 0%, rgba(47, 47, 47, 0.9) 100%);
        }
        
        .plan-card.recommended::before {
            content: 'الأكثر شعبية';
            position: absolute;
            top: 1rem;
            right: -2rem;
            background: #E50914;
            color: white;
            padding: 0.5rem 3rem;
            transform: rotate(45deg);
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .plan-name {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #E50914;
        }
        
        .plan-price {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .plan-period {
            color: #ccc;
            margin-bottom: 2rem;
        }
        
        .plan-features {
            list-style: none;
            margin-bottom: 2rem;
            text-align: right;
        }
        
        .plan-features li {
            padding: 0.8rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .plan-features li:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            color: #4CAF50;
            font-size: 1.2rem;
            min-width: 20px;
        }
        
        .feature-icon.disabled {
            color: #666;
        }
        
        .plan-btn {
            width: 100%;
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            padding: 1.2rem 2rem;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .plan-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
        }
        
        .plan-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .plan-btn.current {
            background: #4CAF50;
        }
        
        .payment-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
        }
        
        .payment-content {
            background: rgba(47, 47, 47, 0.95);
            margin: 5% auto;
            padding: 3rem;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            border: 2px solid rgba(229, 9, 20, 0.3);
        }
        
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #E50914;
        }
        
        .payment-form {
            margin-top: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #ccc;
            font-weight: bold;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 1px solid #555;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.3);
            color: #fff;
            font-size: 1rem;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #E50914;
            box-shadow: 0 0 10px rgba(229, 9, 20, 0.3);
        }
        
        .payment-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .payment-method {
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid #555;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-method:hover,
        .payment-method.selected {
            border-color: #E50914;
            background: rgba(229, 9, 20, 0.1);
        }
        
        .payment-method-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .stats-section {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 3rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .stats-section h3 {
            color: #E50914;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }
        
        .stat-item {
            text-align: center;
            background: rgba(0, 0, 0, 0.3);
            padding: 1.5rem;
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #E50914;
            display: block;
        }
        
        .stat-label {
            color: #ccc;
            margin-top: 0.5rem;
        }
        
        .message {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
            color: #F44336;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .back-link {
            display: inline-block;
            background: linear-gradient(45deg, #555, #333);
            color: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            text-decoration: none;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }
        
        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            color: white;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .plans-grid {
                grid-template-columns: 1fr;
            }
            
            .current-plan-info {
                flex-direction: column;
                gap: 1rem;
            }
            
            .payment-content {
                margin: 10% auto;
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>💎 خطط الاشتراك</h1>
        <p>اختر الخطة المناسبة لك واستمتع بأفضل المحتوى</p>
    </div>

    <div class="container">
        <a href="../index.php" class="back-link">← العودة إلى الرئيسية</a>
        
        <?php if ($message): ?>
            <div class="message"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <?php if ($current_subscription): ?>
        <div class="current-plan">
            <h2>اشتراكك الحالي</h2>
            <div class="current-plan-info">
                <div class="plan-info-item">
                    <strong>النوع:</strong> 
                    <?php 
                    echo $current_subscription['subscription_type'] === 'premium' ? 'مميز' : 
                        ($current_subscription['subscription_type'] === 'basic' ? 'أساسي' : 'مجاني'); 
                    ?>
                </div>
                <?php if ($current_subscription['subscription_end']): ?>
                <div class="plan-info-item">
                    <strong>ينتهي في:</strong> 
                    <?php echo date('Y-m-d', strtotime($current_subscription['subscription_end'])); ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <div class="plans-grid">
            <!-- Free Plan -->
            <div class="plan-card">
                <div class="plan-name">مجاني</div>
                <div class="plan-price">$0</div>
                <div class="plan-period">مجاناً للأبد</div>
                <ul class="plan-features">
                    <li><span class="feature-icon">✅</span> مشاهدة الإعلانات</li>
                    <li><span class="feature-icon">✅</span> محتوى محدود</li>
                    <li><span class="feature-icon disabled">❌</span> بدون إعلانات</li>
                    <li><span class="feature-icon disabled">❌</span> جودة عالية</li>
                    <li><span class="feature-icon disabled">❌</span> تحميل للمشاهدة لاحقاً</li>
                    <li><span class="feature-icon disabled">❌</span> محتوى حصري</li>
                </ul>
                <button class="plan-btn" disabled>الخطة الحالية</button>
            </div>

            <!-- Basic Plan -->
            <div class="plan-card">
                <div class="plan-name">أساسي</div>
                <div class="plan-price">$49.99</div>
                <div class="plan-period">كل 6 أشهر</div>
                <ul class="plan-features">
                    <li><span class="feature-icon">✅</span> مشاهدة بدون إعلانات</li>
                    <li><span class="feature-icon">✅</span> جودة HD</li>
                    <li><span class="feature-icon">✅</span> مشاهدة على جهازين</li>
                    <li><span class="feature-icon">✅</span> مكتبة أفلام ومسلسلات</li>
                    <li><span class="feature-icon disabled">❌</span> محتوى حصري</li>
                    <li><span class="feature-icon disabled">❌</span> جودة 4K</li>
                </ul>
                <button class="plan-btn" onclick="openPaymentModal('basic', 49.99)"
                    <?php echo ($current_subscription && $current_subscription['subscription_type'] === 'basic') ? 'disabled' : ''; ?>>
                    <?php echo ($current_subscription && $current_subscription['subscription_type'] === 'basic') ? 'الخطة الحالية' : 'اشترك الآن'; ?>
                </button>
            </div>

            <!-- Premium Plan -->
            <div class="plan-card recommended">
                <div class="plan-name">مميز</div>
                <div class="plan-price">$99.99</div>
                <div class="plan-period">كل 12 شهر</div>
                <ul class="plan-features">
                    <li><span class="feature-icon">✅</span> جميع ميزات الأساسي</li>
                    <li><span class="feature-icon">✅</span> محتوى حصري ومميز</li>
                    <li><span class="feature-icon">✅</span> جودة 4K Ultra HD</li>
                    <li><span class="feature-icon">✅</span> مشاهدة على 4 أجهزة</li>
                    <li><span class="feature-icon">✅</span> تحميل للمشاهدة لاحقاً</li>
                    <li><span class="feature-icon">✅</span> دعم فني مميز</li>
                </ul>
                <button class="plan-btn" onclick="openPaymentModal('premium', 99.99)"
                    <?php echo ($current_subscription && $current_subscription['subscription_type'] === 'premium') ? 'disabled' : ''; ?>>
                    <?php echo ($current_subscription && $current_subscription['subscription_type'] === 'premium') ? 'الخطة الحالية' : 'اشترك الآن'; ?>
                </button>
            </div>
        </div>

        <div class="stats-section">
            <h3>إحصائيات المنصة</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number"><?php echo number_format($stats['premium_users']); ?></span>
                    <div class="stat-label">مشترك مميز</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo number_format($stats['basic_users']); ?></span>
                    <div class="stat-label">مشترك أساسي</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo number_format($stats['free_users']); ?></span>
                    <div class="stat-label">مستخدم مجاني</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">1000+</span>
                    <div class="stat-label">ساعة محتوى</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Modal -->
    <div id="paymentModal" class="payment-modal">
        <div class="payment-content">
            <span class="close" onclick="closePaymentModal()">&times;</span>
            <h2>إتمام الاشتراك</h2>
            <form method="POST" class="payment-form">
                <input type="hidden" name="plan" id="selectedPlan">
                <input type="hidden" name="payment_method" id="selectedPaymentMethod">
                
                <div class="form-group">
                    <label>الخطة المختارة:</label>
                    <div id="planDetails" style="background: rgba(229, 9, 20, 0.1); padding: 1rem; border-radius: 8px; border: 1px solid #E50914;"></div>
                </div>
                
                <div class="form-group">
                    <label>طريقة الدفع:</label>
                    <div class="payment-methods">
                        <div class="payment-method" onclick="selectPaymentMethod('credit_card')">
                            <div class="payment-method-icon">💳</div>
                            <div>بطاقة ائتمان</div>
                        </div>
                        <div class="payment-method" onclick="selectPaymentMethod('paypal')">
                            <div class="payment-method-icon">🅿️</div>
                            <div>PayPal</div>
                        </div>
                        <div class="payment-method" onclick="selectPaymentMethod('bank_transfer')">
                            <div class="payment-method-icon">🏦</div>
                            <div>تحويل بنكي</div>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="plan-btn" id="paymentSubmitBtn" disabled>إتمام الدفع</button>
            </form>
        </div>
    </div>

    <script>
        let selectedPlan = '';
        let selectedPrice = 0;

        function openPaymentModal(plan, price) {
            <?php if (!isset($_SESSION['user_id'])): ?>
                alert('يجب تسجيل الدخول أولاً');
                window.location.href = '../auth/login.php';
                return;
            <?php endif; ?>
            
            selectedPlan = plan;
            selectedPrice = price;
            
            document.getElementById('selectedPlan').value = plan;
            document.getElementById('planDetails').innerHTML = `
                <strong>${plan === 'basic' ? 'الخطة الأساسية' : 'الخطة المميزة'}</strong><br>
                السعر: $${price}<br>
                المدة: ${plan === 'basic' ? '6 أشهر' : '12 شهر'}
            `;
            
            document.getElementById('paymentModal').style.display = 'block';
        }

        function closePaymentModal() {
            document.getElementById('paymentModal').style.display = 'none';
            // Reset selections
            document.querySelectorAll('.payment-method').forEach(method => {
                method.classList.remove('selected');
            });
            document.getElementById('paymentSubmitBtn').disabled = true;
        }

        function selectPaymentMethod(method) {
            document.querySelectorAll('.payment-method').forEach(m => {
                m.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
            
            document.getElementById('selectedPaymentMethod').value = method;
            document.getElementById('paymentSubmitBtn').disabled = false;
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('paymentModal');
            if (event.target === modal) {
                closePaymentModal();
            }
        }

        console.log('💎 Subscription Plans loaded successfully!');
    </script>
</body>
</html>
