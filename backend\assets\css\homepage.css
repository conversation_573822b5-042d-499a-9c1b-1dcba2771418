/* 
 * Shahid Platform Homepage Styles
 * تصميم متطور ومتجاوب للصفحة الرئيسية
 */

/* إعدادات أساسية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
    color: #fff;
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    direction: rtl;
}

/* خلفية متحركة */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0.1;
}

.floating-icon {
    position: absolute;
    font-size: 2rem;
    color: #E50914;
    animation: float 6s ease-in-out infinite;
    user-select: none;
}

.floating-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
.floating-icon:nth-child(2) { top: 20%; right: 10%; animation-delay: 1s; }
.floating-icon:nth-child(3) { bottom: 20%; left: 15%; animation-delay: 2s; }
.floating-icon:nth-child(4) { bottom: 10%; right: 20%; animation-delay: 3s; }
.floating-icon:nth-child(5) { top: 50%; left: 5%; animation-delay: 4s; }
.floating-icon:nth-child(6) { top: 60%; right: 5%; animation-delay: 5s; }

@keyframes float {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg); 
        opacity: 0.7;
    }
    50% { 
        transform: translateY(-20px) rotate(180deg); 
        opacity: 1;
    }
}

/* رأس الصفحة */
.header {
    background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
    padding: 3rem 0;
    text-align: center;
    box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="white" opacity="0.1"/></svg>') repeat;
    animation: sparkle 3s linear infinite;
}

@keyframes sparkle {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(-100px) translateY(-100px); }
}

.header h1 {
    font-size: 4rem;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    position: relative;
    z-index: 2;
    font-weight: 900;
}

.header .subtitle {
    font-size: 1.5rem;
    opacity: 0.9;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
    font-weight: 300;
}

.header .completion-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: bold;
    position: relative;
    z-index: 2;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* الحاوي الرئيسي */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* قسم الترحيب */
.welcome-section {
    text-align: center;
    margin-bottom: 4rem;
    padding: 2rem;
}

.welcome-section h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #E50914, #FF6B35);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.welcome-section p {
    font-size: 1.3rem;
    color: #ccc;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
    font-weight: 300;
}

/* شبكة الميزات */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.feature-card {
    background: rgba(47, 47, 47, 0.9);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(229, 9, 20, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(229, 9, 20, 0.1), transparent);
    transition: left 0.5s ease;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(229, 9, 20, 0.3);
    border-color: rgba(229, 9, 20, 0.5);
}

.feature-card .icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    display: block;
    filter: drop-shadow(0 4px 8px rgba(229, 9, 20, 0.3));
}

.feature-card h3 {
    color: #E50914;
    margin-bottom: 1rem;
    font-size: 1.8rem;
    font-weight: 700;
}

.feature-card p {
    color: #ccc;
    font-size: 1.1rem;
    line-height: 1.6;
    font-weight: 300;
}

/* قسم الإحصائيات */
.stats-section {
    background: rgba(47, 47, 47, 0.9);
    border-radius: 20px;
    padding: 3rem;
    margin-bottom: 4rem;
    border: 1px solid rgba(229, 9, 20, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
}

.stats-section h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    background: linear-gradient(45deg, #E50914, #FF6B35);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 2rem;
    text-align: center;
}

.stat-item {
    background: rgba(229, 9, 20, 0.1);
    border-radius: 15px;
    padding: 2rem 1rem;
    border: 1px solid rgba(229, 9, 20, 0.3);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.stat-item:hover {
    transform: scale(1.05) translateY(-5px);
    background: rgba(229, 9, 20, 0.2);
    box-shadow: 0 10px 25px rgba(229, 9, 20, 0.3);
}

.stat-item .number {
    font-size: 3rem;
    font-weight: 900;
    color: #E50914;
    display: block;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(229, 9, 20, 0.3);
}

.stat-item .label {
    color: #ccc;
    font-size: 1.1rem;
    font-weight: 500;
}

/* قسم الإجراءات */
.actions-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 4rem;
}

.action-btn {
    background: linear-gradient(45deg, #E50914, #B8070F);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-decoration: none;
    text-align: center;
    font-weight: bold;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(229, 9, 20, 0.3);
    position: relative;
    overflow: hidden;
    display: block;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 35px rgba(229, 9, 20, 0.4);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(45deg, #555, #333);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.action-btn.secondary:hover {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

/* قسم الحالة */
.status-section {
    background: rgba(47, 47, 47, 0.9);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 3rem;
    border: 1px solid rgba(229, 9, 20, 0.2);
    backdrop-filter: blur(10px);
}

.status-section h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: #E50914;
    font-size: 1.8rem;
    font-weight: 700;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.status-item:hover {
    background: rgba(0, 0, 0, 0.5);
    transform: translateX(5px);
}

.status-indicator {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #4CAF50;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
    animation: pulse 2s infinite;
    flex-shrink: 0;
}

.status-indicator.error {
    background: #F44336;
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
}

@keyframes pulse {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.5; 
        transform: scale(1.1);
    }
}

/* التذييل */
.footer {
    text-align: center;
    padding: 3rem;
    color: #999;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 3rem;
    background: rgba(0, 0, 0, 0.2);
}

.footer p {
    margin-bottom: 0.5rem;
    font-weight: 300;
}

/* الرسوم المتحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes glow {
    0%, 100% {
        text-shadow: 0 0 5px rgba(229, 9, 20, 0.5);
    }
    50% {
        text-shadow: 0 0 20px rgba(229, 9, 20, 0.8);
    }
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .header h1 {
        font-size: 2.5rem;
    }
    
    .header .subtitle {
        font-size: 1.2rem;
    }
    
    .welcome-section h2 {
        font-size: 2rem;
    }
    
    .welcome-section p {
        font-size: 1.1rem;
    }
    
    .container {
        padding: 1rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .feature-card {
        padding: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .stat-item {
        padding: 1.5rem 1rem;
    }
    
    .stat-item .number {
        font-size: 2.5rem;
    }
    
    .actions-section {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .action-btn {
        padding: 1.5rem;
        font-size: 1.1rem;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .floating-icon {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 2rem 0;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .welcome-section h2 {
        font-size: 1.8rem;
    }
    
    .feature-card .icon {
        font-size: 3rem;
    }
    
    .feature-card h3 {
        font-size: 1.5rem;
    }
    
    .stats-section h2 {
        font-size: 2rem;
    }
    
    .stat-item .number {
        font-size: 2rem;
    }
}

/* تحسينات إضافية للأداء */
.feature-card,
.stat-item,
.action-btn,
.status-item {
    will-change: transform;
}

/* تأثيرات التركيز للوصولية */
.action-btn:focus,
.feature-card:focus {
    outline: 2px solid #E50914;
    outline-offset: 2px;
}

/* تحسين الخطوط */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
