/* Shahid - Main Stylesheet */

:root {
    --primary-color: #e50914;
    --secondary-color: #221f1f;
    --dark-color: #141414;
    --light-color: #f5f5f5;
    --text-color: #ffffff;
    --text-muted: #999999;
    --border-color: #333333;
    --gradient-primary: linear-gradient(135deg, #e50914 0%, #b20710 100%);
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--dark-color);
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
}

.main-content {
    padding-top: 76px; /* Account for fixed navbar */
    min-height: calc(100vh - 76px);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1.5rem;
}

/* Navigation */
.navbar {
    background: rgba(20, 20, 20, 0.95) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.navbar-brand img {
    filter: brightness(1.1);
}

.navbar-nav .nav-link {
    color: var(--text-color) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

.dropdown-menu {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.dropdown-item {
    color: var(--text-color);
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: var(--text-color);
}

/* Search */
.search-container {
    position: relative;
    width: 300px;
}

.search-container input {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 25px;
    padding: 0.5rem 1rem;
}

.search-container input:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    color: var(--text-color);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    display: none;
}

.suggestion-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    color: var(--text-color);
    text-decoration: none;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.suggestion-item:hover {
    background-color: var(--primary-color);
    color: var(--text-color);
}

.suggestion-item img {
    width: 40px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    margin-left: 0.75rem;
}

.suggestion-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.suggestion-meta {
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    overflow: hidden;
}

.hero-slide {
    height: 100vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        rgba(20, 20, 20, 0.8) 0%,
        rgba(20, 20, 20, 0.4) 50%,
        rgba(20, 20, 20, 0.8) 100%
    );
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.hero-meta .badge {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

.hero-actions .btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 25px;
    transition: var(--transition);
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    opacity: 0.7;
    transition: var(--transition);
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
}

.carousel-indicators {
    bottom: 2rem;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 0.25rem;
}

/* Content Cards */
.content-section {
    margin-bottom: 3rem;
}

.section-header {
    margin-bottom: 2rem;
}

.content-card {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    background: var(--secondary-color);
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

.card-image {
    position: relative;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: var(--transition);
}

.content-card:hover .card-image img {
    transform: scale(1.05);
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.content-card:hover .card-overlay {
    opacity: 1;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-actions .btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.premium-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.card-info {
    padding: 1rem;
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.card-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.card-title a:hover {
    color: var(--primary-color);
}

.card-meta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.card-meta .rating {
    color: #ffc107;
}

/* Buttons */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    border-radius: 25px;
    font-weight: 600;
    transition: var(--transition);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #b20710 0%, #8a0508 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(229, 9, 20, 0.4);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    border-radius: 25px;
    font-weight: 600;
    transition: var(--transition);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Footer */
.footer {
    background: var(--secondary-color) !important;
    border-top: 1px solid var(--border-color);
    margin-top: auto;
}

.footer h5, .footer h6 {
    color: var(--text-color);
    margin-bottom: 1rem;
}

.footer .text-muted {
    color: var(--text-muted) !important;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

.payment-methods img {
    opacity: 0.7;
    transition: var(--transition);
}

.payment-methods img:hover {
    opacity: 1;
}

/* Back to Top */
.btn-back-to-top {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.btn-back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(229, 9, 20, 0.4);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(20, 20, 20, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

/* Toast Notifications */
.toast-notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 600;
    z-index: 1050;
    display: none;
    box-shadow: var(--shadow);
}

.toast-success {
    background: #28a745;
}

.toast-error {
    background: #dc3545;
}

.toast-warning {
    background: #ffc107;
    color: #212529;
}

.toast-info {
    background: #17a2b8;
}

/* CTA Section */
.cta-section {
    background: var(--gradient-primary) !important;
    margin: 3rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .search-container {
        width: 200px;
    }
    
    .content-card .card-image img {
        height: 250px;
    }
    
    .btn-back-to-top {
        bottom: 1rem;
        left: 1rem;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 576px) {
    .hero-content {
        text-align: center;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .search-container {
        width: 150px;
    }
    
    .content-card .card-image img {
        height: 200px;
    }
}
