<?php
/**
 * Add Advanced Sample Data - Shahid Platform
 * Add episodes, categories, and more detailed content
 */

$config = include 'config/database.php';

try {
    $dsn = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>🚀 إضافة بيانات متقدمة - Shahid Platform</h1>";
    
    // Add Categories
    echo "<h2>📂 إضافة التصنيفات:</h2>";
    
    $categories = [
        ['name' => 'دراما', 'name_en' => 'Drama', 'description' => 'أفلام ومسلسلات درامية'],
        ['name' => 'كوميديا', 'name_en' => 'Comedy', 'description' => 'محتوى كوميدي مضحك'],
        ['name' => 'أكشن', 'name_en' => 'Action', 'description' => 'أفلام الحركة والإثارة'],
        ['name' => 'رومانسي', 'name_en' => 'Romance', 'description' => 'قصص الحب والرومانسية'],
        ['name' => 'إثارة', 'name_en' => 'Thriller', 'description' => 'أفلام الإثارة والتشويق'],
        ['name' => 'خيال علمي', 'name_en' => 'Sci-Fi', 'description' => 'أفلام الخيال العلمي'],
        ['name' => 'رعب', 'name_en' => 'Horror', 'description' => 'أفلام الرعب والخوف'],
        ['name' => 'وثائقي', 'name_en' => 'Documentary', 'description' => 'أفلام وثائقية تعليمية']
    ];
    
    // Create categories table if not exists
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            name_en VARCHAR(100) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_name (name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    $categoryStmt = $pdo->prepare("INSERT IGNORE INTO categories (name, name_en, description) VALUES (?, ?, ?)");
    
    foreach ($categories as $category) {
        $categoryStmt->execute([$category['name'], $category['name_en'], $category['description']]);
        echo "<div class='success'>✅ تم إضافة التصنيف: " . htmlspecialchars($category['name']) . "</div>";
    }
    
    // Add Episodes for Series
    echo "<h2>📺 إضافة حلقات المسلسلات:</h2>";
    
    // Create episodes table if not exists
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS episodes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            series_id INT NOT NULL,
            season INT NOT NULL DEFAULT 1,
            episode INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            title_en VARCHAR(255),
            description TEXT,
            duration INT DEFAULT 45,
            air_date DATE,
            video_url VARCHAR(500),
            thumbnail VARCHAR(500),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE,
            UNIQUE KEY unique_episode (series_id, season, episode)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Get series IDs
    $seriesResult = $pdo->query("SELECT id, title FROM series LIMIT 3");
    $seriesData = $seriesResult->fetchAll(PDO::FETCH_ASSOC);
    
    $episodeStmt = $pdo->prepare("
        INSERT IGNORE INTO episodes (series_id, season, episode, title, title_en, description, duration, air_date, video_url, thumbnail) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($seriesData as $series) {
        // Add 5 episodes for each series
        for ($ep = 1; $ep <= 5; $ep++) {
            $episodeData = [
                $series['id'],
                1, // season
                $ep, // episode number
                "الحلقة $ep - " . $series['title'],
                "Episode $ep - " . $series['title'],
                "وصف الحلقة رقم $ep من مسلسل " . $series['title'],
                45 + rand(-10, 15), // duration with variation
                date('Y-m-d', strtotime("-" . (30 - $ep * 7) . " days")), // air_date
                "https://example.com/videos/series_{$series['id']}_s1_e{$ep}.mp4",
                "https://via.placeholder.com/640x360/E50914/FFFFFF?text=Episode+{$ep}"
            ];
            
            $episodeStmt->execute($episodeData);
            echo "<div class='success'>✅ تم إضافة الحلقة $ep من مسلسل: " . htmlspecialchars($series['title']) . "</div>";
        }
    }
    
    // Add User Ratings
    echo "<h2>⭐ إضافة تقييمات المستخدمين:</h2>";
    
    // Create ratings table if not exists
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS ratings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            content_type ENUM('movie', 'series') NOT NULL,
            content_id INT NOT NULL,
            rating DECIMAL(2,1) NOT NULL CHECK (rating >= 1 AND rating <= 10),
            review TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_rating (user_id, content_type, content_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Get user ID (assuming we have at least one user)
    $userResult = $pdo->query("SELECT id FROM users LIMIT 1");
    $userId = $userResult->fetchColumn();
    
    if ($userId) {
        // Get movies and series for ratings
        $moviesResult = $pdo->query("SELECT id, title FROM movies LIMIT 5");
        $movies = $moviesResult->fetchAll(PDO::FETCH_ASSOC);
        
        $seriesResult = $pdo->query("SELECT id, title FROM series LIMIT 3");
        $series = $seriesResult->fetchAll(PDO::FETCH_ASSOC);
        
        $ratingStmt = $pdo->prepare("
            INSERT IGNORE INTO ratings (user_id, content_type, content_id, rating, review) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        // Add movie ratings
        foreach ($movies as $movie) {
            $rating = rand(70, 95) / 10; // Random rating between 7.0 and 9.5
            $reviews = [
                "فيلم رائع ومؤثر، أنصح بمشاهدته",
                "قصة جميلة وأداء ممتاز من الممثلين",
                "فيلم يستحق المشاهدة، إخراج متميز",
                "محتوى ممتاز وقيم عالية",
                "تجربة مشاهدة ممتعة ومفيدة"
            ];
            
            $ratingStmt->execute([
                $userId,
                'movie',
                $movie['id'],
                $rating,
                $reviews[array_rand($reviews)]
            ]);
            
            echo "<div class='success'>✅ تم إضافة تقييم للفيلم: " . htmlspecialchars($movie['title']) . " ($rating/10)</div>";
        }
        
        // Add series ratings
        foreach ($series as $serie) {
            $rating = rand(75, 98) / 10; // Random rating between 7.5 and 9.8
            $reviews = [
                "مسلسل ممتاز بقصة مشوقة",
                "أحداث مثيرة وشخصيات قوية",
                "مسلسل يستحق المتابعة حتى النهاية",
                "إنتاج عالي الجودة وأداء رائع",
                "قصة محبوكة بإتقان ومؤثرة"
            ];
            
            $ratingStmt->execute([
                $userId,
                'series',
                $serie['id'],
                $rating,
                $reviews[array_rand($reviews)]
            ]);
            
            echo "<div class='success'>✅ تم إضافة تقييم للمسلسل: " . htmlspecialchars($serie['title']) . " ($rating/10)</div>";
        }
    }
    
    // Add Watchlist/Favorites
    echo "<h2>❤️ إضافة قائمة المفضلة:</h2>";
    
    // Create favorites table if not exists
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS favorites (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            content_type ENUM('movie', 'series') NOT NULL,
            content_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_favorite (user_id, content_type, content_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    if ($userId) {
        $favoriteStmt = $pdo->prepare("
            INSERT IGNORE INTO favorites (user_id, content_type, content_id) 
            VALUES (?, ?, ?)
        ");
        
        // Add some movies to favorites
        $favoriteMovies = array_slice($movies, 0, 3);
        foreach ($favoriteMovies as $movie) {
            $favoriteStmt->execute([$userId, 'movie', $movie['id']]);
            echo "<div class='success'>✅ تم إضافة الفيلم للمفضلة: " . htmlspecialchars($movie['title']) . "</div>";
        }
        
        // Add some series to favorites
        $favoriteSeries = array_slice($series, 0, 2);
        foreach ($favoriteSeries as $serie) {
            $favoriteStmt->execute([$userId, 'series', $serie['id']]);
            echo "<div class='success'>✅ تم إضافة المسلسل للمفضلة: " . htmlspecialchars($serie['title']) . "</div>";
        }
    }
    
    // Summary
    echo "<h2>📊 ملخص البيانات المتقدمة:</h2>";
    
    $stats = [
        'categories' => $pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn(),
        'episodes' => $pdo->query("SELECT COUNT(*) FROM episodes")->fetchColumn(),
        'ratings' => $pdo->query("SELECT COUNT(*) FROM ratings")->fetchColumn(),
        'favorites' => $pdo->query("SELECT COUNT(*) FROM favorites")->fetchColumn()
    ];
    
    echo "<div class='info'>";
    echo "<p><strong>التصنيفات:</strong> " . $stats['categories'] . "</p>";
    echo "<p><strong>الحلقات:</strong> " . $stats['episodes'] . "</p>";
    echo "<p><strong>التقييمات:</strong> " . $stats['ratings'] . "</p>";
    echo "<p><strong>المفضلة:</strong> " . $stats['favorites'] . "</p>";
    echo "</div>";
    
    // Test new API endpoints
    echo "<h2>🧪 اختبار API المحدث:</h2>";
    
    $apiUrls = [
        'api/?endpoint=status' => 'حالة النظام المحدثة',
        'api/?endpoint=movies' => 'قائمة الأفلام مع التقييمات',
        'api/?endpoint=series' => 'قائمة المسلسلات مع الحلقات',
        'api/?endpoint=search&q=دراما' => 'البحث في المحتوى'
    ];
    
    foreach ($apiUrls as $url => $description) {
        echo "<p><a href='$url' target='_blank' class='btn btn-primary'>🔗 $description</a></p>";
    }
    
} catch (PDOException $e) {
    echo "<div class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
}

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 40px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1 {
    color: #E50914;
    border-bottom: 3px solid #E50914;
    padding-bottom: 10px;
    margin-bottom: 30px;
}

h2 {
    color: #333;
    margin-top: 30px;
    margin-bottom: 15px;
    border-left: 4px solid #E50914;
    padding-left: 15px;
}

.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 8px;
    border-radius: 5px;
    margin: 3px 0;
    font-size: 0.9em;
}

.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 10px;
    border-radius: 5px;
    margin: 5px 0;
}

.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    background: #E50914;
    color: white;
    transition: all 0.3s;
}

.btn:hover {
    opacity: 0.8;
    text-decoration: none;
}

p {
    margin: 10px 0;
}
</style>
