import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive/hive.dart';

class StorageService {
  static late SharedPreferences _prefs;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  static late Box _hiveBox;
  
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _hiveBox = await Hive.openBox('shahid_cache');
  }
  
  // Shared Preferences Methods
  
  // String
  String? getString(String key) {
    return _prefs.getString(key);
  }
  
  Future<bool> setString(String key, String value) {
    return _prefs.setString(key, value);
  }
  
  // Int
  int? getInt(String key) {
    return _prefs.getInt(key);
  }
  
  Future<bool> setInt(String key, int value) {
    return _prefs.setInt(key, value);
  }
  
  // Double
  double? getDouble(String key) {
    return _prefs.getDouble(key);
  }
  
  Future<bool> setDouble(String key, double value) {
    return _prefs.setDouble(key, value);
  }
  
  // Bool
  bool? getBool(String key) {
    return _prefs.getBool(key);
  }
  
  Future<bool> setBool(String key, bool value) {
    return _prefs.setBool(key, value);
  }
  
  // List<String>
  List<String>? getStringList(String key) {
    return _prefs.getStringList(key);
  }
  
  Future<bool> setStringList(String key, List<String> value) {
    return _prefs.setStringList(key, value);
  }
  
  // Object (JSON)
  T? getObject<T>(String key, T Function(Map<String, dynamic>) fromJson) {
    final jsonString = _prefs.getString(key);
    if (jsonString == null) return null;
    
    try {
      final jsonMap = json.decode(jsonString) as Map<String, dynamic>;
      return fromJson(jsonMap);
    } catch (e) {
      return null;
    }
  }
  
  Future<bool> setObject<T>(String key, T object) {
    try {
      final jsonString = json.encode(object);
      return _prefs.setString(key, jsonString);
    } catch (e) {
      return Future.value(false);
    }
  }
  
  // Remove
  Future<bool> remove(String key) {
    return _prefs.remove(key);
  }
  
  // Clear all
  Future<bool> clear() {
    return _prefs.clear();
  }
  
  // Check if key exists
  bool containsKey(String key) {
    return _prefs.containsKey(key);
  }
  
  // Get all keys
  Set<String> getKeys() {
    return _prefs.getKeys();
  }
  
  // Secure Storage Methods
  
  // Secure String
  Future<String?> getSecureString(String key) async {
    try {
      return await _secureStorage.read(key: key);
    } catch (e) {
      return null;
    }
  }
  
  Future<void> setSecureString(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
    } catch (e) {
      // Handle error
    }
  }
  
  // Secure Object
  Future<T?> getSecureObject<T>(String key, T Function(Map<String, dynamic>) fromJson) async {
    try {
      final jsonString = await _secureStorage.read(key: key);
      if (jsonString == null) return null;
      
      final jsonMap = json.decode(jsonString) as Map<String, dynamic>;
      return fromJson(jsonMap);
    } catch (e) {
      return null;
    }
  }
  
  Future<void> setSecureObject<T>(String key, T object) async {
    try {
      final jsonString = json.encode(object);
      await _secureStorage.write(key: key, value: jsonString);
    } catch (e) {
      // Handle error
    }
  }
  
  // Remove secure
  Future<void> removeSecure(String key) async {
    try {
      await _secureStorage.delete(key: key);
    } catch (e) {
      // Handle error
    }
  }
  
  // Clear all secure
  Future<void> clearSecure() async {
    try {
      await _secureStorage.deleteAll();
    } catch (e) {
      // Handle error
    }
  }
  
  // Check if secure key exists
  Future<bool> containsSecureKey(String key) async {
    try {
      return await _secureStorage.containsKey(key: key);
    } catch (e) {
      return false;
    }
  }
  
  // Hive Cache Methods
  
  // Cache String
  T? getCached<T>(String key) {
    try {
      return _hiveBox.get(key);
    } catch (e) {
      return null;
    }
  }
  
  Future<void> setCached<T>(String key, T value, {Duration? expiry}) async {
    try {
      final data = {
        'value': value,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'expiry': expiry?.inMilliseconds,
      };
      await _hiveBox.put(key, data);
    } catch (e) {
      // Handle error
    }
  }
  
  // Get cached with expiry check
  T? getCachedWithExpiry<T>(String key) {
    try {
      final data = _hiveBox.get(key);
      if (data == null) return null;
      
      final timestamp = data['timestamp'] as int?;
      final expiry = data['expiry'] as int?;
      
      if (expiry != null && timestamp != null) {
        final now = DateTime.now().millisecondsSinceEpoch;
        if (now - timestamp > expiry) {
          // Expired, remove and return null
          _hiveBox.delete(key);
          return null;
        }
      }
      
      return data['value'] as T?;
    } catch (e) {
      return null;
    }
  }
  
  // Remove cached
  Future<void> removeCached(String key) async {
    try {
      await _hiveBox.delete(key);
    } catch (e) {
      // Handle error
    }
  }
  
  // Clear cache
  Future<void> clearCache() async {
    try {
      await _hiveBox.clear();
    } catch (e) {
      // Handle error
    }
  }
  
  // Check if cached key exists
  bool containsCachedKey(String key) {
    try {
      return _hiveBox.containsKey(key);
    } catch (e) {
      return false;
    }
  }
  
  // Get cache size
  int getCacheSize() {
    try {
      return _hiveBox.length;
    } catch (e) {
      return 0;
    }
  }
  
  // Utility Methods
  
  // Get app version
  String? getAppVersion() {
    return getString('app_version');
  }
  
  Future<bool> setAppVersion(String version) {
    return setString('app_version', version);
  }
  
  // Get first time flag
  bool isFirstTime() {
    return getBool('first_time') ?? true;
  }
  
  Future<bool> setFirstTime(bool value) {
    return setBool('first_time', value);
  }
  
  // Theme mode
  String getThemeMode() {
    return getString('theme_mode') ?? 'system';
  }
  
  Future<bool> setThemeMode(String mode) {
    return setString('theme_mode', mode);
  }
  
  // Language
  String getLanguage() {
    return getString('language') ?? 'ar';
  }
  
  Future<bool> setLanguage(String language) {
    return setString('language', language);
  }
  
  // User preferences
  Map<String, dynamic> getUserPreferences() {
    final prefsString = getString('user_preferences');
    if (prefsString == null) return {};
    
    try {
      return json.decode(prefsString) as Map<String, dynamic>;
    } catch (e) {
      return {};
    }
  }
  
  Future<bool> setUserPreferences(Map<String, dynamic> preferences) {
    try {
      final prefsString = json.encode(preferences);
      return setString('user_preferences', prefsString);
    } catch (e) {
      return Future.value(false);
    }
  }
  
  // Download settings
  String getDownloadQuality() {
    return getString('download_quality') ?? '720p';
  }
  
  Future<bool> setDownloadQuality(String quality) {
    return setString('download_quality', quality);
  }
  
  String getStreamingQuality() {
    return getString('streaming_quality') ?? 'auto';
  }
  
  Future<bool> setStreamingQuality(String quality) {
    return setString('streaming_quality', quality);
  }
  
  // Auto-play setting
  bool getAutoPlay() {
    return getBool('auto_play') ?? true;
  }
  
  Future<bool> setAutoPlay(bool value) {
    return setBool('auto_play', value);
  }
  
  // Subtitles setting
  bool getSubtitlesEnabled() {
    return getBool('subtitles_enabled') ?? true;
  }
  
  Future<bool> setSubtitlesEnabled(bool value) {
    return setBool('subtitles_enabled', value);
  }
  
  // Notifications setting
  bool getNotificationsEnabled() {
    return getBool('notifications_enabled') ?? true;
  }
  
  Future<bool> setNotificationsEnabled(bool value) {
    return setBool('notifications_enabled', value);
  }
  
  // Biometric authentication
  bool getBiometricEnabled() {
    return getBool('biometric_enabled') ?? false;
  }
  
  Future<bool> setBiometricEnabled(bool value) {
    return setBool('biometric_enabled', value);
  }
}
