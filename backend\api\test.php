<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔗 API Test Dashboard - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #E50914 0%, #B8070F 100%);
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(229, 9, 20, 0.3);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .test-section {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .test-section h2 {
            color: #E50914;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
        }
        
        .endpoint-test {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #E50914;
        }
        
        .endpoint-test:last-child {
            margin-bottom: 0;
        }
        
        .endpoint-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .method {
            background: #4CAF50;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .method.post {
            background: #2196F3;
        }
        
        .test-btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }
        
        .status {
            padding: 0.3rem 0.8rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status.success {
            background: #4CAF50;
            color: white;
        }
        
        .status.error {
            background: #F44336;
            color: white;
        }
        
        .status.pending {
            background: #FF9800;
            color: white;
        }
        
        .response {
            background: #1a1a1a;
            border-radius: 5px;
            padding: 1rem;
            margin-top: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        }
        
        .quick-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .quick-test {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .quick-test h3 {
            color: #E50914;
            margin-bottom: 1rem;
        }
        
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .test-result.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        
        .test-result.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
            color: #F44336;
        }
        
        .documentation {
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .documentation h2 {
            color: #E50914;
            margin-bottom: 1.5rem;
        }
        
        .endpoint-doc {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            font-family: 'Courier New', monospace;
        }
        
        .links-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .link-btn {
            background: linear-gradient(45deg, #555, #333);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .link-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 API Test Dashboard</h1>
            <p>اختبار شامل لجميع endpoints الـ API</p>
        </div>

        <div class="quick-tests">
            <div class="quick-test">
                <h3>1. API Status Test:</h3>
                <button class="test-btn" onclick="testQuick('status')">Test Status</button>
                <div id="status-result" class="test-result" style="display: none;"></div>
            </div>
            
            <div class="quick-test">
                <h3>2. Movies Endpoint Test:</h3>
                <button class="test-btn" onclick="testQuick('movies')">Test Movies</button>
                <div id="movies-result" class="test-result" style="display: none;"></div>
            </div>
            
            <div class="quick-test">
                <h3>3. Search Endpoint Test:</h3>
                <button class="test-btn" onclick="testQuick('search')">Test Search</button>
                <div id="search-result" class="test-result" style="display: none;"></div>
            </div>
        </div>

        <div class="test-section">
            <h2>📡 API Status:</h2>
            
            <div class="endpoint-test">
                <div class="endpoint-header">
                    <div>
                        <span class="method">GET</span>
                        <strong>/api/</strong> - API Status
                    </div>
                    <div>
                        <button class="test-btn" onclick="testEndpoint('', 'api-root')">Test</button>
                        <span id="status-api-root" class="status pending">Pending</span>
                    </div>
                </div>
                <div id="response-api-root" class="response"></div>
            </div>

            <div class="endpoint-test">
                <div class="endpoint-header">
                    <div>
                        <span class="method">GET</span>
                        <strong>/api/status</strong> - Detailed Status
                    </div>
                    <div>
                        <button class="test-btn" onclick="testEndpoint('status', 'status')">Test</button>
                        <span id="status-status" class="status pending">Pending</span>
                    </div>
                </div>
                <div id="response-status" class="response"></div>
            </div>

            <div class="endpoint-test">
                <div class="endpoint-header">
                    <div>
                        <span class="method">GET</span>
                        <strong>/api/movies</strong> - List Movies
                    </div>
                    <div>
                        <button class="test-btn" onclick="testEndpoint('movies', 'movies')">Test</button>
                        <span id="status-movies" class="status pending">Pending</span>
                    </div>
                </div>
                <div id="response-movies" class="response"></div>
            </div>

            <div class="endpoint-test">
                <div class="endpoint-header">
                    <div>
                        <span class="method">GET</span>
                        <strong>/api/series</strong> - List Series
                    </div>
                    <div>
                        <button class="test-btn" onclick="testEndpoint('series', 'series')">Test</button>
                        <span id="status-series" class="status pending">Pending</span>
                    </div>
                </div>
                <div id="response-series" class="response"></div>
            </div>

            <div class="endpoint-test">
                <div class="endpoint-header">
                    <div>
                        <span class="method">GET</span>
                        <strong>/api/search?q=test</strong> - Search Content
                    </div>
                    <div>
                        <button class="test-btn" onclick="testEndpoint('search?q=test', 'search')">Test</button>
                        <span id="status-search" class="status pending">Pending</span>
                    </div>
                </div>
                <div id="response-search" class="response"></div>
            </div>

            <div class="endpoint-test">
                <div class="endpoint-header">
                    <div>
                        <span class="method post">POST</span>
                        <strong>/api/auth/login</strong> - User Login
                    </div>
                    <div>
                        <button class="test-btn" onclick="testLogin()">Test</button>
                        <span id="status-login" class="status pending">Pending</span>
                    </div>
                </div>
                <div id="response-login" class="response"></div>
            </div>

            <div class="endpoint-test">
                <div class="endpoint-header">
                    <div>
                        <span class="method post">POST</span>
                        <strong>/api/auth/register</strong> - User Registration
                    </div>
                    <div>
                        <button class="test-btn" onclick="testRegister()">Test</button>
                        <span id="status-register" class="status pending">Pending</span>
                    </div>
                </div>
                <div id="response-register" class="response"></div>
            </div>
        </div>

        <div class="documentation">
            <h2>📋 API Documentation:</h2>
            <p><strong>Available Endpoints:</strong></p>
            
            <div class="endpoint-doc">
GET  /api/status           - API status and database info<br>
GET  /api/movies           - List all movies (paginated)<br>
GET  /api/movies/{id}      - Get specific movie details<br>
GET  /api/series           - List all series (paginated)<br>
GET  /api/series/{id}      - Get specific series with episodes<br>
GET  /api/search?q={query} - Search movies and series<br>
POST /api/auth/login       - User login (email, password)<br>
POST /api/auth/register    - User registration (name, email, password)<br>
GET  /api/user/profile     - User profile (requires auth)
            </div>

            <p><strong>Example Usage:</strong></p>
            <div class="endpoint-doc">
// Get movies<br>
curl -X GET 'http://127.0.0.1/amr2/flutter_module_1/backend/api/movies?page=1&limit=10'<br><br>

// Search content<br>
curl -X GET 'http://127.0.0.1/amr2/flutter_module_1/backend/api/search?q=matrix'<br><br>

// Login user<br>
curl -X POST 'http://127.0.0.1/amr2/flutter_module_1/backend/api/auth/login' \<br>
  -H 'Content-Type: application/json' \<br>
  -d '{"email":"<EMAIL>","password":"password123"}'<br><br>

// Register user<br>
curl -X POST 'http://127.0.0.1/amr2/flutter_module_1/backend/api/auth/register' \<br>
  -H 'Content-Type: application/json' \<br>
  -d '{"name":"John Doe","email":"<EMAIL>","password":"password123"}'
            </div>
        </div>

        <div class="links-section">
            <a href="fixed_api.php?endpoint=status" class="link-btn">📊 API Status</a>
            <a href="fixed_api.php?endpoint=movies" class="link-btn">🎬 Movies</a>
            <a href="fixed_api.php?endpoint=series" class="link-btn">📺 Series</a>
            <a href="fixed_api.php?endpoint=search&q=test" class="link-btn">🔍 Search</a>
            <a href="../homepage.php" class="link-btn">🏠 Homepage</a>
            <a href="../test_advanced_features.php" class="link-btn">🧪 Test Homepage</a>
        </div>
    </div>

    <script>
        const baseUrl = 'fixed_api.php';

        function testEndpoint(endpoint, id) {
            const statusEl = document.getElementById(`status-${id}`);
            const responseEl = document.getElementById(`response-${id}`);
            
            statusEl.textContent = 'Testing...';
            statusEl.className = 'status pending';
            responseEl.style.display = 'none';
            
            const url = endpoint ? `${baseUrl}?endpoint=${endpoint}` : baseUrl;
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    statusEl.textContent = 'Success ✅';
                    statusEl.className = 'status success';
                    responseEl.textContent = JSON.stringify(data, null, 2);
                    responseEl.style.display = 'block';
                })
                .catch(error => {
                    statusEl.textContent = 'Failed ❌';
                    statusEl.className = 'status error';
                    responseEl.textContent = 'Error: ' + error.message;
                    responseEl.style.display = 'block';
                });
        }

        function testLogin() {
            const statusEl = document.getElementById('status-login');
            const responseEl = document.getElementById('response-login');
            
            statusEl.textContent = 'Testing...';
            statusEl.className = 'status pending';
            responseEl.style.display = 'none';
            
            fetch(`${baseUrl}?endpoint=login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: '<EMAIL>',
                    password: 'password123'
                })
            })
            .then(response => response.json())
            .then(data => {
                statusEl.textContent = 'Success ✅';
                statusEl.className = 'status success';
                responseEl.textContent = JSON.stringify(data, null, 2);
                responseEl.style.display = 'block';
            })
            .catch(error => {
                statusEl.textContent = 'Failed ❌';
                statusEl.className = 'status error';
                responseEl.textContent = 'Error: ' + error.message;
                responseEl.style.display = 'block';
            });
        }

        function testRegister() {
            const statusEl = document.getElementById('status-register');
            const responseEl = document.getElementById('response-register');
            
            statusEl.textContent = 'Testing...';
            statusEl.className = 'status pending';
            responseEl.style.display = 'none';
            
            fetch(`${baseUrl}?endpoint=register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: 'Test User',
                    email: '<EMAIL>',
                    password: 'password123'
                })
            })
            .then(response => response.json())
            .then(data => {
                statusEl.textContent = 'Success ✅';
                statusEl.className = 'status success';
                responseEl.textContent = JSON.stringify(data, null, 2);
                responseEl.style.display = 'block';
            })
            .catch(error => {
                statusEl.textContent = 'Failed ❌';
                statusEl.className = 'status error';
                responseEl.textContent = 'Error: ' + error.message;
                responseEl.style.display = 'block';
            });
        }

        function testQuick(endpoint) {
            const resultEl = document.getElementById(`${endpoint}-result`);
            resultEl.style.display = 'block';
            resultEl.textContent = 'Testing...';
            resultEl.className = 'test-result';
            
            const url = `${baseUrl}?endpoint=${endpoint}${endpoint === 'search' ? '&q=test' : ''}`;
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultEl.textContent = `✅ ${endpoint.charAt(0).toUpperCase() + endpoint.slice(1)} Endpoint: Working`;
                        resultEl.className = 'test-result success';
                    } else {
                        resultEl.textContent = `❌ ${endpoint.charAt(0).toUpperCase() + endpoint.slice(1)} Endpoint: Error`;
                        resultEl.className = 'test-result error';
                    }
                })
                .catch(error => {
                    resultEl.textContent = `❌ ${endpoint.charAt(0).toUpperCase() + endpoint.slice(1)} Endpoint: No Response`;
                    resultEl.className = 'test-result error';
                });
        }

        // Auto-test on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔗 API Test Dashboard loaded');
            
            // Test all quick tests automatically
            setTimeout(() => {
                testQuick('status');
            }, 500);
            
            setTimeout(() => {
                testQuick('movies');
            }, 1000);
            
            setTimeout(() => {
                testQuick('search');
            }, 1500);
        });
    </script>
</body>
</html>
