<?php
/**
 * Shahid - Payment Model
 * Professional Video Streaming Platform
 */

class Payment extends Model {
    protected $table = 'payments';
    
    public function createPayment($data) {
        $requiredFields = ['user_id', 'subscription_id', 'amount', 'currency', 'payment_method'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Missing required field: {$field}");
            }
        }
        
        $paymentData = [
            'user_id' => intval($data['user_id']),
            'subscription_id' => intval($data['subscription_id']),
            'amount' => floatval($data['amount']),
            'currency' => $data['currency'],
            'payment_method' => $data['payment_method'],
            'status' => $data['status'] ?? 'pending',
            'type' => $data['type'] ?? 'subscription',
            'external_id' => $data['external_id'] ?? null,
            'metadata' => isset($data['metadata']) ? json_encode($data['metadata']) : null,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->create($paymentData);
    }
    
    public function updatePaymentStatus($id, $status, $metadata = []) {
        $updateData = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($status === 'completed') {
            $updateData['completed_at'] = date('Y-m-d H:i:s');
        } elseif ($status === 'failed') {
            $updateData['failed_at'] = date('Y-m-d H:i:s');
        }
        
        if (!empty($metadata)) {
            $updateData['metadata'] = json_encode($metadata);
        }
        
        return $this->update($id, $updateData);
    }
    
    public function getPaymentsByUser($userId, $limit = 20, $offset = 0) {
        $sql = "SELECT p.*, s.name as subscription_name, s.duration_days
                FROM {$this->table} p
                LEFT JOIN subscriptions s ON p.subscription_id = s.id
                WHERE p.user_id = :user_id
                ORDER BY p.created_at DESC
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function getPaymentsByStatus($status, $limit = 50, $offset = 0) {
        $sql = "SELECT p.*, u.name as user_name, u.email as user_email, 
                       s.name as subscription_name
                FROM {$this->table} p
                LEFT JOIN users u ON p.user_id = u.id
                LEFT JOIN subscriptions s ON p.subscription_id = s.id
                WHERE p.status = :status
                ORDER BY p.created_at DESC
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function getPendingPayments($olderThan = 24) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'pending' 
                AND created_at < DATE_SUB(NOW(), INTERVAL :hours HOUR)
                ORDER BY created_at ASC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':hours', $olderThan, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function getPaymentStats($period = '30 days') {
        $stats = [];
        
        // Total revenue
        $sql = "SELECT 
                    SUM(amount) as total_revenue,
                    COUNT(*) as total_payments,
                    AVG(amount) as avg_payment
                FROM {$this->table} 
                WHERE status = 'completed' 
                AND created_at >= DATE_SUB(NOW(), INTERVAL {$period})";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['overview'] = $stmt->fetch();
        
        // Revenue by payment method
        $sql = "SELECT 
                    payment_method,
                    SUM(amount) as revenue,
                    COUNT(*) as count
                FROM {$this->table} 
                WHERE status = 'completed' 
                AND created_at >= DATE_SUB(NOW(), INTERVAL {$period})
                GROUP BY payment_method
                ORDER BY revenue DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['by_method'] = $stmt->fetchAll();
        
        // Revenue by status
        $sql = "SELECT 
                    status,
                    COUNT(*) as count,
                    SUM(amount) as total_amount
                FROM {$this->table} 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL {$period})
                GROUP BY status";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['by_status'] = $stmt->fetchAll();
        
        // Daily revenue (last 30 days)
        $sql = "SELECT 
                    DATE(created_at) as date,
                    SUM(amount) as revenue,
                    COUNT(*) as payments
                FROM {$this->table} 
                WHERE status = 'completed' 
                AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY DATE(created_at)
                ORDER BY date ASC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $stats['daily_revenue'] = $stmt->fetchAll();
        
        return $stats;
    }
    
    public function refundPayment($id, $reason = '', $amount = null) {
        $payment = $this->findById($id);
        
        if (!$payment) {
            throw new Exception('Payment not found');
        }
        
        if ($payment['status'] !== 'completed') {
            throw new Exception('Can only refund completed payments');
        }
        
        $refundAmount = $amount ?? $payment['amount'];
        
        if ($refundAmount > $payment['amount']) {
            throw new Exception('Refund amount cannot exceed original payment amount');
        }
        
        // Create refund record
        $refundData = [
            'user_id' => $payment['user_id'],
            'subscription_id' => $payment['subscription_id'],
            'amount' => -$refundAmount, // Negative amount for refund
            'currency' => $payment['currency'],
            'payment_method' => $payment['payment_method'],
            'status' => 'completed',
            'type' => 'refund',
            'parent_payment_id' => $id,
            'metadata' => json_encode(['reason' => $reason]),
            'created_at' => date('Y-m-d H:i:s'),
            'completed_at' => date('Y-m-d H:i:s')
        ];
        
        $refundId = $this->create($refundData);
        
        // Update original payment status if fully refunded
        if ($refundAmount == $payment['amount']) {
            $this->update($id, [
                'status' => 'refunded',
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
        
        return $refundId;
    }
    
    public function getRefunds($paymentId = null) {
        $sql = "SELECT p.*, original.id as original_payment_id, 
                       original.amount as original_amount
                FROM {$this->table} p
                LEFT JOIN {$this->table} original ON p.parent_payment_id = original.id
                WHERE p.type = 'refund'";
        
        $params = [];
        
        if ($paymentId) {
            $sql .= " AND p.parent_payment_id = :payment_id";
            $params[':payment_id'] = $paymentId;
        }
        
        $sql .= " ORDER BY p.created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function searchPayments($filters = [], $limit = 50, $offset = 0) {
        $sql = "SELECT p.*, u.name as user_name, u.email as user_email,
                       s.name as subscription_name
                FROM {$this->table} p
                LEFT JOIN users u ON p.user_id = u.id
                LEFT JOIN subscriptions s ON p.subscription_id = s.id
                WHERE 1=1";
        
        $params = [];
        
        if (!empty($filters['user_id'])) {
            $sql .= " AND p.user_id = :user_id";
            $params[':user_id'] = $filters['user_id'];
        }
        
        if (!empty($filters['status'])) {
            $sql .= " AND p.status = :status";
            $params[':status'] = $filters['status'];
        }
        
        if (!empty($filters['payment_method'])) {
            $sql .= " AND p.payment_method = :payment_method";
            $params[':payment_method'] = $filters['payment_method'];
        }
        
        if (!empty($filters['min_amount'])) {
            $sql .= " AND p.amount >= :min_amount";
            $params[':min_amount'] = $filters['min_amount'];
        }
        
        if (!empty($filters['max_amount'])) {
            $sql .= " AND p.amount <= :max_amount";
            $params[':max_amount'] = $filters['max_amount'];
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND p.created_at >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND p.created_at <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }
        
        if (!empty($filters['external_id'])) {
            $sql .= " AND p.external_id = :external_id";
            $params[':external_id'] = $filters['external_id'];
        }
        
        $sql .= " ORDER BY p.created_at DESC LIMIT :limit OFFSET :offset";
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;
        
        $stmt = $this->db->prepare($sql);
        foreach ($params as $key => $value) {
            if ($key === ':limit' || $key === ':offset') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function validatePaymentData($data) {
        $errors = [];
        
        if (empty($data['user_id']) || !is_numeric($data['user_id'])) {
            $errors['user_id'] = 'Valid user ID is required';
        }
        
        if (empty($data['subscription_id']) || !is_numeric($data['subscription_id'])) {
            $errors['subscription_id'] = 'Valid subscription ID is required';
        }
        
        if (empty($data['amount']) || !is_numeric($data['amount']) || $data['amount'] <= 0) {
            $errors['amount'] = 'Amount must be a positive number';
        }
        
        $validCurrencies = ['USD', 'EUR', 'SAR', 'AED', 'EGP'];
        if (empty($data['currency']) || !in_array($data['currency'], $validCurrencies)) {
            $errors['currency'] = 'Invalid currency code';
        }
        
        $validMethods = ['stripe', 'paypal', 'bank_transfer', 'credit_card'];
        if (empty($data['payment_method']) || !in_array($data['payment_method'], $validMethods)) {
            $errors['payment_method'] = 'Invalid payment method';
        }
        
        $validStatuses = ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'];
        if (isset($data['status']) && !in_array($data['status'], $validStatuses)) {
            $errors['status'] = 'Invalid payment status';
        }
        
        return $errors;
    }
    
    public function getFailedPaymentReasons() {
        $sql = "SELECT 
                    JSON_EXTRACT(metadata, '$.error_code') as error_code,
                    JSON_EXTRACT(metadata, '$.error_message') as error_message,
                    COUNT(*) as count
                FROM {$this->table} 
                WHERE status = 'failed' 
                AND metadata IS NOT NULL
                AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY error_code, error_message
                ORDER BY count DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    public function cleanupExpiredPayments() {
        // Mark payments as expired if pending for more than 24 hours
        $sql = "UPDATE {$this->table} 
                SET status = 'expired', updated_at = NOW()
                WHERE status = 'pending' 
                AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        
        return $stmt->rowCount();
    }
}
?>
