<?php
/**
 * Shahid API Error Handler
 * Professional Video Streaming Platform
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

$errorCode = intval($_GET['code'] ?? 500);

$errorMessages = [
    400 => [
        'error' => 'Bad Request',
        'message' => 'The request could not be understood by the server due to malformed syntax.',
        'details' => 'Please check your request parameters and try again.'
    ],
    401 => [
        'error' => 'Unauthorized',
        'message' => 'Authentication is required to access this resource.',
        'details' => 'Please provide a valid authorization token.'
    ],
    403 => [
        'error' => 'Forbidden',
        'message' => 'You do not have permission to access this resource.',
        'details' => 'This action requires higher privileges or an active subscription.'
    ],
    404 => [
        'error' => 'Not Found',
        'message' => 'The requested resource could not be found.',
        'details' => 'Please check the URL and try again.'
    ],
    405 => [
        'error' => 'Method Not Allowed',
        'message' => 'The request method is not supported for this resource.',
        'details' => 'Please check the allowed HTTP methods for this endpoint.'
    ],
    429 => [
        'error' => 'Too Many Requests',
        'message' => 'Rate limit exceeded.',
        'details' => 'Please wait before making additional requests.'
    ],
    500 => [
        'error' => 'Internal Server Error',
        'message' => 'An unexpected error occurred on the server.',
        'details' => 'Please try again later or contact support if the problem persists.'
    ],
    503 => [
        'error' => 'Service Unavailable',
        'message' => 'The service is temporarily unavailable.',
        'details' => 'Please try again later.'
    ]
];

$response = [
    'success' => false,
    'error_code' => $errorCode,
    'timestamp' => date('c')
];

if (isset($errorMessages[$errorCode])) {
    $response = array_merge($response, $errorMessages[$errorCode]);
} else {
    $response['error'] = 'Unknown Error';
    $response['message'] = 'An unknown error occurred.';
    $response['details'] = 'Please contact support for assistance.';
}

// Log the error
error_log("API Error {$errorCode}: " . json_encode([
    'url' => $_SERVER['REQUEST_URI'] ?? 'unknown',
    'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'timestamp' => date('Y-m-d H:i:s')
]));

http_response_code($errorCode);
echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
