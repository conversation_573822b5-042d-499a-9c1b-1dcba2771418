<?php
/**
 * نظام رفع الملفات المتقدم
 * يدعم رفع الصور والفيديوهات مع التحقق من الأمان
 */

require_once 'config/database.php';

class FileUploadHandler {
    private $db;
    private $allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    private $allowedVideoTypes = ['mp4', 'avi', 'mkv', 'mov', 'wmv'];
    private $maxImageSize = 5 * 1024 * 1024; // 5MB
    private $maxVideoSize = 500 * 1024 * 1024; // 500MB
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    /**
     * رفع صورة (بوستر، صورة مصغرة، إلخ)
     */
    public function uploadImage($file, $type = 'poster') {
        try {
            // التحقق من وجود الملف
            if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('لم يتم رفع الملف بشكل صحيح');
            }
            
            // التحقق من نوع الملف
            $fileInfo = pathinfo($file['name']);
            $extension = strtolower($fileInfo['extension']);
            
            if (!in_array($extension, $this->allowedImageTypes)) {
                throw new Exception('نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', $this->allowedImageTypes));
            }
            
            // التحقق من حجم الملف
            if ($file['size'] > $this->maxImageSize) {
                throw new Exception('حجم الملف كبير جداً. الحد الأقصى: 5MB');
            }
            
            // التحقق من أن الملف صورة حقيقية
            $imageInfo = getimagesize($file['tmp_name']);
            if ($imageInfo === false) {
                throw new Exception('الملف ليس صورة صالحة');
            }
            
            // إنشاء اسم ملف فريد
            $fileName = $this->generateUniqueFileName($extension, $type);
            $uploadPath = $this->getUploadPath($type);
            $fullPath = $uploadPath . '/' . $fileName;
            
            // إنشاء المجلد إذا لم يكن موجوداً
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            
            // نقل الملف
            if (!move_uploaded_file($file['tmp_name'], $fullPath)) {
                throw new Exception('فشل في حفظ الملف');
            }
            
            // تحسين الصورة
            $this->optimizeImage($fullPath, $imageInfo[2]);
            
            // حفظ معلومات الملف في قاعدة البيانات
            $fileId = $this->saveFileInfo($fileName, $type, $file['size'], $extension);
            
            return [
                'success' => true,
                'file_id' => $fileId,
                'file_name' => $fileName,
                'file_url' => $this->getFileUrl($fileName, $type),
                'message' => 'تم رفع الصورة بنجاح'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * رفع فيديو
     */
    public function uploadVideo($file, $type = 'movie') {
        try {
            // التحقق من وجود الملف
            if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('لم يتم رفع الملف بشكل صحيح');
            }
            
            // التحقق من نوع الملف
            $fileInfo = pathinfo($file['name']);
            $extension = strtolower($fileInfo['extension']);
            
            if (!in_array($extension, $this->allowedVideoTypes)) {
                throw new Exception('نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', $this->allowedVideoTypes));
            }
            
            // التحقق من حجم الملف
            if ($file['size'] > $this->maxVideoSize) {
                throw new Exception('حجم الملف كبير جداً. الحد الأقصى: 500MB');
            }
            
            // إنشاء اسم ملف فريد
            $fileName = $this->generateUniqueFileName($extension, $type);
            $uploadPath = $this->getUploadPath('videos');
            $fullPath = $uploadPath . '/' . $fileName;
            
            // إنشاء المجلد إذا لم يكن موجوداً
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            
            // نقل الملف
            if (!move_uploaded_file($file['tmp_name'], $fullPath)) {
                throw new Exception('فشل في حفظ الملف');
            }
            
            // حفظ معلومات الملف في قاعدة البيانات
            $fileId = $this->saveFileInfo($fileName, 'video', $file['size'], $extension);
            
            return [
                'success' => true,
                'file_id' => $fileId,
                'file_name' => $fileName,
                'file_url' => $this->getFileUrl($fileName, 'videos'),
                'message' => 'تم رفع الفيديو بنجاح'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إنشاء اسم ملف فريد
     */
    private function generateUniqueFileName($extension, $type) {
        $timestamp = time();
        $random = bin2hex(random_bytes(8));
        return $type . '_' . $timestamp . '_' . $random . '.' . $extension;
    }
    
    /**
     * الحصول على مسار الرفع
     */
    private function getUploadPath($type) {
        $basePath = __DIR__ . '/uploads';
        
        switch ($type) {
            case 'poster':
                return $basePath . '/posters';
            case 'thumbnail':
                return $basePath . '/thumbnails';
            case 'avatar':
                return $basePath . '/avatars';
            case 'videos':
                return $basePath . '/videos';
            default:
                return $basePath . '/misc';
        }
    }
    
    /**
     * الحصول على رابط الملف
     */
    private function getFileUrl($fileName, $type) {
        $baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . '/uploads';
        
        switch ($type) {
            case 'poster':
                return $baseUrl . '/posters/' . $fileName;
            case 'thumbnail':
                return $baseUrl . '/thumbnails/' . $fileName;
            case 'avatar':
                return $baseUrl . '/avatars/' . $fileName;
            case 'videos':
                return $baseUrl . '/videos/' . $fileName;
            default:
                return $baseUrl . '/misc/' . $fileName;
        }
    }
    
    /**
     * تحسين الصورة
     */
    private function optimizeImage($filePath, $imageType) {
        $maxWidth = 1920;
        $maxHeight = 1080;
        $quality = 85;
        
        // الحصول على أبعاد الصورة الحالية
        list($width, $height) = getimagesize($filePath);
        
        // حساب الأبعاد الجديدة
        if ($width > $maxWidth || $height > $maxHeight) {
            $ratio = min($maxWidth / $width, $maxHeight / $height);
            $newWidth = intval($width * $ratio);
            $newHeight = intval($height * $ratio);
            
            // إنشاء صورة جديدة
            $newImage = imagecreatetruecolor($newWidth, $newHeight);
            
            // تحميل الصورة الأصلية
            switch ($imageType) {
                case IMAGETYPE_JPEG:
                    $source = imagecreatefromjpeg($filePath);
                    break;
                case IMAGETYPE_PNG:
                    $source = imagecreatefrompng($filePath);
                    imagealphablending($newImage, false);
                    imagesavealpha($newImage, true);
                    break;
                case IMAGETYPE_GIF:
                    $source = imagecreatefromgif($filePath);
                    break;
                default:
                    return;
            }
            
            // تغيير حجم الصورة
            imagecopyresampled($newImage, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
            
            // حفظ الصورة المحسنة
            switch ($imageType) {
                case IMAGETYPE_JPEG:
                    imagejpeg($newImage, $filePath, $quality);
                    break;
                case IMAGETYPE_PNG:
                    imagepng($newImage, $filePath, 9);
                    break;
                case IMAGETYPE_GIF:
                    imagegif($newImage, $filePath);
                    break;
            }
            
            // تنظيف الذاكرة
            imagedestroy($source);
            imagedestroy($newImage);
        }
    }
    
    /**
     * حفظ معلومات الملف في قاعدة البيانات
     */
    private function saveFileInfo($fileName, $type, $size, $extension) {
        $stmt = $this->db->prepare("
            INSERT INTO uploaded_files (file_name, file_type, file_size, file_extension, upload_date)
            VALUES (?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([$fileName, $type, $size, $extension]);
        return $this->db->lastInsertId();
    }
    
    /**
     * حذف ملف
     */
    public function deleteFile($fileId) {
        try {
            // الحصول على معلومات الملف
            $stmt = $this->db->prepare("SELECT * FROM uploaded_files WHERE id = ?");
            $stmt->execute([$fileId]);
            $file = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$file) {
                throw new Exception('الملف غير موجود');
            }
            
            // حذف الملف من النظام
            $filePath = $this->getUploadPath($file['file_type']) . '/' . $file['file_name'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            
            // حذف السجل من قاعدة البيانات
            $stmt = $this->db->prepare("DELETE FROM uploaded_files WHERE id = ?");
            $stmt->execute([$fileId]);
            
            return [
                'success' => true,
                'message' => 'تم حذف الملف بنجاح'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * الحصول على قائمة الملفات
     */
    public function getFiles($type = null, $limit = 50) {
        $sql = "SELECT * FROM uploaded_files";
        $params = [];
        
        if ($type) {
            $sql .= " WHERE file_type = ?";
            $params[] = $type;
        }
        
        $sql .= " ORDER BY upload_date DESC LIMIT ?";
        $params[] = $limit;
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}

// معالجة طلبات الرفع
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $uploader = new FileUploadHandler();
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'upload_image':
            if (isset($_FILES['image'])) {
                $type = $_POST['type'] ?? 'poster';
                echo json_encode($uploader->uploadImage($_FILES['image'], $type));
            } else {
                echo json_encode(['success' => false, 'error' => 'لم يتم اختيار ملف']);
            }
            break;
            
        case 'upload_video':
            if (isset($_FILES['video'])) {
                $type = $_POST['type'] ?? 'movie';
                echo json_encode($uploader->uploadVideo($_FILES['video'], $type));
            } else {
                echo json_encode(['success' => false, 'error' => 'لم يتم اختيار ملف']);
            }
            break;
            
        case 'delete_file':
            $fileId = $_POST['file_id'] ?? 0;
            echo json_encode($uploader->deleteFile($fileId));
            break;
            
        case 'get_files':
            $type = $_POST['type'] ?? null;
            $limit = $_POST['limit'] ?? 50;
            echo json_encode([
                'success' => true,
                'files' => $uploader->getFiles($type, $limit)
            ]);
            break;
            
        default:
            echo json_encode(['success' => false, 'error' => 'إجراء غير صالح']);
    }
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع الملفات - Shahid</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #141414, #2F2F2F);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        
        h1 {
            text-align: center;
            color: #E50914;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .upload-section {
            background: rgba(20, 20, 20, 0.8);
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px dashed #E50914;
        }
        
        .upload-section h3 {
            color: #E50914;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #fff;
        }
        
        input[type="file"], select {
            width: 100%;
            padding: 12px;
            border: 2px solid #555;
            border-radius: 8px;
            background: #333;
            color: white;
            font-size: 16px;
        }
        
        input[type="file"]:focus, select:focus {
            border-color: #E50914;
            outline: none;
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(229, 9, 20, 0.4);
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
            display: none;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(45deg, #E50914, #B8070F);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        
        .result.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        
        .result.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }
        
        .file-info {
            background: rgba(229, 9, 20, 0.1);
            border: 1px solid #E50914;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .file-info h4 {
            color: #E50914;
            margin-bottom: 10px;
        }
        
        .file-info p {
            margin: 5px 0;
            color: #ccc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 نظام رفع الملفات</h1>
        
        <!-- رفع الصور -->
        <div class="upload-section">
            <h3>📸 رفع الصور</h3>
            <form id="imageForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="imageFile">اختر صورة:</label>
                    <input type="file" id="imageFile" name="image" accept="image/*" required>
                </div>
                <div class="form-group">
                    <label for="imageType">نوع الصورة:</label>
                    <select id="imageType" name="type">
                        <option value="poster">بوستر</option>
                        <option value="thumbnail">صورة مصغرة</option>
                        <option value="avatar">صورة شخصية</option>
                    </select>
                </div>
                <button type="submit" class="btn">رفع الصورة</button>
                <div class="progress" id="imageProgress">
                    <div class="progress-bar" id="imageProgressBar"></div>
                </div>
                <div class="result" id="imageResult"></div>
            </form>
        </div>
        
        <!-- رفع الفيديوهات -->
        <div class="upload-section">
            <h3>🎥 رفع الفيديوهات</h3>
            <form id="videoForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="videoFile">اختر فيديو:</label>
                    <input type="file" id="videoFile" name="video" accept="video/*" required>
                </div>
                <div class="form-group">
                    <label for="videoType">نوع الفيديو:</label>
                    <select id="videoType" name="type">
                        <option value="movie">فيلم</option>
                        <option value="episode">حلقة مسلسل</option>
                        <option value="trailer">إعلان</option>
                    </select>
                </div>
                <button type="submit" class="btn">رفع الفيديو</button>
                <div class="progress" id="videoProgress">
                    <div class="progress-bar" id="videoProgressBar"></div>
                </div>
                <div class="result" id="videoResult"></div>
            </form>
        </div>
    </div>

    <script>
        // رفع الصور
        document.getElementById('imageForm').addEventListener('submit', function(e) {
            e.preventDefault();
            uploadFile(this, 'upload_image', 'imageProgress', 'imageProgressBar', 'imageResult');
        });
        
        // رفع الفيديوهات
        document.getElementById('videoForm').addEventListener('submit', function(e) {
            e.preventDefault();
            uploadFile(this, 'upload_video', 'videoProgress', 'videoProgressBar', 'videoResult');
        });
        
        function uploadFile(form, action, progressId, progressBarId, resultId) {
            const formData = new FormData(form);
            formData.append('action', action);
            
            const progress = document.getElementById(progressId);
            const progressBar = document.getElementById(progressBarId);
            const result = document.getElementById(resultId);
            
            // إظهار شريط التقدم
            progress.style.display = 'block';
            result.style.display = 'none';
            
            const xhr = new XMLHttpRequest();
            
            // تحديث شريط التقدم
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    progressBar.style.width = percentComplete + '%';
                }
            });
            
            // معالجة الاستجابة
            xhr.addEventListener('load', function() {
                progress.style.display = 'none';
                
                try {
                    const response = JSON.parse(xhr.responseText);
                    
                    if (response.success) {
                        result.className = 'result success';
                        result.innerHTML = `
                            <h4>✅ ${response.message}</h4>
                            <div class="file-info">
                                <h4>معلومات الملف:</h4>
                                <p><strong>اسم الملف:</strong> ${response.file_name}</p>
                                <p><strong>رابط الملف:</strong> <a href="${response.file_url}" target="_blank">${response.file_url}</a></p>
                                <p><strong>معرف الملف:</strong> ${response.file_id}</p>
                            </div>
                        `;
                        form.reset();
                    } else {
                        result.className = 'result error';
                        result.innerHTML = `<h4>❌ خطأ: ${response.error}</h4>`;
                    }
                } catch (e) {
                    result.className = 'result error';
                    result.innerHTML = '<h4>❌ خطأ في معالجة الاستجابة</h4>';
                }
                
                result.style.display = 'block';
            });
            
            // معالجة الأخطاء
            xhr.addEventListener('error', function() {
                progress.style.display = 'none';
                result.className = 'result error';
                result.innerHTML = '<h4>❌ خطأ في الشبكة</h4>';
                result.style.display = 'block';
            });
            
            xhr.open('POST', '');
            xhr.send(formData);
        }
    </script>
</body>
</html>
