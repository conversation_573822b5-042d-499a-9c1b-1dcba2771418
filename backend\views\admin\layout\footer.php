        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/jquery.min.js"></script>
    <script src="/assets/js/admin.js"></script>
    
    <!-- Custom Scripts -->
    <script>
        // CSRF Token for AJAX requests
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        // Sidebar toggle
        $('#sidebarToggle').click(function() {
            $('body').toggleClass('sidebar-collapsed');
        });
        
        // Auto-hide alerts
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
        
        // Submenu handling
        $('.has-submenu').click(function(e) {
            e.preventDefault();
            const submenu = $(this).next('.submenu');
            const arrow = $(this).find('.submenu-arrow');
            
            if (submenu.hasClass('show')) {
                submenu.removeClass('show');
                arrow.removeClass('fa-chevron-up').addClass('fa-chevron-down');
            } else {
                $('.submenu').removeClass('show');
                $('.submenu-arrow').removeClass('fa-chevron-up').addClass('fa-chevron-down');
                submenu.addClass('show');
                arrow.removeClass('fa-chevron-down').addClass('fa-chevron-up');
            }
        });
        
        // Search functionality
        $('.navbar-search input').on('input', function() {
            const query = $(this).val();
            if (query.length > 2) {
                // Implement search functionality
                console.log('Searching for:', query);
            }
        });
        
        // Notification mark as read
        $('.notification-item').click(function() {
            $(this).addClass('read');
        });
        
        // Data tables initialization
        if (typeof DataTable !== 'undefined') {
            $('.data-table').DataTable({
                language: {
                    url: '/assets/js/datatables-arabic.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']]
            });
        }
        
        // Confirmation dialogs
        $('.btn-delete').click(function(e) {
            e.preventDefault();
            const url = $(this).attr('href');
            
            if (confirm('هل أنت متأكد من الحذف؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                window.location.href = url;
            }
        });
        
        // Form validation
        $('.needs-validation').each(function() {
            $(this).on('submit', function(e) {
                if (!this.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                $(this).addClass('was-validated');
            });
        });
        
        // File upload preview
        $('.file-input').change(function() {
            const file = this.files[0];
            const preview = $(this).siblings('.file-preview');
            
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.html(`<img src="${e.target.result}" alt="Preview" class="img-thumbnail" style="max-width: 200px;">`);
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Status toggle
        $('.status-toggle').change(function() {
            const id = $(this).data('id');
            const type = $(this).data('type');
            const status = $(this).is(':checked') ? 'active' : 'inactive';
            
            $.post('/admin/toggle-status', {
                id: id,
                type: type,
                status: status
            })
            .done(function(response) {
                if (response.success) {
                    showToast('تم تحديث الحالة بنجاح', 'success');
                } else {
                    showToast('فشل في تحديث الحالة', 'error');
                }
            })
            .fail(function() {
                showToast('حدث خطأ في الاتصال', 'error');
            });
        });
        
        // Toast notifications
        function showToast(message, type = 'info') {
            const toast = $(`
                <div class="toast-notification toast-${type}">
                    <div class="toast-content">
                        <i class="fas fa-${getToastIcon(type)} me-2"></i>
                        ${message}
                    </div>
                    <button class="toast-close" onclick="$(this).parent().fadeOut()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `);
            
            $('.toast-container').append(toast);
            toast.fadeIn().delay(5000).fadeOut(function() {
                $(this).remove();
            });
        }
        
        function getToastIcon(type) {
            switch (type) {
                case 'success': return 'check-circle';
                case 'error': return 'exclamation-circle';
                case 'warning': return 'exclamation-triangle';
                default: return 'info-circle';
            }
        }
        
        // Real-time updates (if WebSocket is available)
        if (typeof WebSocket !== 'undefined') {
            // Implement WebSocket connection for real-time updates
            // const ws = new WebSocket('ws://localhost:8080');
            // ws.onmessage = function(event) {
            //     const data = JSON.parse(event.data);
            //     handleRealTimeUpdate(data);
            // };
        }
        
        // Export functionality
        $('.btn-export').click(function() {
            const format = $(this).data('format');
            const type = $(this).data('type');
            
            showToast('جاري تصدير البيانات...', 'info');
            
            window.location.href = `/admin/export?type=${type}&format=${format}`;
        });
        
        // Bulk actions
        $('.select-all').change(function() {
            $('.item-checkbox').prop('checked', $(this).is(':checked'));
            updateBulkActions();
        });
        
        $('.item-checkbox').change(function() {
            updateBulkActions();
        });
        
        function updateBulkActions() {
            const selectedCount = $('.item-checkbox:checked').length;
            const bulkActions = $('.bulk-actions');
            
            if (selectedCount > 0) {
                bulkActions.show();
                bulkActions.find('.selected-count').text(selectedCount);
            } else {
                bulkActions.hide();
            }
        }
        
        // Bulk delete
        $('.bulk-delete').click(function() {
            const selectedIds = $('.item-checkbox:checked').map(function() {
                return $(this).val();
            }).get();
            
            if (selectedIds.length === 0) {
                showToast('يرجى اختيار عنصر واحد على الأقل', 'warning');
                return;
            }
            
            if (confirm(`هل أنت متأكد من حذف ${selectedIds.length} عنصر؟`)) {
                $.post('/admin/bulk-delete', {
                    ids: selectedIds,
                    type: $(this).data('type')
                })
                .done(function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        showToast('فشل في حذف العناصر', 'error');
                    }
                })
                .fail(function() {
                    showToast('حدث خطأ في الاتصال', 'error');
                });
            }
        });
    </script>
    
    <!-- Toast Container -->
    <div class="toast-container"></div>
    
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?= $js ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <?php if (isset($inline_js)): ?>
        <script><?= $inline_js ?></script>
    <?php endif; ?>
    
</body>
</html>
