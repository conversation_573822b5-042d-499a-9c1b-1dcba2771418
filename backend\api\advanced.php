<?php
/**
 * API متقدم للميزات الإضافية
 * يتضمن المفضلة، سجل المشاهدة، التقييمات، والإحصائيات
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// تضمين أنظمة الأمان
require_once '../security_system.php';

try {
    $endpoint = $_GET['endpoint'] ?? '';
    
    switch ($endpoint) {
        // ===== نظام المفضلة =====
        case 'favorites':
            require_once '../favorites_system.php';
            $favoritesSystem = new FavoritesSystem();
            $userId = $_GET['user_id'] ?? 1;
            $contentType = $_GET['content_type'] ?? null;
            
            $favorites = $favoritesSystem->getUserFavorites($userId, $contentType);
            echo json_encode([
                'success' => true,
                'message' => 'User favorites retrieved successfully',
                'data' => $favorites,
                'user_id' => (int)$userId,
                'content_type' => $contentType,
                'total' => count($favorites)
            ]);
            break;
            
        case 'toggle_favorite':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                echo json_encode(['success' => false, 'error' => 'Method not allowed']);
                break;
            }
            
            require_once '../favorites_system.php';
            $favoritesSystem = new FavoritesSystem();
            
            $input = json_decode(file_get_contents('php://input'), true);
            $userId = $input['user_id'] ?? 1;
            $contentId = $input['content_id'] ?? 0;
            $contentType = $input['content_type'] ?? 'movie';
            
            $result = $favoritesSystem->toggleFavorite($userId, $contentId, $contentType);
            echo json_encode($result);
            break;
            
        case 'is_favorite':
            require_once '../favorites_system.php';
            $favoritesSystem = new FavoritesSystem();
            
            $userId = $_GET['user_id'] ?? 1;
            $contentId = $_GET['content_id'] ?? 0;
            $contentType = $_GET['content_type'] ?? 'movie';
            
            $isFavorite = $favoritesSystem->isFavorite($userId, $contentId, $contentType);
            echo json_encode([
                'success' => true,
                'is_favorite' => $isFavorite,
                'user_id' => (int)$userId,
                'content_id' => (int)$contentId,
                'content_type' => $contentType
            ]);
            break;
            
        // ===== سجل المشاهدة =====
        case 'watch_history':
            require_once '../favorites_system.php';
            $favoritesSystem = new FavoritesSystem();
            $userId = $_GET['user_id'] ?? 1;
            $limit = $_GET['limit'] ?? 50;
            $offset = $_GET['offset'] ?? 0;
            
            $history = $favoritesSystem->getUserWatchHistory($userId, $limit, $offset);
            echo json_encode([
                'success' => true,
                'message' => 'Watch history retrieved successfully',
                'data' => $history,
                'user_id' => (int)$userId,
                'total' => count($history)
            ]);
            break;
            
        case 'continue_watching':
            require_once '../favorites_system.php';
            $favoritesSystem = new FavoritesSystem();
            $userId = $_GET['user_id'] ?? 1;
            $limit = $_GET['limit'] ?? 10;
            
            $continueWatching = $favoritesSystem->getContinueWatching($userId, $limit);
            echo json_encode([
                'success' => true,
                'message' => 'Continue watching list retrieved successfully',
                'data' => $continueWatching,
                'user_id' => (int)$userId,
                'total' => count($continueWatching)
            ]);
            break;
            
        case 'update_watch_progress':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                echo json_encode(['success' => false, 'error' => 'Method not allowed']);
                break;
            }
            
            require_once '../favorites_system.php';
            $favoritesSystem = new FavoritesSystem();
            
            $input = json_decode(file_get_contents('php://input'), true);
            $userId = $input['user_id'] ?? 1;
            $contentId = $input['content_id'] ?? 0;
            $contentType = $input['content_type'] ?? 'movie';
            $watchTime = $input['watch_time'] ?? 0;
            $totalTime = $input['total_time'] ?? 0;
            $episodeId = $input['episode_id'] ?? null;
            
            $result = $favoritesSystem->updateWatchHistory($userId, $contentId, $contentType, $watchTime, $totalTime, $episodeId);
            echo json_encode($result);
            break;
            
        // ===== إحصائيات المستخدم =====
        case 'user_stats':
            require_once '../favorites_system.php';
            $favoritesSystem = new FavoritesSystem();
            $userId = $_GET['user_id'] ?? 1;
            
            $stats = $favoritesSystem->getUserStats($userId);
            echo json_encode([
                'success' => true,
                'message' => 'User statistics retrieved successfully',
                'data' => $stats,
                'user_id' => (int)$userId
            ]);
            break;
            
        // ===== نظام التقييمات =====
        case 'ratings':
            require_once '../ratings_system.php';
            $ratingsSystem = new RatingsSystem();
            
            $contentId = $_GET['content_id'] ?? 0;
            $contentType = $_GET['content_type'] ?? 'movie';
            $limit = $_GET['limit'] ?? 20;
            $offset = $_GET['offset'] ?? 0;
            
            $ratings = $ratingsSystem->getContentRatings($contentId, $contentType, $limit, $offset);
            $average = $ratingsSystem->getAverageRating($contentId, $contentType);
            $stats = $ratingsSystem->getRatingStats($contentId, $contentType);
            
            echo json_encode([
                'success' => true,
                'message' => 'Content ratings retrieved successfully',
                'data' => [
                    'ratings' => $ratings,
                    'average' => $average,
                    'stats' => $stats
                ],
                'content_id' => (int)$contentId,
                'content_type' => $contentType
            ]);
            break;
            
        case 'add_rating':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                echo json_encode(['success' => false, 'error' => 'Method not allowed']);
                break;
            }
            
            require_once '../ratings_system.php';
            $ratingsSystem = new RatingsSystem();
            
            $input = json_decode(file_get_contents('php://input'), true);
            $userId = $input['user_id'] ?? 1;
            $contentId = $input['content_id'] ?? 0;
            $contentType = $input['content_type'] ?? 'movie';
            $rating = $input['rating'] ?? 0;
            $comment = $input['comment'] ?? '';
            
            $result = $ratingsSystem->addRating($userId, $contentId, $contentType, $rating, $comment);
            echo json_encode($result);
            break;
            
        case 'user_rating':
            require_once '../ratings_system.php';
            $ratingsSystem = new RatingsSystem();
            
            $userId = $_GET['user_id'] ?? 1;
            $contentId = $_GET['content_id'] ?? 0;
            $contentType = $_GET['content_type'] ?? 'movie';
            
            $userRating = $ratingsSystem->getUserRating($userId, $contentId, $contentType);
            echo json_encode([
                'success' => true,
                'data' => $userRating,
                'user_id' => (int)$userId,
                'content_id' => (int)$contentId,
                'content_type' => $contentType
            ]);
            break;
            
        case 'top_rated':
            require_once '../ratings_system.php';
            $ratingsSystem = new RatingsSystem();
            
            $contentType = $_GET['content_type'] ?? 'movie';
            $limit = $_GET['limit'] ?? 10;
            
            $topRated = $ratingsSystem->getTopRatedContent($contentType, $limit);
            echo json_encode([
                'success' => true,
                'message' => 'Top rated content retrieved successfully',
                'data' => $topRated,
                'content_type' => $contentType,
                'total' => count($topRated)
            ]);
            break;
            
        // ===== إدارة الملفات =====
        case 'upload_status':
            echo json_encode([
                'success' => true,
                'message' => 'File upload system is available',
                'upload_url' => '../upload_handler.php',
                'max_file_size' => [
                    'image' => '5MB',
                    'video' => '500MB'
                ],
                'supported_formats' => [
                    'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
                    'videos' => ['mp4', 'avi', 'mkv', 'mov', 'wmv']
                ]
            ]);
            break;
            
        // ===== إحصائيات النظام =====
        case 'system_stats':
            try {
                $pdo = new PDO("mysql:host=localhost;dbname=shahid_platform;charset=utf8mb4", "root", "");
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // إحصائيات المحتوى
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM movies WHERE status = 'active'");
                $moviesCount = $stmt->fetch()['count'];
                
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM series WHERE status = 'active'");
                $seriesCount = $stmt->fetch()['count'];
                
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM episodes WHERE status = 'active'");
                $episodesCount = $stmt->fetch()['count'];
                
                // إحصائيات التقييمات
                $stmt = $pdo->query("SELECT COUNT(*) as count, AVG(rating) as avg_rating FROM ratings");
                $ratingsData = $stmt->fetch();
                
                // إحصائيات المفضلة
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM favorites");
                $favoritesCount = $stmt->fetch()['count'];
                
                echo json_encode([
                    'success' => true,
                    'message' => 'System statistics retrieved successfully',
                    'data' => [
                        'content' => [
                            'movies' => (int)$moviesCount,
                            'series' => (int)$seriesCount,
                            'episodes' => (int)$episodesCount,
                            'total_content' => (int)($moviesCount + $seriesCount)
                        ],
                        'engagement' => [
                            'total_ratings' => (int)$ratingsData['count'],
                            'average_rating' => round($ratingsData['avg_rating'], 1),
                            'total_favorites' => (int)$favoritesCount
                        ],
                        'system' => [
                            'api_version' => '2.0',
                            'last_updated' => date('Y-m-d H:i:s'),
                            'status' => 'operational'
                        ]
                    ]
                ]);
                
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'error' => 'Failed to retrieve system statistics: ' . $e->getMessage()
                ]);
            }
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'error' => 'Invalid endpoint',
                'message' => 'Advanced API endpoints for Shahid platform',
                'available_endpoints' => [
                    'favorites' => 'GET - Get user favorites (user_id, content_type)',
                    'toggle_favorite' => 'POST - Add/remove from favorites',
                    'is_favorite' => 'GET - Check if content is favorite',
                    'watch_history' => 'GET - Get user watch history',
                    'continue_watching' => 'GET - Get continue watching list',
                    'update_watch_progress' => 'POST - Update watch progress',
                    'user_stats' => 'GET - Get user statistics',
                    'ratings' => 'GET - Get content ratings',
                    'add_rating' => 'POST - Add content rating',
                    'user_rating' => 'GET - Get user rating for content',
                    'top_rated' => 'GET - Get top rated content',
                    'upload_status' => 'GET - Get file upload system status',
                    'system_stats' => 'GET - Get system statistics'
                ],
                'usage' => [
                    'base_url' => 'api/advanced.php',
                    'example' => 'api/advanced.php?endpoint=favorites&user_id=1'
                ]
            ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
