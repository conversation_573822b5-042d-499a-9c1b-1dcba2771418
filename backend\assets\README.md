# 🎨 **مجلد الأصول - Shahid Platform Assets**

هذا المجلد يحتوي على جميع الأصول الثابتة المستخدمة في منصة شاهد.

---

## 📁 **هيكل المجلد**

```
assets/
├── css/                    # ملفات الأنماط
│   ├── homepage.css       # أنماط الصفحة الرئيسية
│   ├── admin.css          # أنماط لوحة الإدارة
│   └── common.css         # أنماط مشتركة
├── js/                     # ملفات JavaScript
│   ├── homepage.js        # تفاعلات الصفحة الرئيسية
│   ├── admin.js           # تفاعلات لوحة الإدارة
│   └── common.js          # دوال مشتركة
├── images/                 # الصور والأيقونات
│   ├── logo.png           # شعار المنصة
│   ├── favicon.ico        # أيقونة المتصفح
│   └── backgrounds/       # خلفيات
├── fonts/                  # الخطوط المخصصة
│   ├── Cairo/             # خط القاهرة
│   ├── Tajawal/           # خط تجوال
│   └── Amiri/             # خط أميري
└── videos/                 # فيديوهات تجريبية
    ├── samples/           # عينات فيديو
    └── trailers/          # إعلانات تشويقية
```

---

## 🎨 **ملفات CSS**

### 📄 **homepage.css**
- أنماط الصفحة الرئيسية المتطورة
- تصميم متجاوب لجميع الأجهزة
- رسوم متحركة وتأثيرات تفاعلية
- ألوان وتدرجات احترافية

**الميزات:**
- ✅ تصميم متجاوب 100%
- ✅ رسوم متحركة CSS3
- ✅ تأثيرات hover متقدمة
- ✅ دعم RTL كامل
- ✅ تحسين للأداء

### 📄 **admin.css** (قريباً)
- أنماط لوحات الإدارة
- تصميم dashboard احترافي
- جداول وإحصائيات متقدمة

### 📄 **common.css** (قريباً)
- أنماط مشتركة
- متغيرات CSS
- فئات مساعدة

---

## ⚡ **ملفات JavaScript**

### 📄 **homepage.js**
- تفاعلات الصفحة الرئيسية المتقدمة
- رسوم متحركة ديناميكية
- اختصارات لوحة المفاتيح
- تأثيرات التمرير

**الوظائف الرئيسية:**
- `initializeHomepage()` - تهيئة الصفحة
- `initializeAnimations()` - تفعيل الرسوم المتحركة
- `initializeCounters()` - عدادات متحركة
- `initializeTimeUpdater()` - تحديث الوقت
- `initializeScrollEffects()` - تأثيرات التمرير
- `initializeMouseEffects()` - تأثيرات الماوس
- `initializeKeyboardShortcuts()` - اختصارات المفاتيح

**اختصارات لوحة المفاتيح:**
- `Ctrl + 1` - API الأساسي
- `Ctrl + 2` - لوحة الإدارة
- `Ctrl + 3` - لوحة التحكم
- `Ctrl + 4` - صفحات الاختبار
- `Ctrl + H` - عرض المساعدة
- `Esc` - إغلاق النوافذ المنبثقة

### 📄 **admin.js** (قريباً)
- تفاعلات لوحة الإدارة
- إدارة الجداول
- رسوم بيانية تفاعلية

### 📄 **common.js** (قريباً)
- دوال مشتركة
- مساعدات JavaScript
- أدوات عامة

---

## 🖼️ **الصور والأيقونات**

### 📁 **images/**
- `logo.png` - شعار منصة شاهد (1024x1024)
- `favicon.ico` - أيقونة المتصفح (32x32)
- `og-image.png` - صورة مشاركة وسائل التواصل (1200x630)

### 📁 **backgrounds/**
- خلفيات متدرجة
- أنماط وتكسترات
- صور خلفية عالية الجودة

### 📁 **icons/**
- أيقونات SVG
- أيقونات PNG بأحجام متعددة
- أيقونات تطبيق الجوال

---

## 🔤 **الخطوط المخصصة**

### 📁 **fonts/Cairo/**
- خط القاهرة العربي الحديث
- أوزان متعددة: Light, Regular, Bold
- دعم كامل للعربية والإنجليزية

### 📁 **fonts/Tajawal/**
- خط تجوال الأنيق
- مناسب للعناوين والنصوص
- تصميم عصري ومقروء

### 📁 **fonts/Amiri/**
- خط أميري التقليدي
- للنصوص الرسمية
- طابع عربي أصيل

---

## 🎬 **الفيديوهات التجريبية**

### 📁 **videos/samples/**
- فيديوهات تجريبية للاختبار
- جودات متعددة (480p, 720p, 1080p)
- تنسيقات مختلفة (MP4, WebM)

### 📁 **videos/trailers/**
- إعلانات تشويقية
- مقاطع ترويجية
- عروض توضيحية

---

## 🛠️ **إرشادات الاستخدام**

### 📝 **إضافة ملفات CSS جديدة:**
```html
<link rel="stylesheet" href="assets/css/filename.css">
```

### 📝 **إضافة ملفات JavaScript جديدة:**
```html
<script src="assets/js/filename.js"></script>
```

### 📝 **استخدام الصور:**
```html
<img src="assets/images/filename.png" alt="وصف الصورة">
```

### 📝 **استخدام الخطوط:**
```css
font-family: 'Cairo', 'Segoe UI', sans-serif;
```

---

## 🎨 **معايير التصميم**

### 🎨 **الألوان الأساسية:**
- **الأحمر الشاهد**: `#E50914`
- **الأحمر الداكن**: `#B8070F`
- **الخلفية الداكنة**: `#0F0F0F`
- **الخلفية الفاتحة**: `#1A1A1A`
- **النص الأساسي**: `#FFFFFF`
- **النص الثانوي**: `#CCCCCC`

### 📐 **المقاييس:**
- **نصف القطر الأساسي**: `8px`
- **نصف القطر الكبير**: `15px`
- **المسافة الأساسية**: `16px`
- **المسافة الكبيرة**: `32px`

### 🔤 **الخطوط:**
- **العناوين**: Cairo Bold
- **النصوص**: Cairo Regular
- **التفاصيل**: Cairo Light

---

## 📊 **الأداء والتحسين**

### ⚡ **تحسينات CSS:**
- ضغط الملفات في الإنتاج
- استخدام CSS Grid و Flexbox
- تحسين الرسوم المتحركة
- تقليل إعادة الرسم

### ⚡ **تحسينات JavaScript:**
- تحميل غير متزامن
- تجميع الملفات
- تقليل DOM manipulation
- استخدام requestAnimationFrame

### ⚡ **تحسينات الصور:**
- تنسيقات حديثة (WebP, AVIF)
- أحجام متجاوبة
- تحميل كسول (Lazy Loading)
- ضغط بدون فقدان

---

## 🔧 **أدوات التطوير**

### 🛠️ **أدوات CSS:**
- Sass/SCSS للمعالجة المسبقة
- PostCSS للتحسين
- Autoprefixer للتوافق
- PurgeCSS لإزالة الأنماط غير المستخدمة

### 🛠️ **أدوات JavaScript:**
- Babel للتوافق
- Webpack للتجميع
- ESLint للجودة
- Terser للضغط

### 🛠️ **أدوات الصور:**
- ImageOptim للضغط
- SVGO لتحسين SVG
- Sharp لمعالجة الصور
- Squoosh للضغط المتقدم

---

## 📋 **قائمة المهام**

### ✅ **مكتمل:**
- [x] ملف homepage.css
- [x] ملف homepage.js
- [x] هيكل المجلدات
- [x] التوثيق الأساسي

### 🔄 **قيد التطوير:**
- [ ] ملف admin.css
- [ ] ملف admin.js
- [ ] ملف common.css
- [ ] ملف common.js
- [ ] مجموعة الصور
- [ ] مجموعة الخطوط
- [ ] مجموعة الفيديوهات

### 🎯 **مخطط له:**
- [ ] نظام إدارة الأصول
- [ ] أدوات البناء التلقائي
- [ ] اختبارات الأداء
- [ ] تحسين SEO

---

## 📞 **الدعم والمساعدة**

للحصول على المساعدة في استخدام الأصول:

1. **مراجعة التوثيق** في هذا الملف
2. **فحص الأمثلة** في الملفات الموجودة
3. **اتباع معايير التصميم** المحددة
4. **اختبار التوافق** مع المتصفحات المختلفة

---

<div align="center">

## 🎨 **مجلد الأصول - جودة احترافية عالمية** ✨

**📁 منظم... 🎨 جميل... ⚡ محسن... 🛠️ قابل للصيانة**

</div>
