<?php
/**
 * Fix Database Configuration
 * Quick tool to fix database configuration issues
 */

$errors = [];
$success = [];

if ($_POST) {
    $dbHost = $_POST['db_host'] ?? '';
    $dbName = $_POST['db_name'] ?? '';
    $dbUser = $_POST['db_user'] ?? '';
    $dbPass = $_POST['db_pass'] ?? '';
    
    if (empty($dbHost) || empty($dbName) || empty($dbUser)) {
        $errors[] = 'Please fill all required fields';
    } else {
        try {
            // Test connection first
            $dsn = "mysql:host=$dbHost;charset=utf8mb4";
            $pdo = new PDO($dsn, $dbUser, $dbPass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create database if not exists
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // Test connection to the specific database
            $dsn = "mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4";
            $pdo = new PDO($dsn, $dbUser, $dbPass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create config directory if needed
            if (!is_dir('config')) {
                mkdir('config', 0755, true);
            }
            
            // Create config file
            $configContent = "<?php\n/**\n * Shahid - Database Configuration\n * Professional Video Streaming Platform\n * \n * This file is generated by the installation process\n * DO NOT EDIT MANUALLY\n */\n\nreturn [\n    'host' => '$dbHost',\n    'name' => '$dbName',\n    'username' => '$dbUser',\n    'password' => '$dbPass'\n];";
            
            if (file_put_contents('config/database.php', $configContent)) {
                $success[] = 'Database configuration saved successfully!';
                $success[] = 'You can now proceed to step 3 to create tables.';
            } else {
                $errors[] = 'Failed to save configuration file. Check write permissions.';
            }
            
        } catch (Exception $e) {
            $errors[] = 'Database connection failed: ' . $e->getMessage();
        }
    }
}

// Load current config if exists
$currentConfig = [];
if (file_exists('config/database.php')) {
    try {
        $currentConfig = include 'config/database.php';
    } catch (Exception $e) {
        // Ignore errors
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Database Configuration - Shahid</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .container { background: white; padding: 40px; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 500px; width: 90%; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { width: 60px; height: 60px; background: linear-gradient(45deg, #E50914, #B20710); border-radius: 12px; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; }
        h1 { color: #333; margin-bottom: 5px; }
        .subtitle { color: #666; font-size: 14px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: 500; color: #333; }
        input[type="text"], input[type="password"] { width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 14px; transition: border-color 0.3s; }
        input:focus { outline: none; border-color: #E50914; }
        .btn { background: linear-gradient(45deg, #E50914, #B20710); color: white; padding: 12px 30px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: 500; width: 100%; transition: transform 0.2s; }
        .btn:hover { transform: translateY(-2px); }
        .error { background: #fee; color: #c33; padding: 10px; border-radius: 5px; margin-bottom: 15px; border-left: 4px solid #c33; }
        .success { background: #efe; color: #363; padding: 10px; border-radius: 5px; margin-bottom: 15px; border-left: 4px solid #363; }
        .info { background: #e7f3ff; color: #0066cc; padding: 10px; border-radius: 5px; margin-bottom: 15px; border-left: 4px solid #0066cc; }
        .links { text-align: center; margin-top: 20px; }
        .links a { color: #E50914; text-decoration: none; margin: 0 10px; }
        .links a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔧</div>
            <h1>Fix Database Configuration</h1>
            <p class="subtitle">Shahid Installation Helper</p>
        </div>

        <?php if (!empty($errors)): ?>
            <?php foreach ($errors as $error): ?>
                <div class="error"><?php echo htmlspecialchars($error); ?></div>
            <?php endforeach; ?>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <?php foreach ($success as $msg): ?>
                <div class="success"><?php echo htmlspecialchars($msg); ?></div>
            <?php endforeach; ?>
            <div class="links">
                <a href="install_simple.php?step=3">Continue to Step 3</a> |
                <a href="test_db_config.php">Test Configuration</a>
            </div>
        <?php else: ?>
            
            <?php if (!empty($currentConfig)): ?>
                <div class="info">
                    <strong>Current Configuration Found:</strong><br>
                    Host: <?php echo htmlspecialchars($currentConfig['host'] ?? 'Not set'); ?><br>
                    Database: <?php echo htmlspecialchars($currentConfig['name'] ?? 'Not set'); ?><br>
                    Username: <?php echo htmlspecialchars($currentConfig['username'] ?? 'Not set'); ?>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="form-group">
                    <label>Database Host:</label>
                    <input type="text" name="db_host" value="<?php echo htmlspecialchars($currentConfig['host'] ?? 'localhost'); ?>" required>
                </div>
                
                <div class="form-group">
                    <label>Database Name:</label>
                    <input type="text" name="db_name" value="<?php echo htmlspecialchars($currentConfig['name'] ?? 'shahid_db'); ?>" required>
                </div>
                
                <div class="form-group">
                    <label>Database Username:</label>
                    <input type="text" name="db_user" value="<?php echo htmlspecialchars($currentConfig['username'] ?? 'root'); ?>" required>
                </div>
                
                <div class="form-group">
                    <label>Database Password:</label>
                    <input type="password" name="db_pass" value="<?php echo htmlspecialchars($currentConfig['password'] ?? ''); ?>">
                </div>
                
                <button type="submit" class="btn">Fix Configuration</button>
            </form>

            <div class="links">
                <a href="test_db_config.php">Test Current Config</a> |
                <a href="install_simple.php">Full Installation</a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
