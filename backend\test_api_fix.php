<?php
/**
 * Test API Fix - Shahid Platform
 * Quick test to verify API redirect fix
 */

echo "<h1>🔧 اختبار إصلاح API - Shahid Platform</h1>";

// Test API endpoints
echo "<h2>🔗 اختبار API Endpoints:</h2>";

$baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
$apiBaseUrl = $baseUrl . '/api';

$endpoints = [
    '' => 'الصفحة الرئيسية للـ API',
    '/status' => 'حالة النظام',
    '/movies' => 'قائمة الأفلام',
    '/series' => 'قائمة المسلسلات',
    '/test_api.php' => 'صفحة اختبار API'
];

foreach ($endpoints as $endpoint => $description) {
    $url = $apiBaseUrl . $endpoint;
    
    echo "<h3>🧪 اختبار: $description</h3>";
    echo "<p><strong>URL:</strong> <a href='$url' target='_blank'>$url</a></p>";
    
    try {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10,
                'header' => 'Content-Type: application/json'
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            echo "<div class='error'>❌ لا يمكن الوصول للـ endpoint</div>";
        } else {
            // Try to decode JSON
            $data = json_decode($response, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                if (isset($data['success']) && $data['success']) {
                    echo "<div class='success'>✅ يعمل بشكل صحيح - استجابة JSON صالحة</div>";
                    if (isset($data['message'])) {
                        echo "<p><strong>الرسالة:</strong> " . htmlspecialchars($data['message']) . "</p>";
                    }
                } else {
                    echo "<div class='warning'>⚠️ استجابة JSON لكن success = false</div>";
                    if (isset($data['error'])) {
                        echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($data['error']) . "</p>";
                    }
                }
            } else {
                // Check if it's HTML (might be an error page)
                if (strpos($response, '<html>') !== false || strpos($response, '<!DOCTYPE') !== false) {
                    if (strpos($response, 'Fatal error') !== false) {
                        echo "<div class='error'>❌ خطأ PHP Fatal Error</div>";
                    } elseif (strpos($response, 'Warning') !== false) {
                        echo "<div class='warning'>⚠️ تحذيرات PHP</div>";
                    } else {
                        echo "<div class='info'>ℹ️ استجابة HTML (ربما صفحة اختبار)</div>";
                    }
                } else {
                    echo "<div class='warning'>⚠️ استجابة غير JSON</div>";
                }
                
                // Show first 200 characters of response
                $preview = substr($response, 0, 200);
                echo "<details><summary>عرض الاستجابة</summary><pre>" . htmlspecialchars($preview) . "...</pre></details>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في الاختبار: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "<hr>";
}

// Test file existence
echo "<h2>📁 فحص الملفات:</h2>";

$apiFiles = [
    'index_simple.php' => 'API البسيط (الرئيسي)',
    'test_api.php' => 'صفحة اختبار API',
    '.htaccess' => 'إعدادات Apache'
];

foreach ($apiFiles as $file => $description) {
    $filePath = __DIR__ . '/api/' . $file;
    if (file_exists($filePath)) {
        echo "<div class='success'>✅ <strong>$description:</strong> $file موجود</div>";
    } else {
        echo "<div class='error'>❌ <strong>$description:</strong> $file غير موجود</div>";
    }
}

// Check .htaccess content
echo "<h2>⚙️ فحص إعدادات .htaccess:</h2>";

$htaccessPath = __DIR__ . '/api/.htaccess';
if (file_exists($htaccessPath)) {
    $htaccessContent = file_get_contents($htaccessPath);
    
    if (strpos($htaccessContent, 'index_simple.php') !== false) {
        echo "<div class='success'>✅ .htaccess يوجه إلى index_simple.php</div>";
    } else {
        echo "<div class='warning'>⚠️ .htaccess قد لا يوجه بشكل صحيح</div>";
    }
    
    if (strpos($htaccessContent, 'DirectoryIndex') !== false) {
        echo "<div class='success'>✅ DirectoryIndex محدد</div>";
    } else {
        echo "<div class='warning'>⚠️ DirectoryIndex غير محدد</div>";
    }
} else {
    echo "<div class='error'>❌ ملف .htaccess غير موجود</div>";
}

// Final recommendations
echo "<h2>🎯 التوصيات:</h2>";

echo "<div class='info'>";
echo "<h3>✅ الحلول المطبقة:</h3>";
echo "<ul>";
echo "<li>تم توجيه جميع طلبات API إلى index_simple.php</li>";
echo "<li>تم تحديث .htaccess لتجنب أخطاء index.php</li>";
echo "<li>تم تعيين DirectoryIndex إلى index_simple.php</li>";
echo "</ul>";
echo "</div>";

echo "<div class='success'>";
echo "<h3>🔗 الروابط الموصى بها:</h3>";
echo "<p><a href='api/test_api.php' target='_blank' class='btn btn-primary'>🧪 صفحة اختبار API الشاملة</a></p>";
echo "<p><a href='api/' target='_blank' class='btn btn-info'>🔗 API الرئيسي</a></p>";
echo "<p><a href='admin/' target='_blank' class='btn btn-success'>🎛️ لوحة الإدارة</a></p>";
echo "</div>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 40px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1 {
    color: #E50914;
    border-bottom: 3px solid #E50914;
    padding-bottom: 10px;
    margin-bottom: 30px;
}

h2 {
    color: #333;
    margin-top: 30px;
    margin-bottom: 15px;
    border-left: 4px solid #E50914;
    padding-left: 15px;
}

h3 {
    color: #555;
    margin-top: 20px;
    margin-bottom: 10px;
}

.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: all 0.3s;
}

.btn-primary {
    background: #E50914;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn:hover {
    opacity: 0.8;
    text-decoration: none;
}

hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 20px 0;
}

details {
    margin: 10px 0;
}

summary {
    cursor: pointer;
    font-weight: bold;
    color: #E50914;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 0.9em;
}

p {
    margin: 10px 0;
}

ul {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}

a {
    color: #E50914;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
