<?php
/**
 * Shahid - Series Model
 * Professional Video Streaming Platform
 */

class Series extends Model {
    protected $table = 'series';
    
    public function getLatest($limit = 20, $offset = 0) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' 
                ORDER BY created_at DESC 
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getFeatured($limit = 10) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' AND featured = 1 
                ORDER BY views DESC, created_at DESC 
                LIMIT :limit";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getPopular($limit = 20, $offset = 0) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' 
                ORDER BY views DESC, rating DESC 
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getEpisodes($seriesId, $season = null) {
        $sql = "SELECT * FROM episodes WHERE series_id = :series_id";
        $params = [':series_id' => $seriesId];
        
        if ($season !== null) {
            $sql .= " AND season_number = :season";
            $params[':season'] = $season;
        }
        
        $sql .= " ORDER BY season_number ASC, episode_number ASC";
        
        $stmt = $this->db->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getSeasons($seriesId) {
        $sql = "SELECT season_number, COUNT(*) as episode_count 
                FROM episodes 
                WHERE series_id = :series_id AND status = 'active'
                GROUP BY season_number 
                ORDER BY season_number ASC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':series_id', $seriesId);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getLatestEpisode($seriesId) {
        $sql = "SELECT * FROM episodes 
                WHERE series_id = :series_id AND status = 'active'
                ORDER BY season_number DESC, episode_number DESC 
                LIMIT 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':series_id', $seriesId);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    public function getNextEpisode($seriesId, $currentSeason, $currentEpisode) {
        // Try to get next episode in same season
        $sql = "SELECT * FROM episodes 
                WHERE series_id = :series_id 
                AND season_number = :season 
                AND episode_number > :episode 
                AND status = 'active'
                ORDER BY episode_number ASC 
                LIMIT 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':series_id', $seriesId);
        $stmt->bindParam(':season', $currentSeason);
        $stmt->bindParam(':episode', $currentEpisode);
        $stmt->execute();
        $nextEpisode = $stmt->fetch();
        
        if ($nextEpisode) {
            return $nextEpisode;
        }
        
        // Try to get first episode of next season
        $sql = "SELECT * FROM episodes 
                WHERE series_id = :series_id 
                AND season_number > :season 
                AND status = 'active'
                ORDER BY season_number ASC, episode_number ASC 
                LIMIT 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':series_id', $seriesId);
        $stmt->bindParam(':season', $currentSeason);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    public function search($query, $limit = 20, $offset = 0) {
        $searchTerm = "%$query%";
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' AND (
                    title LIKE :search OR 
                    description LIKE :search OR 
                    director LIKE :search OR
                    JSON_SEARCH(cast, 'one', :search) IS NOT NULL
                )
                ORDER BY 
                    CASE WHEN title LIKE :search THEN 1 ELSE 2 END,
                    views DESC 
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':search', $searchTerm);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function getRelated($seriesId, $limit = 6) {
        // Get current series genres
        $series = $this->find($seriesId);
        if (!$series || !$series['genres']) {
            return [];
        }
        
        $genres = json_decode($series['genres'], true);
        if (empty($genres)) {
            return [];
        }
        
        $genreConditions = [];
        $params = [':series_id' => $seriesId, ':limit' => $limit];
        
        foreach ($genres as $index => $genre) {
            $paramName = ":genre_$index";
            $genreConditions[] = "JSON_CONTAINS(genres, $paramName)";
            $params[$paramName] = json_encode($genre);
        }
        
        $sql = "SELECT * FROM {$this->table} 
                WHERE status = 'active' AND id != :series_id AND (
                    " . implode(' OR ', $genreConditions) . "
                )
                ORDER BY rating DESC, views DESC 
                LIMIT :limit";
        
        $stmt = $this->db->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function incrementViews($seriesId) {
        $sql = "UPDATE {$this->table} SET views = views + 1 WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $seriesId);
        return $stmt->execute();
    }
    
    public function getWithDetails($seriesId) {
        $series = $this->find($seriesId);
        if (!$series) {
            return null;
        }
        
        // Add episodes and seasons
        $series['seasons'] = $this->getSeasons($seriesId);
        $series['latest_episode'] = $this->getLatestEpisode($seriesId);
        
        // Decode JSON fields
        $series['genres'] = json_decode($series['genres'], true) ?: [];
        $series['cast'] = json_decode($series['cast'], true) ?: [];
        
        return $series;
    }
    
    public function createSeries($data) {
        // Encode JSON fields
        if (isset($data['genres']) && is_array($data['genres'])) {
            $data['genres'] = json_encode($data['genres']);
        }
        if (isset($data['cast']) && is_array($data['cast'])) {
            $data['cast'] = json_encode($data['cast']);
        }
        
        // Generate slug if not provided
        if (!isset($data['slug']) && isset($data['title'])) {
            $data['slug'] = $this->generateSlug($data['title']);
        }
        
        return $this->create($data);
    }
    
    public function updateSeries($seriesId, $data) {
        // Encode JSON fields
        if (isset($data['genres']) && is_array($data['genres'])) {
            $data['genres'] = json_encode($data['genres']);
        }
        if (isset($data['cast']) && is_array($data['cast'])) {
            $data['cast'] = json_encode($data['cast']);
        }
        
        return $this->update($seriesId, $data);
    }
    
    public function updateEpisodeCount($seriesId) {
        $sql = "UPDATE {$this->table} SET 
                total_episodes = (
                    SELECT COUNT(*) FROM episodes 
                    WHERE series_id = :series_id AND status = 'active'
                ),
                total_seasons = (
                    SELECT COUNT(DISTINCT season_number) FROM episodes 
                    WHERE series_id = :series_id AND status = 'active'
                )
                WHERE id = :series_id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':series_id', $seriesId);
        return $stmt->execute();
    }
    
    private function generateSlug($title) {
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
        
        // Check if slug exists
        $originalSlug = $slug;
        $counter = 1;
        
        while ($this->findWhere(['slug' => $slug])) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
}
?>
