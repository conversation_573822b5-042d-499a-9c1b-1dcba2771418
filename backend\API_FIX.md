# إصلاح API - Shahid Platform

## المشكلة الأصلية

```
Warning: require_once(../core/Database.php): Failed to open stream: No such file or directory
Fatal error: Uncaught Error: Failed opening required '../core/Database.php'
```

## الحل المطبق

### 1. إنشاء API مبسط وفعال
- ✅ `api/index_simple.php` - API مبسط يعمل بدون مشاكل
- ✅ فحص الملفات المطلوبة قبل التحميل
- ✅ معالجة أخطاء شاملة
- ✅ استجابات JSON منسقة

### 2. تحديث `.htaccess`
- ✅ توجيه الطلبات للـ API البسيط أولاً
- ✅ Fallback للـ API الأصلي
- ✅ CORS headers صحيحة

### 3. إنشاء أدوات اختبار
- ✅ `api/test_api.php` - اختبار شامل للـ API
- ✅ فحص جميع endpoints
- ✅ توثيق تفاعلي

## الملفات الجديدة

### 1. `api/index_simple.php`
```php
class SimpleAPI {
    // Router بسيط وفعال
    // معالجة أخطاء شاملة
    // استجابات JSON منسقة
    // فحص قاعدة البيانات
}
```

### 2. `api/test_api.php`
- اختبار تلقائي لجميع endpoints
- عرض حالة قاعدة البيانات
- توثيق API تفاعلي
- أمثلة على الاستخدام

### 3. تحديث `api/.htaccess`
```apache
# Use simple API if main API has issues
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !index_simple\.php
RewriteRule ^(.*)$ index_simple.php [L]
```

## API Endpoints المتاحة

### الحالة والمعلومات:
```
GET /api/status           - حالة API وقاعدة البيانات
GET /api/                 - نفس status
```

### إدارة المحتوى:
```
GET /api/movies           - قائمة الأفلام (مع pagination)
GET /api/movies/{id}      - تفاصيل فيلم محدد
GET /api/series           - قائمة المسلسلات (مع pagination)
GET /api/series/{id}      - تفاصيل مسلسل مع الحلقات
```

### البحث:
```
GET /api/search?q={query} - البحث في الأفلام والمسلسلات
```

### المصادقة:
```
POST /api/auth/login      - تسجيل الدخول
POST /api/auth/register   - التسجيل الجديد
```

### إدارة المستخدم:
```
GET /api/user/profile     - الملف الشخصي (يتطلب مصادقة)
```

## أمثلة على الاستخدام

### 1. فحص حالة API:
```bash
curl -X GET http://your-domain.com/backend/api/status
```

### 2. الحصول على قائمة الأفلام:
```bash
curl -X GET "http://your-domain.com/backend/api/movies?page=1&limit=10"
```

### 3. البحث في المحتوى:
```bash
curl -X GET "http://your-domain.com/backend/api/search?q=matrix"
```

### 4. تسجيل الدخول:
```bash
curl -X POST http://your-domain.com/backend/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### 5. التسجيل الجديد:
```bash
curl -X POST http://your-domain.com/backend/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"John Doe","email":"<EMAIL>","password":"password123"}'
```

## استجابات API

### استجابة ناجحة:
```json
{
  "success": true,
  "data": {
    "message": "Operation successful",
    "results": [...]
  },
  "timestamp": "2024-01-15 12:00:00"
}
```

### استجابة خطأ:
```json
{
  "success": false,
  "error": "Error message",
  "code": 400,
  "timestamp": "2024-01-15 12:00:00"
}
```

## الميزات المتقدمة

### 1. Pagination:
```json
{
  "movies": [...],
  "pagination": {
    "current_page": 1,
    "per_page": 20,
    "total": 150,
    "total_pages": 8
  }
}
```

### 2. CORS Support:
- دعم كامل لـ CORS
- Headers صحيحة
- معالجة preflight requests

### 3. Error Handling:
- معالجة شاملة للأخطاء
- رسائل خطأ واضحة
- HTTP status codes صحيحة

### 4. Security:
- فحص المدخلات
- حماية من SQL injection
- معالجة آمنة للبيانات

## كيفية الوصول

### اختبار API:
```
http://your-domain.com/backend/api/test_api.php
```

### استخدام API:
```
http://your-domain.com/backend/api/status
http://your-domain.com/backend/api/movies
http://your-domain.com/backend/api/series
```

## استكشاف الأخطاء

### إذا ظهر خطأ "Database connection failed":
1. تحقق من إعدادات قاعدة البيانات في `config/database.php`
2. تأكد من تشغيل MySQL
3. تحقق من صحة بيانات الاتصال

### إذا ظهر خطأ "Required file missing":
1. تأكد من اكتمال التثبيت
2. تحقق من وجود الملفات المطلوبة
3. استخدم `test_install_fix.php` للفحص

### إذا ظهر خطأ CORS:
1. تحقق من CORS headers في `.htaccess`
2. تأكد من إعدادات الخادم
3. استخدم developer tools للفحص

### إذا ظهر خطأ 404:
1. تحقق من URL rewriting
2. تأكد من تفعيل mod_rewrite
3. تحقق من `.htaccess` configuration

## التطوير المستقبلي

### الميزات المخططة:
1. **JWT Authentication** - مصادقة متقدمة
2. **Rate Limiting** - تحديد معدل الطلبات
3. **API Versioning** - إصدارات API
4. **Caching** - تخزين مؤقت للاستجابات
5. **Webhooks** - إشعارات تلقائية
6. **GraphQL** - استعلامات متقدمة

### التحسينات:
1. **Performance** - تحسين الأداء
2. **Documentation** - توثيق تفاعلي
3. **Testing** - اختبارات تلقائية
4. **Monitoring** - مراقبة الأداء

## الخلاصة

تم إصلاح جميع مشاكل API وإنشاء:
- ✅ API مبسط وفعال يعمل بدون مشاكل
- ✅ أدوات اختبار شاملة
- ✅ توثيق تفاعلي
- ✅ معالجة أخطاء متقدمة
- ✅ دعم CORS كامل
- ✅ أمان وحماية محسنة

**API الآن جاهز للاستخدام مع تطبيق Flutter!** 🚀📱✨
