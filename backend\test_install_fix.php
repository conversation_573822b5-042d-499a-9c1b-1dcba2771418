<?php
/**
 * Test Installation Fix
 * Quick test to verify both installation files work
 */

echo "<h1>🔧 Installation Fix Test</h1>";

echo "<h2>📁 File Status Check:</h2>";

$files = [
    'install.php' => 'Original Installation File',
    'install_simple.php' => 'Simplified Installation File',
    'database/schema.sql' => 'Original Database Schema',
    'database/schema_simple.sql' => 'Simplified Database Schema',
    'config/' => 'Configuration Directory'
];

foreach ($files as $file => $description) {
    $exists = file_exists($file) ? '✅' : '❌';
    $status = file_exists($file) ? 'EXISTS' : 'MISSING';
    echo "<p>{$exists} <strong>{$description}</strong>: {$file} - {$status}</p>";
}

echo "<h2>🔗 Installation Links:</h2>";
echo "<p><a href='install.php' target='_blank' style='color: #E50914; text-decoration: none; font-weight: bold;'>🔧 Original Install (Fixed)</a></p>";
echo "<p><a href='install_simple.php' target='_blank' style='color: #28a745; text-decoration: none; font-weight: bold;'>✨ Simple Install (Recommended)</a></p>";

echo "<h2>📋 Installation Status:</h2>";

$statusFiles = [
    'config/database.php' => 'Database Configuration',
    'config/tables_created.lock' => 'Tables Created',
    'config/admin_created.lock' => 'Admin Account Created',
    'config/installed.lock' => 'Installation Complete'
];

foreach ($statusFiles as $file => $description) {
    $exists = file_exists($file) ? '✅' : '⏳';
    $status = file_exists($file) ? 'COMPLETED' : 'PENDING';
    echo "<p>{$exists} <strong>{$description}</strong>: {$status}</p>";
}

echo "<h2>🗄️ Database Connection Test:</h2>";

if (file_exists('config/database.php')) {
    try {
        $config = include 'config/database.php';
        $dsn = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p>✅ <strong>Database Connection:</strong> SUCCESS</p>";
        
        // Test tables
        $tables = ['users', 'movies', 'series', 'episodes'];
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                echo "<p>✅ <strong>Table '$table':</strong> {$count} records</p>";
            } catch (Exception $e) {
                echo "<p>❌ <strong>Table '$table':</strong> NOT FOUND</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p>❌ <strong>Database Connection:</strong> FAILED - " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>⏳ <strong>Database Configuration:</strong> NOT CONFIGURED</p>";
}

echo "<h2>🚀 Next Steps:</h2>";

if (!file_exists('config/database.php')) {
    echo "<p>1. Run the installation using one of the links above</p>";
    echo "<p>2. Configure your database settings</p>";
    echo "<p>3. Create the database tables</p>";
    echo "<p>4. Create an admin account</p>";
} elseif (!file_exists('config/tables_created.lock')) {
    echo "<p>1. Continue with table creation</p>";
    echo "<p>2. Create an admin account</p>";
} elseif (!file_exists('config/admin_created.lock')) {
    echo "<p>1. Create an admin account</p>";
} elseif (!file_exists('config/installed.lock')) {
    echo "<p>1. Complete the installation</p>";
} else {
    echo "<p>✅ Installation is complete!</p>";
    echo "<p>🌐 <a href='index.php'>Go to Website</a></p>";
    echo "<p>⚙️ <a href='admin/'>Go to Admin Panel</a></p>";
}

echo "<h2>🔒 Security Recommendations:</h2>";
echo "<p>After successful installation:</p>";
echo "<ul>";
echo "<li>Delete installation files: <code>rm install*.php</code></li>";
echo "<li>Protect config directory: <code>chmod 644 config/</code></li>";
echo "<li>Add .htaccess to config directory</li>";
echo "<li>Change default admin password</li>";
echo "</ul>";

echo "<style>
body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }
h1 { color: #E50914; border-bottom: 3px solid #E50914; padding-bottom: 10px; }
h2 { color: #333; margin-top: 30px; border-left: 4px solid #E50914; padding-left: 15px; }
p { margin: 10px 0; padding: 8px; background: white; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
a { color: #E50914; text-decoration: none; font-weight: bold; }
a:hover { text-decoration: underline; }
code { background: #f1f1f1; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
ul { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
li { margin: 8px 0; }
</style>";
?>
