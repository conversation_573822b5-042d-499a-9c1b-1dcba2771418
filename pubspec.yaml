name: shahid_app
description: "Shahid - Professional Video Streaming Platform Mobile App"

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
#
# This version is used _only_ for the Runner app, which is used if you just do
# a `flutter run` or a `flutter make-host-app-editable`. It has no impact
# on any other native host app that you embed your Flutter project into.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # UI Components
  cupertino_icons: ^1.0.8

  # HTTP requests
  http: ^1.1.0
  dio: ^5.3.2

  # State Management
  provider: ^6.1.1

  # Local Storage
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0

  # Video Player
  video_player: ^2.8.1
  chewie: ^1.7.4

  # Image handling
  cached_network_image: ^3.3.0
  image_picker: ^1.0.4

  # Navigation
  go_router: ^12.1.3

  # JSON serialization
  json_annotation: ^4.8.1

  # Notifications
  firebase_messaging: ^14.7.6
  flutter_local_notifications: ^16.3.0

  # Authentication
  firebase_auth: ^4.15.0
  google_sign_in: ^6.1.6

  # Payment
  stripe_payment: ^1.1.4

  # Utils
  intl: ^0.19.0
  url_launcher: ^6.2.1
  permission_handler: ^11.1.0
  connectivity_plus: ^5.0.2

  # UI Enhancements
  shimmer: ^3.0.0
  lottie: ^2.7.0
  flutter_staggered_grid_view: ^0.7.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add Flutter specific custom fonts to your application, add a fonts
  # section here, in this "flutter" section. Each entry in this list should
  # have a "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package


  # This section identifies your Flutter project as a module meant for
  # embedding in a native host app.  These identifiers should _not_ ordinarily
  # be changed after generation - they are used to ensure that the tooling can
  # maintain consistency when adding or modifying assets and plugins.
  # They also do not have any bearing on your native host application's
  # identifiers, which may be completely independent or the same as these.
  module:
    androidX: true
    androidPackage: com.example.flutter_module_1
    iosBundleIdentifier: com.example.flutterModule1
