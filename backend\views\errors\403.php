<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>403 - ممنوع الوصول | Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .error-container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
            position: relative;
            z-index: 2;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            background: linear-gradient(45deg, #FF6B35, #F7931E);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            text-shadow: 0 0 30px rgba(255, 107, 53, 0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(255, 107, 53, 0.5); }
            to { text-shadow: 0 0 40px rgba(255, 107, 53, 0.8); }
        }
        
        .error-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .error-message {
            font-size: 1.2rem;
            color: #ccc;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .lock-icon {
            font-size: 4rem;
            color: #FF6B35;
            margin-bottom: 1rem;
            animation: shake 1s ease-in-out infinite;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #555, #333);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        
        .btn-secondary:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }
        
        .info-box {
            background: rgba(255, 107, 53, 0.1);
            border: 1px solid rgba(255, 107, 53, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: right;
        }
        
        .info-box h3 {
            color: #FF6B35;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .info-box ul {
            list-style: none;
            padding: 0;
        }
        
        .info-box li {
            margin-bottom: 0.5rem;
            padding-right: 1rem;
            position: relative;
        }
        
        .info-box li::before {
            content: '•';
            color: #FF6B35;
            position: absolute;
            right: 0;
            font-weight: bold;
        }
        
        .contact-info {
            background: rgba(45, 45, 45, 0.8);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .contact-info h3 {
            color: #E50914;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .contact-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .contact-link {
            background: rgba(229, 9, 20, 0.1);
            color: #E50914;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(229, 9, 20, 0.3);
        }
        
        .contact-link:hover {
            background: rgba(229, 9, 20, 0.2);
            transform: translateY(-2px);
        }
        
        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            opacity: 0.1;
        }
        
        .floating-icon {
            position: absolute;
            font-size: 2rem;
            color: #FF6B35;
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-icon:nth-child(2) { top: 20%; right: 10%; animation-delay: 1s; }
        .floating-icon:nth-child(3) { bottom: 20%; left: 15%; animation-delay: 2s; }
        .floating-icon:nth-child(4) { bottom: 10%; right: 20%; animation-delay: 3s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 5rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="background-animation">
        <div class="floating-icon">🔒</div>
        <div class="floating-icon">🛡️</div>
        <div class="floating-icon">⚠️</div>
        <div class="floating-icon">🚫</div>
    </div>
    
    <div class="error-container">
        <div class="lock-icon">🔒</div>
        <div class="error-code">403</div>
        <h1 class="error-title">ممنوع الوصول</h1>
        <p class="error-message">
            عذراً، ليس لديك صلاحية للوصول إلى هذه الصفحة أو المورد المطلوب.
            <br>
            يرجى التحقق من صلاحياتك أو تسجيل الدخول بحساب مناسب.
        </p>
        
        <div class="info-box">
            <h3>الأسباب المحتملة:</h3>
            <ul>
                <li>لم تقم بتسجيل الدخول</li>
                <li>انتهت صلاحية جلستك</li>
                <li>ليس لديك الصلاحيات المطلوبة</li>
                <li>تم تقييد الوصول لهذا المورد</li>
                <li>تحتاج إلى اشتراك مميز</li>
            </ul>
        </div>
        
        <div class="error-actions">
            <a href="/backend/api/?endpoint=login" class="btn">
                🔑 تسجيل الدخول
            </a>
            <a href="/backend/" class="btn btn-secondary">
                🏠 الصفحة الرئيسية
            </a>
            <a href="javascript:history.back()" class="btn btn-secondary">
                ← العودة للخلف
            </a>
        </div>
        
        <div class="contact-info">
            <h3>تحتاج مساعدة؟</h3>
            <div class="contact-links">
                <a href="/backend/admin/" class="contact-link">لوحة الإدارة</a>
                <a href="/backend/api/?endpoint=help" class="contact-link">المساعدة</a>
                <a href="/backend/api/?endpoint=contact" class="contact-link">اتصل بنا</a>
                <a href="/backend/api/?endpoint=support" class="contact-link">الدعم الفني</a>
            </div>
        </div>
    </div>
    
    <script>
        // رسالة تحذيرية في وحدة التحكم
        console.warn(`
🔒 تحذير أمني - Shahid Platform 🔒

تم منع الوصول إلى هذا المورد.
السبب: عدم وجود صلاحيات كافية

إذا كنت تعتقد أن هذا خطأ، يرجى:
1. التأكد من تسجيل الدخول
2. التحقق من صلاحياتك
3. الاتصال بالدعم الفني

تاريخ المحاولة: ${new Date().toLocaleString('ar-SA')}
عنوان IP: ${window.location.hostname}
        `);
        
        // تسجيل محاولة الوصول غير المصرح بها
        fetch('/backend/api/advanced.php?endpoint=log_access_attempt', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                type: 'unauthorized_access',
                url: window.location.href,
                timestamp: new Date().toISOString(),
                user_agent: navigator.userAgent
            })
        }).catch(e => console.log('تم تسجيل محاولة الوصول'));
        
        // تأثيرات بصرية
        document.addEventListener('mousemove', function(e) {
            const icons = document.querySelectorAll('.floating-icon');
            icons.forEach((icon, index) => {
                const speed = (index + 1) * 0.0001;
                const x = (e.clientX * speed);
                const y = (e.clientY * speed);
                icon.style.transform += ` translate(${x}px, ${y}px)`;
            });
        });
        
        // تحديث عنوان الصفحة
        let titleIndex = 0;
        const titles = [
            '403 - ممنوع الوصول | Shahid Platform',
            '🔒 الوصول مقيد - تحقق من صلاحياتك',
            '🛡️ حماية أمنية نشطة',
            '⚠️ تسجيل الدخول مطلوب'
        ];
        
        setInterval(() => {
            document.title = titles[titleIndex];
            titleIndex = (titleIndex + 1) % titles.length;
        }, 3000);
        
        // إعادة توجيه تلقائي بعد 30 ثانية
        setTimeout(() => {
            if (confirm('هل تريد الانتقال إلى صفحة تسجيل الدخول؟')) {
                window.location.href = '/backend/api/?endpoint=login';
            }
        }, 30000);
    </script>
</body>
</html>
