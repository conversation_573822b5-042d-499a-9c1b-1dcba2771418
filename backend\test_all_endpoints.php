<?php
/**
 * Test All API Endpoints - Shahid Platform
 * Comprehensive test of all working endpoints
 */

echo "<h1>🎉 اختبار جميع API Endpoints - Shahid Platform</h1>";

$baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
$apiBaseUrl = $baseUrl . '/api';

// All endpoints to test
$endpoints = [
    // Basic endpoints
    '' => 'الصفحة الرئيسية للـ API',
    '?endpoint=status' => 'حالة النظام',
    '?endpoint=movies' => 'قائمة الأفلام',
    '?endpoint=series' => 'قائمة المسلسلات',
    '?endpoint=search&q=test' => 'البحث',
    
    // API format endpoints
    '?api=status' => 'حالة النظام (API format)',
    '?api=movies' => 'الأفلام (API format)',
    '?api=series' => 'المسلسلات (API format)',
    '?api=search&q=test' => 'البحث (API format)',
    
    // Direct file access
    'simple_test.php' => 'ملف الاختبار البسيط',
    'test_api.php' => 'صفحة اختبار API الأصلية',
    'index.html' => 'صفحة HTML الاحتياطية'
];

echo "<h2>🔗 اختبار جميع Endpoints:</h2>";

$successCount = 0;
$totalCount = count($endpoints);

foreach ($endpoints as $endpoint => $description) {
    $url = $apiBaseUrl . '/' . $endpoint;
    
    echo "<h3>🧪 اختبار: $description</h3>";
    echo "<p><strong>URL:</strong> <a href='$url' target='_blank'>$url</a></p>";
    
    try {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10,
                'header' => [
                    'Content-Type: application/json',
                    'Accept: application/json',
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                ]
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            $error = error_get_last();
            echo "<div class='error'>❌ فشل في الوصول: " . htmlspecialchars($error['message'] ?? 'Unknown error') . "</div>";
        } else {
            $responseLength = strlen($response);
            
            // Try to decode as JSON
            $data = json_decode($response, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                // Valid JSON response
                if (isset($data['success'])) {
                    if ($data['success']) {
                        echo "<div class='success'>✅ يعمل بشكل مثالي - JSON صالح</div>";
                        $successCount++;
                        
                        // Show key information
                        if (isset($data['message'])) {
                            echo "<p>💬 الرسالة: " . htmlspecialchars($data['message']) . "</p>";
                        }
                        
                        if (isset($data['data'])) {
                            if (is_array($data['data'])) {
                                $count = count($data['data']);
                                echo "<p>📊 عدد العناصر: $count</p>";
                                
                                // Show first item details for movies/series
                                if ($count > 0 && isset($data['data'][0]['title'])) {
                                    $firstItem = $data['data'][0];
                                    echo "<p>🎬 أول عنصر: " . htmlspecialchars($firstItem['title']) . "</p>";
                                }
                            } elseif (isset($data['data']['endpoints'])) {
                                $endpointCount = count($data['data']['endpoints']);
                                echo "<p>🔗 عدد Endpoints: $endpointCount</p>";
                            }
                        }
                        
                        if (isset($data['total'])) {
                            echo "<p>📈 المجموع: " . $data['total'] . "</p>";
                        }
                        
                        if (isset($data['query'])) {
                            echo "<p>🔍 البحث عن: " . htmlspecialchars($data['query']) . "</p>";
                        }
                        
                    } else {
                        echo "<div class='warning'>⚠️ JSON صالح لكن success = false</div>";
                        if (isset($data['error'])) {
                            echo "<p>❌ الخطأ: " . htmlspecialchars($data['error']) . "</p>";
                        }
                    }
                } else {
                    echo "<div class='info'>ℹ️ JSON صالح بدون حقل success</div>";
                    $successCount++;
                }
                
                // Show compact JSON for successful responses
                if (isset($data['success']) && $data['success'] && $responseLength < 1500) {
                    echo "<details><summary>عرض الاستجابة JSON</summary>";
                    echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                    echo "</details>";
                }
                
            } else {
                // Not JSON, check if HTML or plain text
                if (strpos($response, '<!DOCTYPE html>') !== false || strpos($response, '<html>') !== false) {
                    echo "<div class='info'>📄 استجابة HTML (حجم: $responseLength بايت)</div>";
                    
                    // Check for errors in HTML
                    if (strpos($response, 'Fatal error') !== false) {
                        echo "<div class='error'>❌ يحتوي على Fatal Error</div>";
                    } elseif (strpos($response, 'Warning') !== false) {
                        echo "<div class='warning'>⚠️ يحتوي على تحذيرات PHP</div>";
                    } elseif (strpos($response, 'Forbidden') !== false) {
                        echo "<div class='error'>❌ يحتوي على رسالة Forbidden</div>";
                    } else {
                        echo "<div class='success'>✅ صفحة HTML تعمل</div>";
                        $successCount++;
                    }
                } else {
                    // Plain text response
                    if ($responseLength < 100 && strpos($response, 'Works') !== false) {
                        echo "<div class='success'>✅ استجابة نصية تعمل: " . htmlspecialchars($response) . "</div>";
                        $successCount++;
                    } else {
                        echo "<div class='warning'>⚠️ استجابة غير JSON/HTML (حجم: $responseLength بايت)</div>";
                        
                        // Show first 200 characters
                        $preview = substr($response, 0, 200);
                        echo "<details><summary>عرض أول 200 حرف</summary>";
                        echo "<pre>" . htmlspecialchars($preview) . "...</pre>";
                        echo "</details>";
                    }
                }
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في الاختبار: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "<hr>";
}

// Summary
echo "<h2>📊 ملخص النتائج:</h2>";

$successPercentage = round(($successCount / $totalCount) * 100, 1);

if ($successPercentage >= 80) {
    echo "<div class='success'>";
    echo "<h3>🎉 ممتاز! API يعمل بشكل رائع</h3>";
    echo "<p><strong>معدل النجاح:</strong> $successCount من $totalCount ($successPercentage%)</p>";
    echo "</div>";
} elseif ($successPercentage >= 60) {
    echo "<div class='warning'>";
    echo "<h3>⚠️ جيد، لكن يحتاج بعض التحسينات</h3>";
    echo "<p><strong>معدل النجاح:</strong> $successCount من $totalCount ($successPercentage%)</p>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h3>❌ يحتاج إصلاحات إضافية</h3>";
    echo "<p><strong>معدل النجاح:</strong> $successCount من $totalCount ($successPercentage%)</p>";
    echo "</div>";
}

// Recommendations based on results
echo "<h2>🎯 التوصيات:</h2>";

if ($successPercentage >= 80) {
    echo "<div class='success'>";
    echo "<h3>✅ API جاهز للاستخدام!</h3>";
    echo "<ul>";
    echo "<li>جميع Endpoints الأساسية تعمل</li>";
    echo "<li>يمكن البدء في تطوير التطبيق</li>";
    echo "<li>يمكن إضافة المزيد من البيانات التجريبية</li>";
    echo "<li>يمكن تطوير واجهة المستخدم</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>🔗 الخطوات التالية:</h3>";
    echo "<ol>";
    echo "<li><strong>إضافة بيانات تجريبية:</strong> أفلام ومسلسلات أكثر</li>";
    echo "<li><strong>تطوير Flutter App:</strong> ربط التطبيق بالـ API</li>";
    echo "<li><strong>إضافة المصادقة:</strong> تسجيل دخول وإنشاء حسابات</li>";
    echo "<li><strong>إضافة رفع الملفات:</strong> رفع الفيديوهات والصور</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<h3>🔗 الروابط المفيدة:</h3>";
echo "<p><a href='api/' target='_blank' class='btn btn-primary'>🔗 صفحة API الرئيسية</a></p>";
echo "<p><a href='api/?endpoint=status' target='_blank' class='btn btn-success'>📊 حالة النظام</a></p>";
echo "<p><a href='api/?endpoint=movies' target='_blank' class='btn btn-info'>🎬 قائمة الأفلام</a></p>";
echo "<p><a href='api/?endpoint=series' target='_blank' class='btn btn-warning'>📺 قائمة المسلسلات</a></p>";
echo "<p><a href='admin/' target='_blank' class='btn btn-secondary'>🎛️ لوحة الإدارة</a></p>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 40px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1 {
    color: #E50914;
    border-bottom: 3px solid #E50914;
    padding-bottom: 10px;
    margin-bottom: 30px;
}

h2 {
    color: #333;
    margin-top: 30px;
    margin-bottom: 15px;
    border-left: 4px solid #E50914;
    padding-left: 15px;
}

h3 {
    color: #555;
    margin-top: 20px;
    margin-bottom: 10px;
}

.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: all 0.3s;
}

.btn-primary { background: #E50914; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-info { background: #17a2b8; color: white; }
.btn-warning { background: #ffc107; color: black; }
.btn-secondary { background: #6c757d; color: white; }

.btn:hover {
    opacity: 0.8;
    text-decoration: none;
}

hr {
    border: none;
    border-top: 1px solid #ddd;
    margin: 20px 0;
}

details {
    margin: 10px 0;
}

summary {
    cursor: pointer;
    font-weight: bold;
    color: #E50914;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 0.9em;
    border: 1px solid #ddd;
    max-height: 400px;
    overflow-y: auto;
}

p {
    margin: 10px 0;
}

ol, ul {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}

a {
    color: #E50914;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
