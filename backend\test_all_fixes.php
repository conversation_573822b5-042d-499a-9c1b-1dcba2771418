<?php
/**
 * Test All Fixes - Shahid Platform
 * Comprehensive test for all SQL fixes
 */

echo "<h1>🔧 اختبار شامل لجميع الإصلاحات - Shahid Platform</h1>";

// Check if database config exists
if (!file_exists('config/database.php')) {
    echo "<div class='error'>❌ ملف إعدادات قاعدة البيانات غير موجود</div>";
    exit();
}

try {
    $config = include 'config/database.php';
    $pdo = new PDO("mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4", 
                   $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ اتصال قاعدة البيانات ناجح</div>";
    
    // Test 1: API Movies Query
    echo "<h2>🎬 اختبار 1: API - استعلام الأفلام</h2>";
    try {
        $limit = 10;
        $offset = 0;
        $stmt = $pdo->prepare("SELECT * FROM movies WHERE status = 'published' ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $stmt->execute();
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<div class='success'>✅ API Movies Query - يعمل بشكل صحيح (عدد النتائج: " . count($movies) . ")</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ API Movies Query - خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    // Test 2: API Series Query
    echo "<h2>📺 اختبار 2: API - استعلام المسلسلات</h2>";
    try {
        $limit = 10;
        $offset = 0;
        $stmt = $pdo->prepare("SELECT * FROM series WHERE status = 'published' ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $stmt->execute();
        $series = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<div class='success'>✅ API Series Query - يعمل بشكل صحيح (عدد النتائج: " . count($series) . ")</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ API Series Query - خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    // Test 3: Admin Movies Query
    echo "<h2>🎛️ اختبار 3: Admin - استعلام الأفلام</h2>";
    try {
        $limit = 10;
        $offset = 0;
        $stmt = $pdo->prepare("SELECT * FROM movies ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $stmt->execute();
        $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<div class='success'>✅ Admin Movies Query - يعمل بشكل صحيح (عدد النتائج: " . count($movies) . ")</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Admin Movies Query - خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    // Test 4: Admin Series Query
    echo "<h2>🎛️ اختبار 4: Admin - استعلام المسلسلات</h2>";
    try {
        $limit = 10;
        $offset = 0;
        $stmt = $pdo->prepare("SELECT s.*, COUNT(e.id) as episodes_count FROM series s LEFT JOIN episodes e ON s.id = e.series_id GROUP BY s.id ORDER BY s.created_at DESC LIMIT $limit OFFSET $offset");
        $stmt->execute();
        $series = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<div class='success'>✅ Admin Series Query - يعمل بشكل صحيح (عدد النتائج: " . count($series) . ")</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Admin Series Query - خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    // Test 5: Admin Users Query
    echo "<h2>👥 اختبار 5: Admin - استعلام المستخدمين</h2>";
    try {
        $limit = 10;
        $offset = 0;
        $stmt = $pdo->prepare("SELECT * FROM users ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<div class='success'>✅ Admin Users Query - يعمل بشكل صحيح (عدد النتائج: " . count($users) . ")</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Admin Users Query - خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    // Test 6: Admin Users Search Query
    echo "<h2>🔍 اختبار 6: Admin - البحث في المستخدمين</h2>";
    try {
        $limit = 10;
        $offset = 0;
        $search = "admin";
        $searchTerm = "%$search%";
        $stmt = $pdo->prepare("SELECT * FROM users WHERE name LIKE ? OR email LIKE ? ORDER BY created_at DESC LIMIT $limit OFFSET $offset");
        $stmt->execute([$searchTerm, $searchTerm]);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<div class='success'>✅ Admin Users Search Query - يعمل بشكل صحيح (عدد النتائج: " . count($users) . ")</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Admin Users Search Query - خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    // Test 7: API Endpoints
    echo "<h2>🔗 اختبار 7: API Endpoints</h2>";
    
    $endpoints = [
        'status' => 'حالة النظام',
        'movies' => 'قائمة الأفلام',
        'series' => 'قائمة المسلسلات'
    ];
    
    foreach ($endpoints as $endpoint => $description) {
        $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/' . $endpoint;
        
        try {
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'timeout' => 5,
                    'header' => 'Content-Type: application/json'
                ]
            ]);
            
            $response = @file_get_contents($url, false, $context);
            if ($response) {
                $data = json_decode($response, true);
                if ($data && isset($data['success']) && $data['success']) {
                    echo "<div class='success'>✅ $description - يعمل بشكل صحيح</div>";
                } else {
                    echo "<div class='warning'>⚠️ $description - استجابة غير متوقعة</div>";
                }
            } else {
                echo "<div class='error'>❌ $description - لا يمكن الوصول</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ $description - خطأ في الاختبار</div>";
        }
    }
    
    // Test 8: Database Tables
    echo "<h2>🗄️ اختبار 8: جداول قاعدة البيانات</h2>";
    
    $requiredTables = ['users', 'movies', 'series', 'episodes', 'settings'];
    $missingTables = [];
    
    foreach ($requiredTables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                // Check if table has data
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $count = $stmt->fetch()['count'];
                echo "<div class='success'>✅ جدول $table - موجود ($count سجل)</div>";
            } else {
                $missingTables[] = $table;
                echo "<div class='error'>❌ جدول $table - غير موجود</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ جدول $table - خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
    }
    
    // Final Summary
    echo "<h2>📊 الملخص النهائي</h2>";
    
    if (empty($missingTables)) {
        echo "<div class='success'>";
        echo "<h3>🎉 جميع الاختبارات نجحت!</h3>";
        echo "<p>✅ جميع استعلامات SQL تعمل بشكل صحيح</p>";
        echo "<p>✅ جميع API endpoints تعمل</p>";
        echo "<p>✅ جميع جداول قاعدة البيانات موجودة</p>";
        echo "<p>✅ لوحة الإدارة جاهزة للاستخدام</p>";
        echo "</div>";
        
        echo "<h3>🔗 الروابط المتاحة:</h3>";
        echo "<p><a href='index_simple.php' class='btn btn-primary'>🏠 الصفحة الرئيسية</a></p>";
        echo "<p><a href='admin/' class='btn btn-success'>🎛️ لوحة الإدارة</a></p>";
        echo "<p><a href='api/test_api.php' class='btn btn-info'>🧪 اختبار API</a></p>";
        
    } else {
        echo "<div class='warning'>";
        echo "<h3>⚠️ هناك بعض المشاكل</h3>";
        echo "<p>الجداول المفقودة: " . implode(', ', $missingTables) . "</p>";
        echo "<p><a href='create_database.php' class='btn btn-primary'>🗄️ إنشاء الجداول المفقودة</a></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ خطأ في الاتصال بقاعدة البيانات:</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<h4>🔧 الحلول المقترحة:</h4>";
    echo "<ul>";
    echo "<li>تأكد من إنشاء قاعدة البيانات: <a href='create_database.php'>create_database.php</a></li>";
    echo "<li>تحقق من بيانات الاتصال في config/database.php</li>";
    echo "<li>تأكد من تشغيل خادم MySQL/MariaDB</li>";
    echo "</ul>";
    echo "</div>";
}

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 40px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1 {
    color: #E50914;
    border-bottom: 3px solid #E50914;
    padding-bottom: 10px;
    margin-bottom: 30px;
}

h2 {
    color: #333;
    margin-top: 30px;
    margin-bottom: 15px;
    border-left: 4px solid #E50914;
    padding-left: 15px;
}

h3 {
    color: #555;
    margin-top: 20px;
    margin-bottom: 10px;
}

.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: all 0.3s;
}

.btn-primary {
    background: #E50914;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn:hover {
    opacity: 0.8;
    text-decoration: none;
}

p {
    margin: 10px 0;
}

ul {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}

a {
    color: #E50914;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
