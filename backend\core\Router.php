<?php
/**
 * Shahid - Router Class
 * Professional Video Streaming Platform
 */

class Router {
    private $routes = [];
    
    public function add($route, $controller) {
        $this->routes[$route] = $controller;
    }
    
    public function dispatch($url) {
        // Remove query string
        $url = strtok($url, '?');
        
        // Check for exact match first
        if (isset($this->routes[$url])) {
            $this->callController($this->routes[$url]);
            return;
        }
        
        // Check for parameterized routes
        foreach ($this->routes as $route => $controller) {
            $pattern = $this->convertRouteToRegex($route);
            if (preg_match($pattern, $url, $matches)) {
                array_shift($matches); // Remove full match
                $this->callController($controller, $matches);
                return;
            }
        }
        
        // No route found
        throw new Exception("Route not found: " . $url);
    }
    
    private function convertRouteToRegex($route) {
        // Convert {param} to regex capture groups
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $route);
        return '#^' . $pattern . '$#';
    }
    
    private function callController($controllerAction, $params = []) {
        list($controllerName, $action) = explode('@', $controllerAction);
        
        $controllerFile = 'controllers/' . $controllerName . '.php';
        if (!file_exists($controllerFile)) {
            throw new Exception("Controller file not found: " . $controllerFile);
        }
        
        require_once $controllerFile;
        
        if (!class_exists($controllerName)) {
            throw new Exception("Controller class not found: " . $controllerName);
        }
        
        $controller = new $controllerName();
        
        if (!method_exists($controller, $action)) {
            throw new Exception("Action not found: " . $action);
        }
        
        // Call the controller action with parameters
        call_user_func_array([$controller, $action], $params);
    }
}
?>
