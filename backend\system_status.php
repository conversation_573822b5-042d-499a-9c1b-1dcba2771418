<?php
/**
 * Shahid Platform - System Status Check
 * Real-time system status monitoring
 */

// Function to check database connection
function checkDatabase() {
    try {
        $config = include __DIR__ . '/config/database.php';
        $pdo = new PDO(
            "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4",
            $config['username'],
            $config['password']
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check if tables exist
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Check admin user
        $adminExists = false;
        if (in_array('users', $tables)) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
            $stmt->execute();
            $adminExists = $stmt->fetchColumn() > 0;
        }
        
        return [
            'connected' => true,
            'tables' => count($tables),
            'admin_exists' => $adminExists,
            'error' => null
        ];
    } catch (Exception $e) {
        return [
            'connected' => false,
            'tables' => 0,
            'admin_exists' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Get system status
$dbStatus = checkDatabase();
$phpVersion = phpversion();

// Return JSON if requested
if (isset($_GET['json'])) {
    header('Content-Type: application/json');
    echo json_encode([
        'database' => $dbStatus,
        'php_version' => $phpVersion,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// Return status for homepage
if (isset($_GET['status'])) {
    $status = [
        'database_connected' => $dbStatus['connected'],
        'tables_exist' => $dbStatus['tables'] > 0,
        'admin_exists' => $dbStatus['admin_exists'],
        'installation_complete' => file_exists(__DIR__ . '/config/database.php'),
        'php_version' => $phpVersion
    ];
    
    echo json_encode($status);
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة النظام - Shahid Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
            color: #fff;
            line-height: 1.6;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(47, 47, 47, 0.9);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(229, 9, 20, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #E50914;
        }
        
        .header h1 {
            color: #E50914;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .status-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            border-left: 4px solid #666;
        }
        
        .status-card.success {
            border-left-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }
        
        .status-card.error {
            border-left-color: #F44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .status-card.warning {
            border-left-color: #FF9800;
            background: rgba(255, 152, 0, 0.1);
        }
        
        .status-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }
        
        .status-icon {
            font-size: 2rem;
        }
        
        .status-title {
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .status-details {
            color: #ccc;
            font-size: 0.9rem;
        }
        
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .btn {
            background: linear-gradient(45deg, #E50914, #B8070F);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #FF9800, #F57C00);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #555, #333);
        }
        
        .refresh-btn {
            position: fixed;
            top: 2rem;
            left: 2rem;
            background: rgba(229, 9, 20, 0.8);
            border: none;
            color: white;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #E50914;
            transform: rotate(180deg);
        }
        
        .timestamp {
            text-align: center;
            color: #666;
            font-size: 0.9rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <button class="refresh-btn" onclick="location.reload()" title="تحديث">🔄</button>
    
    <div class="container">
        <div class="header">
            <h1>📊 حالة النظام</h1>
            <p>مراقبة مباشرة لحالة منصة شاهد</p>
        </div>

        <div class="status-grid">
            <!-- Database Status -->
            <div class="status-card <?php echo $dbStatus['connected'] ? 'success' : 'error'; ?>">
                <div class="status-header">
                    <div class="status-title">قاعدة البيانات</div>
                    <div class="status-icon"><?php echo $dbStatus['connected'] ? '✅' : '❌'; ?></div>
                </div>
                <div class="status-details">
                    <?php if ($dbStatus['connected']): ?>
                        متصل بنجاح<br>
                        الجداول: <?php echo $dbStatus['tables']; ?> جدول
                    <?php else: ?>
                        غير متصل<br>
                        الخطأ: <?php echo htmlspecialchars($dbStatus['error']); ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Tables Status -->
            <div class="status-card <?php echo $dbStatus['tables'] > 0 ? 'success' : 'error'; ?>">
                <div class="status-header">
                    <div class="status-title">الجداول</div>
                    <div class="status-icon"><?php echo $dbStatus['tables'] > 0 ? '✅' : '❌'; ?></div>
                </div>
                <div class="status-details">
                    <?php if ($dbStatus['tables'] > 0): ?>
                        موجودة (<?php echo $dbStatus['tables']; ?> جدول)
                    <?php else: ?>
                        غير موجودة
                    <?php endif; ?>
                </div>
            </div>

            <!-- Admin Account -->
            <div class="status-card <?php echo $dbStatus['admin_exists'] ? 'success' : 'error'; ?>">
                <div class="status-header">
                    <div class="status-title">حساب المدير</div>
                    <div class="status-icon"><?php echo $dbStatus['admin_exists'] ? '✅' : '❌'; ?></div>
                </div>
                <div class="status-details">
                    <?php if ($dbStatus['admin_exists']): ?>
                        موجود ومفعل
                    <?php else: ?>
                        غير موجود
                    <?php endif; ?>
                </div>
            </div>

            <!-- Installation Status -->
            <div class="status-card <?php echo file_exists(__DIR__ . '/config/database.php') ? 'success' : 'error'; ?>">
                <div class="status-header">
                    <div class="status-title">التثبيت</div>
                    <div class="status-icon"><?php echo file_exists(__DIR__ . '/config/database.php') ? '✅' : '❌'; ?></div>
                </div>
                <div class="status-details">
                    <?php if (file_exists(__DIR__ . '/config/database.php')): ?>
                        مكتمل
                    <?php else: ?>
                        غير مكتمل
                    <?php endif; ?>
                </div>
            </div>

            <!-- PHP Version -->
            <div class="status-card <?php echo version_compare($phpVersion, '7.4.0', '>=') ? 'success' : 'warning'; ?>">
                <div class="status-header">
                    <div class="status-title">إصدار PHP</div>
                    <div class="status-icon"><?php echo version_compare($phpVersion, '7.4.0', '>=') ? '✅' : '⚠️'; ?></div>
                </div>
                <div class="status-details">
                    <?php echo $phpVersion; ?><br>
                    <?php echo version_compare($phpVersion, '7.4.0', '>=') ? 'متوافق' : 'يحتاج ترقية'; ?>
                </div>
            </div>

            <!-- System Health -->
            <div class="status-card <?php echo ($dbStatus['connected'] && $dbStatus['tables'] > 0 && $dbStatus['admin_exists']) ? 'success' : 'error'; ?>">
                <div class="status-header">
                    <div class="status-title">حالة النظام العامة</div>
                    <div class="status-icon"><?php echo ($dbStatus['connected'] && $dbStatus['tables'] > 0 && $dbStatus['admin_exists']) ? '✅' : '❌'; ?></div>
                </div>
                <div class="status-details">
                    <?php if ($dbStatus['connected'] && $dbStatus['tables'] > 0 && $dbStatus['admin_exists']): ?>
                        النظام يعمل بشكل طبيعي
                    <?php else: ?>
                        يحتاج إصلاح
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="actions">
            <?php if (!$dbStatus['connected'] || $dbStatus['tables'] == 0 || !$dbStatus['admin_exists']): ?>
                <a href="fix_database.php" class="btn btn-warning">🔧 إصلاح قاعدة البيانات</a>
            <?php endif; ?>
            
            <?php if ($dbStatus['connected'] && $dbStatus['tables'] > 0): ?>
                <a href="admin/dashboard.php" class="btn btn-success">🎛️ لوحة الإدارة</a>
                <a href="api/test.php" class="btn btn-success">🔗 اختبار API</a>
            <?php endif; ?>
            
            <a href="homepage.php" class="btn btn-secondary">🏠 الرئيسية</a>
            <a href="http://localhost/phpmyadmin/" target="_blank" class="btn btn-secondary">📊 phpMyAdmin</a>
        </div>

        <div class="timestamp">
            آخر تحديث: <?php echo date('Y-m-d H:i:s'); ?>
        </div>
    </div>

    <script>
        // Auto refresh every 30 seconds
        setTimeout(() => {
            location.reload();
        }, 30000);
        
        console.log('📊 System Status Monitor loaded');
    </script>
</body>
</html>
