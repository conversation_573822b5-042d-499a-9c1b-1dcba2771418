/**
 * Shahid Video Player
 * Advanced video player with custom features
 */

let player;
let currentMovieData;
let progressUpdateInterval;
let skipIntroTimeout;
let isFullscreen = false;
let lastActivity = Date.now();
let hideControlsTimeout;

// Initialize the video player
function initializePlayer(movieData) {
    currentMovieData = movieData;
    
    // Initialize Video.js player
    player = videojs('shahid-player', {
        fluid: true,
        responsive: true,
        playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 2],
        controls: true,
        preload: 'auto',
        html5: {
            vhs: {
                overrideNative: true
            }
        },
        techOrder: ['html5'],
        sources: getVideoSources(),
        tracks: getSubtitleTracks()
    });
    
    // Player event listeners
    setupPlayerEvents();
    
    // Load user settings
    loadUserSettings();
    
    // Set initial progress
    if (movieData.watchProgress > 0) {
        player.ready(() => {
            player.currentTime(movieData.watchProgress);
            showResumeNotification();
        });
    }
    
    // Hide loading overlay when ready
    player.ready(() => {
        hideLoadingOverlay();
    });
    
    // Setup keyboard shortcuts
    setupKeyboardShortcuts();
    
    // Setup activity tracking
    setupActivityTracking();
}

// Setup player event listeners
function setupPlayerEvents() {
    // Progress tracking
    player.on('timeupdate', updateProgress);
    
    // Error handling
    player.on('error', handlePlayerError);
    
    // Loading states
    player.on('loadstart', showLoadingOverlay);
    player.on('canplay', hideLoadingOverlay);
    player.on('waiting', showLoadingOverlay);
    player.on('playing', hideLoadingOverlay);
    
    // Skip intro detection
    player.on('timeupdate', checkSkipIntro);
    
    // Auto-hide controls
    player.on('useractive', showControls);
    player.on('userinactive', hideControls);
    
    // Fullscreen events
    player.on('fullscreenchange', handleFullscreenChange);
    
    // Quality change
    player.on('loadedmetadata', setupQualitySelector);
    
    // Ended event
    player.on('ended', handleVideoEnded);
    
    // Volume change
    player.on('volumechange', saveVolumeSettings);
}

// Get video sources based on available qualities
function getVideoSources() {
    const sources = [];
    const qualities = currentMovieData.qualities || ['auto'];
    
    qualities.forEach(quality => {
        sources.push({
            src: `/api/stream/${currentMovieData.id}?quality=${quality}&token=${currentMovieData.token}`,
            type: 'video/mp4',
            label: quality
        });
    });
    
    return sources;
}

// Get subtitle tracks
function getSubtitleTracks() {
    const tracks = [];
    const subtitles = currentMovieData.subtitles || [];
    
    subtitles.forEach(subtitle => {
        tracks.push({
            kind: 'subtitles',
            src: `/api/subtitles/${subtitle.id}?token=${currentMovieData.token}`,
            srclang: subtitle.language,
            label: subtitle.language_name,
            default: subtitle.is_default
        });
    });
    
    return tracks;
}

// Update watch progress
function updateProgress() {
    if (!player || player.paused()) return;
    
    const currentTime = Math.floor(player.currentTime());
    const duration = Math.floor(player.duration());
    
    if (currentTime > 0 && duration > 0) {
        // Save progress every 10 seconds
        if (currentTime % 10 === 0) {
            saveWatchProgress(currentTime, duration);
        }
        
        // Update progress bar in UI
        updateProgressBar(currentTime, duration);
    }
}

// Save watch progress to server
function saveWatchProgress(currentTime, duration) {
    $.post('/movies/update-progress', {
        movie_id: currentMovieData.id,
        progress: currentTime,
        duration: duration,
        csrf_token: $('meta[name="csrf-token"]').attr('content')
    }).fail(function() {
        console.warn('Failed to save watch progress');
    });
}

// Update progress bar
function updateProgressBar(currentTime, duration) {
    const percentage = (currentTime / duration) * 100;
    $('.progress-bar').css('width', percentage + '%');
}

// Handle player errors
function handlePlayerError() {
    const error = player.error();
    console.error('Player error:', error);
    
    let errorMessage = 'حدث خطأ غير متوقع في تشغيل الفيديو';
    
    if (error) {
        switch (error.code) {
            case 1:
                errorMessage = 'تم إلغاء تحميل الفيديو';
                break;
            case 2:
                errorMessage = 'حدث خطأ في الشبكة أثناء تحميل الفيديو';
                break;
            case 3:
                errorMessage = 'حدث خطأ في فك تشفير الفيديو';
                break;
            case 4:
                errorMessage = 'تنسيق الفيديو غير مدعوم';
                break;
        }
    }
    
    showErrorOverlay(errorMessage);
}

// Show/hide overlays
function showLoadingOverlay() {
    $('#loadingOverlay').fadeIn();
}

function hideLoadingOverlay() {
    $('#loadingOverlay').fadeOut();
}

function showErrorOverlay(message) {
    $('#errorMessage').text(message);
    $('#errorOverlay').fadeIn();
    hideLoadingOverlay();
}

function hideErrorOverlay() {
    $('#errorOverlay').fadeOut();
}

// Retry video loading
function retryVideo() {
    hideErrorOverlay();
    player.load();
    player.play();
}

// Skip intro functionality
function checkSkipIntro() {
    const currentTime = player.currentTime();
    const skipIntroStart = 10; // Start showing skip button at 10 seconds
    const skipIntroEnd = 90;   // Hide skip button at 90 seconds
    
    if (currentTime >= skipIntroStart && currentTime <= skipIntroEnd) {
        $('#skipIntroBtn').fadeIn();
        
        // Auto-skip if enabled
        if (localStorage.getItem('skipIntroAuto') === 'true' && !skipIntroTimeout) {
            skipIntroTimeout = setTimeout(() => {
                skipIntro();
            }, 5000); // Auto-skip after 5 seconds
        }
    } else {
        $('#skipIntroBtn').fadeOut();
        if (skipIntroTimeout) {
            clearTimeout(skipIntroTimeout);
            skipIntroTimeout = null;
        }
    }
}

function skipIntro() {
    player.currentTime(90); // Skip to 90 seconds
    $('#skipIntroBtn').fadeOut();
    if (skipIntroTimeout) {
        clearTimeout(skipIntroTimeout);
        skipIntroTimeout = null;
    }
}

// Keyboard shortcuts
function setupKeyboardShortcuts() {
    $(document).keydown(function(e) {
        if (!player) return;
        
        // Prevent default for player shortcuts
        const playerShortcuts = [32, 37, 39, 38, 40, 70, 77, 75, 74, 76];
        if (playerShortcuts.includes(e.keyCode)) {
            e.preventDefault();
        }
        
        switch (e.keyCode) {
            case 32: // Spacebar - Play/Pause
                if (player.paused()) {
                    player.play();
                } else {
                    player.pause();
                }
                break;
                
            case 37: // Left Arrow - Rewind 10s
                player.currentTime(Math.max(0, player.currentTime() - 10));
                showSeekIndicator('-10s');
                break;
                
            case 39: // Right Arrow - Forward 10s
                player.currentTime(Math.min(player.duration(), player.currentTime() + 10));
                showSeekIndicator('+10s');
                break;
                
            case 38: // Up Arrow - Volume up
                player.volume(Math.min(1, player.volume() + 0.1));
                break;
                
            case 40: // Down Arrow - Volume down
                player.volume(Math.max(0, player.volume() - 0.1));
                break;
                
            case 70: // F - Fullscreen
                toggleFullscreen();
                break;
                
            case 77: // M - Mute
                player.muted(!player.muted());
                break;
                
            case 75: // K - Play/Pause (alternative)
                if (player.paused()) {
                    player.play();
                } else {
                    player.pause();
                }
                break;
                
            case 74: // J - Rewind 10s (alternative)
                player.currentTime(Math.max(0, player.currentTime() - 10));
                showSeekIndicator('-10s');
                break;
                
            case 76: // L - Forward 10s (alternative)
                player.currentTime(Math.min(player.duration(), player.currentTime() + 10));
                showSeekIndicator('+10s');
                break;
        }
        
        // Update activity
        updateActivity();
    });
}

// Show seek indicator
function showSeekIndicator(text) {
    const indicator = $(`<div class="seek-indicator">${text}</div>`);
    $('.video-wrapper').append(indicator);
    
    setTimeout(() => {
        indicator.fadeOut(() => {
            indicator.remove();
        });
    }, 1000);
}

// Activity tracking for auto-hide controls
function setupActivityTracking() {
    $('.player-container').on('mousemove click keydown', updateActivity);
    
    // Check activity every second
    setInterval(checkActivity, 1000);
}

function updateActivity() {
    lastActivity = Date.now();
    showControls();
}

function checkActivity() {
    if (Date.now() - lastActivity > 3000 && !player.paused()) {
        hideControls();
    }
}

function showControls() {
    $('.player-topbar, .player-controls').removeClass('hidden');
    $('body').removeClass('hide-cursor');
    
    if (hideControlsTimeout) {
        clearTimeout(hideControlsTimeout);
    }
}

function hideControls() {
    if (!player.paused() && isFullscreen) {
        $('.player-topbar, .player-controls').addClass('hidden');
        $('body').addClass('hide-cursor');
    }
}

// Fullscreen functionality
function toggleFullscreen() {
    if (player.isFullscreen()) {
        player.exitFullscreen();
    } else {
        player.requestFullscreen();
    }
}

function handleFullscreenChange() {
    isFullscreen = player.isFullscreen();
    
    if (isFullscreen) {
        $('body').addClass('fullscreen-mode');
        $('.fullscreen-btn i').removeClass('fa-expand').addClass('fa-compress');
    } else {
        $('body').removeClass('fullscreen-mode');
        $('.fullscreen-btn i').removeClass('fa-compress').addClass('fa-expand');
        showControls();
    }
}

// Quality selector
function setupQualitySelector() {
    const qualities = player.currentSources().map(source => source.label || 'auto');
    const qualitySelect = $('#qualitySelect');
    
    qualitySelect.empty();
    qualitySelect.append('<option value="auto">تلقائي</option>');
    
    qualities.forEach(quality => {
        if (quality !== 'auto') {
            qualitySelect.append(`<option value="${quality}">${quality}</option>`);
        }
    });
    
    // Handle quality change
    qualitySelect.change(function() {
        changeQuality($(this).val());
    });
}

function changeQuality(quality) {
    const currentTime = player.currentTime();
    const wasPaused = player.paused();
    
    // Find the source with the selected quality
    const sources = player.currentSources();
    const newSource = sources.find(source => source.label === quality) || sources[0];
    
    player.src(newSource);
    player.load();
    
    player.ready(() => {
        player.currentTime(currentTime);
        if (!wasPaused) {
            player.play();
        }
    });
}

// Playback speed
function changePlaybackSpeed(speed) {
    player.playbackRate(parseFloat(speed));
    localStorage.setItem('playbackSpeed', speed);
}

// Settings management
function loadUserSettings() {
    // Load saved settings from localStorage
    const volume = localStorage.getItem('playerVolume');
    if (volume) {
        player.volume(parseFloat(volume));
    }
    
    const speed = localStorage.getItem('playbackSpeed');
    if (speed) {
        player.playbackRate(parseFloat(speed));
        $('#speedSelect').val(speed);
    }
    
    const autoPlayNext = localStorage.getItem('autoPlayNext');
    if (autoPlayNext) {
        $('#autoPlayNext').prop('checked', autoPlayNext === 'true');
    }
    
    const skipIntroAuto = localStorage.getItem('skipIntroAuto');
    if (skipIntroAuto) {
        $('#skipIntroAuto').prop('checked', skipIntroAuto === 'true');
    }
}

function saveSettings() {
    // Save settings to localStorage
    localStorage.setItem('autoPlayNext', $('#autoPlayNext').is(':checked'));
    localStorage.setItem('skipIntroAuto', $('#skipIntroAuto').is(':checked'));
    
    // Apply subtitle settings
    const subtitleLang = $('#subtitleSelect').val();
    if (subtitleLang === 'off') {
        player.textTracks().forEach(track => {
            track.mode = 'disabled';
        });
    } else {
        player.textTracks().forEach(track => {
            track.mode = track.language === subtitleLang ? 'showing' : 'disabled';
        });
    }
    
    $('#settingsModal').modal('hide');
    showToast('تم حفظ الإعدادات بنجاح', 'success');
}

function saveVolumeSettings() {
    localStorage.setItem('playerVolume', player.volume());
}

// Video ended handler
function handleVideoEnded() {
    // Mark as completed
    saveWatchProgress(player.duration(), player.duration());
    
    // Show next episode if available
    if (currentMovieData.nextEpisode && $('#autoPlayNext').is(':checked')) {
        showNextEpisodeCountdown();
    }
}

function showNextEpisodeCountdown() {
    let countdown = 10;
    const btn = $('#nextEpisodeBtn');
    
    btn.show();
    
    const countdownInterval = setInterval(() => {
        btn.html(`<i class="fas fa-step-forward me-2"></i>الحلقة التالية (${countdown})`);
        countdown--;
        
        if (countdown < 0) {
            clearInterval(countdownInterval);
            // Auto-play next episode
            window.location.href = currentMovieData.nextEpisode.url;
        }
    }, 1000);
    
    // Allow manual click to go immediately
    btn.click(() => {
        clearInterval(countdownInterval);
        window.location.href = currentMovieData.nextEpisode.url;
    });
}

// Resume notification
function showResumeNotification() {
    const notification = $(`
        <div class="resume-notification">
            <p>هل تريد متابعة المشاهدة من حيث توقفت؟</p>
            <button class="btn btn-primary btn-sm" onclick="resumeFromProgress()">متابعة</button>
            <button class="btn btn-secondary btn-sm" onclick="startFromBeginning()">من البداية</button>
        </div>
    `);
    
    $('.video-wrapper').append(notification);
    
    // Auto-hide after 10 seconds
    setTimeout(() => {
        notification.fadeOut();
    }, 10000);
}

function resumeFromProgress() {
    $('.resume-notification').fadeOut();
    // Already set to progress position
}

function startFromBeginning() {
    player.currentTime(0);
    $('.resume-notification').fadeOut();
}

// Utility functions
function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

function showToast(message, type = 'info') {
    const toast = $(`
        <div class="toast-notification toast-${type}">
            ${message}
        </div>
    `);
    
    $('body').append(toast);
    toast.fadeIn().delay(3000).fadeOut(() => {
        toast.remove();
    });
}

// Additional player features
function downloadMovie() {
    if (!currentMovieData) return;

    // Check if user has download permission
    $.get('/api/download-check', {
        movie_id: currentMovieData.id,
        token: currentMovieData.token
    })
    .done(function(response) {
        if (response.success) {
            // Create download link
            const downloadUrl = `/api/download/${currentMovieData.id}?token=${currentMovieData.token}`;
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = `${currentMovieData.title}.mp4`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showToast('بدء التحميل...', 'success');
        } else {
            showToast(response.message || 'غير مسموح بالتحميل', 'error');
        }
    })
    .fail(function() {
        showToast('حدث خطأ في التحميل', 'error');
    });
}

// Share functionality
function shareOn(platform) {
    const url = window.location.href;
    const title = currentMovieData.title;
    const text = `شاهد فيلم "${title}" على Shahid`;

    let shareUrl = '';

    switch (platform) {
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            break;
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
            break;
        case 'whatsapp':
            shareUrl = `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`;
            break;
        case 'telegram':
            shareUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
            break;
    }

    if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
        $('#shareModal').modal('hide');
    }
}

function copyShareLink() {
    const shareLink = document.getElementById('shareLink');
    shareLink.select();
    shareLink.setSelectionRange(0, 99999); // For mobile devices

    navigator.clipboard.writeText(shareLink.value).then(function() {
        showToast('تم نسخ الرابط بنجاح', 'success');
    }).catch(function() {
        // Fallback for older browsers
        document.execCommand('copy');
        showToast('تم نسخ الرابط بنجاح', 'success');
    });
}

// Picture-in-Picture support
function togglePictureInPicture() {
    if (!document.pictureInPictureEnabled) {
        showToast('وضع الصورة في الصورة غير مدعوم', 'warning');
        return;
    }

    const video = player.el().querySelector('video');

    if (document.pictureInPictureElement) {
        document.exitPictureInPicture();
    } else {
        video.requestPictureInPicture().catch(error => {
            console.error('Error entering Picture-in-Picture:', error);
            showToast('فشل في تفعيل وضع الصورة في الصورة', 'error');
        });
    }
}

// Advanced analytics tracking
function trackVideoEvent(eventType, data = {}) {
    const eventData = {
        event: eventType,
        movie_id: currentMovieData.id,
        user_id: currentMovieData.userId,
        timestamp: Date.now(),
        current_time: player ? Math.floor(player.currentTime()) : 0,
        duration: player ? Math.floor(player.duration()) : 0,
        ...data
    };

    // Send to analytics endpoint
    $.post('/api/analytics', eventData).catch(error => {
        console.warn('Analytics tracking failed:', error);
    });
}

// Enhanced error handling with retry logic
let retryCount = 0;
const maxRetries = 3;

function handleAdvancedError() {
    const error = player.error();

    if (retryCount < maxRetries) {
        retryCount++;

        setTimeout(() => {
            console.log(`Retrying video load (attempt ${retryCount}/${maxRetries})`);
            player.load();

            // Track retry attempt
            trackVideoEvent('video_retry', {
                retry_count: retryCount,
                error_code: error ? error.code : 'unknown'
            });
        }, 2000 * retryCount); // Exponential backoff
    } else {
        // Max retries reached, show error
        handlePlayerError();
        trackVideoEvent('video_failed', {
            retry_count: retryCount,
            error_code: error ? error.code : 'unknown'
        });
    }
}

// Bandwidth detection and quality adjustment
function detectBandwidth() {
    const startTime = Date.now();
    const testImage = new Image();
    const testImageSize = 1024 * 1024; // 1MB test image

    testImage.onload = function() {
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000; // seconds
        const bitsLoaded = testImageSize * 8; // bits
        const speedBps = bitsLoaded / duration; // bits per second
        const speedMbps = speedBps / (1024 * 1024); // Mbps

        // Suggest quality based on bandwidth
        let suggestedQuality = 'auto';
        if (speedMbps > 5) {
            suggestedQuality = '1080p';
        } else if (speedMbps > 2.5) {
            suggestedQuality = '720p';
        } else if (speedMbps > 1) {
            suggestedQuality = '480p';
        } else {
            suggestedQuality = '360p';
        }

        // Auto-adjust quality if enabled
        if (localStorage.getItem('autoQuality') === 'true') {
            changeQuality(suggestedQuality);
        }

        trackVideoEvent('bandwidth_detected', {
            speed_mbps: speedMbps,
            suggested_quality: suggestedQuality
        });
    };

    testImage.onerror = function() {
        console.warn('Bandwidth detection failed');
    };

    // Use a test image from your server
    testImage.src = '/assets/images/bandwidth-test.jpg?' + Date.now();
}

// Adaptive streaming quality adjustment
function setupAdaptiveStreaming() {
    let qualityCheckInterval;

    player.on('loadedmetadata', () => {
        // Start monitoring playback quality
        qualityCheckInterval = setInterval(() => {
            if (player.paused()) return;

            const buffered = player.buffered();
            const currentTime = player.currentTime();

            if (buffered.length > 0) {
                const bufferedEnd = buffered.end(buffered.length - 1);
                const bufferHealth = bufferedEnd - currentTime;

                // Adjust quality based on buffer health
                if (bufferHealth < 5) { // Less than 5 seconds buffered
                    // Consider lowering quality
                    adjustQualityDown();
                } else if (bufferHealth > 30) { // More than 30 seconds buffered
                    // Consider raising quality
                    adjustQualityUp();
                }
            }
        }, 5000); // Check every 5 seconds
    });

    player.on('dispose', () => {
        if (qualityCheckInterval) {
            clearInterval(qualityCheckInterval);
        }
    });
}

function adjustQualityDown() {
    const currentQuality = getCurrentQuality();
    const qualities = ['1080p', '720p', '480p', '360p'];
    const currentIndex = qualities.indexOf(currentQuality);

    if (currentIndex < qualities.length - 1) {
        const newQuality = qualities[currentIndex + 1];
        changeQuality(newQuality);
        showToast(`تم تقليل الجودة إلى ${newQuality} لتحسين التشغيل`, 'info');
    }
}

function adjustQualityUp() {
    const currentQuality = getCurrentQuality();
    const qualities = ['360p', '480p', '720p', '1080p'];
    const currentIndex = qualities.indexOf(currentQuality);

    if (currentIndex < qualities.length - 1) {
        const newQuality = qualities[currentIndex + 1];
        changeQuality(newQuality);
        showToast(`تم رفع الجودة إلى ${newQuality}`, 'success');
    }
}

function getCurrentQuality() {
    const currentSource = player.currentSource();
    return currentSource ? currentSource.label || 'auto' : 'auto';
}

// Enhanced keyboard shortcuts with help overlay
function showKeyboardHelp() {
    const helpModal = $(`
        <div class="modal fade" id="keyboardHelpModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">اختصارات لوحة المفاتيح</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="keyboard-shortcuts">
                            <div class="shortcut-item">
                                <kbd>Space</kbd> أو <kbd>K</kbd>
                                <span>تشغيل/إيقاف</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>←</kbd> أو <kbd>J</kbd>
                                <span>الرجوع 10 ثوان</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>→</kbd> أو <kbd>L</kbd>
                                <span>التقدم 10 ثوان</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>↑</kbd>
                                <span>رفع الصوت</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>↓</kbd>
                                <span>خفض الصوت</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>M</kbd>
                                <span>كتم الصوت</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>F</kbd>
                                <span>ملء الشاشة</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>?</kbd>
                                <span>عرض هذه المساعدة</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `);

    $('body').append(helpModal);
    $('#keyboardHelpModal').modal('show');

    // Remove modal when hidden
    $('#keyboardHelpModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
}

// Add help shortcut to existing keyboard handler
$(document).keydown(function(e) {
    if (e.keyCode === 191 && e.shiftKey) { // Shift + ?
        e.preventDefault();
        showKeyboardHelp();
    }
});

// Initialize advanced features
function initializeAdvancedFeatures() {
    // Setup adaptive streaming
    setupAdaptiveStreaming();

    // Detect bandwidth after a delay
    setTimeout(detectBandwidth, 5000);

    // Setup Picture-in-Picture if supported
    if (document.pictureInPictureEnabled) {
        const pipButton = $(`
            <button class="btn btn-link pip-btn" onclick="togglePictureInPicture()" title="صورة في صورة">
                <i class="fas fa-external-link-alt"></i>
            </button>
        `);
        $('.controls-right').prepend(pipButton);
    }

    // Track video start
    trackVideoEvent('video_start');

    // Track video progress milestones
    player.on('timeupdate', function() {
        const currentTime = player.currentTime();
        const duration = player.duration();
        const progress = (currentTime / duration) * 100;

        // Track 25%, 50%, 75%, 100% milestones
        const milestones = [25, 50, 75, 100];
        milestones.forEach(milestone => {
            if (progress >= milestone && !player.hasTrackedMilestone?.[milestone]) {
                if (!player.hasTrackedMilestone) player.hasTrackedMilestone = {};
                player.hasTrackedMilestone[milestone] = true;
                trackVideoEvent('video_progress', { milestone });
            }
        });
    });
}

// Export additional functions for global access
window.initializePlayer = initializePlayer;
window.toggleFullscreen = toggleFullscreen;
window.skipIntro = skipIntro;
window.changePlaybackSpeed = changePlaybackSpeed;
window.saveSettings = saveSettings;
window.retryVideo = retryVideo;
window.resumeFromProgress = resumeFromProgress;
window.startFromBeginning = startFromBeginning;
window.downloadMovie = downloadMovie;
window.shareOn = shareOn;
window.copyShareLink = copyShareLink;
window.togglePictureInPicture = togglePictureInPicture;
