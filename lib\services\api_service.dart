import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiService {
  // Base URL for the API - تأكد من تغيير هذا إلى عنوان الخادم الصحيح
  static const String baseUrl = 'http://127.0.0.1/amr2/flutter_module_1/backend/api';
  
  // Timeout duration
  static const Duration timeout = Duration(seconds: 10);

  // Get API status
  static Future<Map<String, dynamic>> getStatus() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/?endpoint=status'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeout);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to get status: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Get movies list
  static Future<Map<String, dynamic>> getMovies() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/?endpoint=movies'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeout);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to get movies: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Get series list
  static Future<Map<String, dynamic>> getSeries() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/?endpoint=series'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeout);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to get series: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Get episodes for a series
  static Future<Map<String, dynamic>> getEpisodes(int seriesId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/?endpoint=episodes&series_id=$seriesId'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeout);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to get episodes: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Search content
  static Future<Map<String, dynamic>> search(String query) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/?endpoint=search&q=${Uri.encodeComponent(query)}'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeout);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to search: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Get categories
  static Future<Map<String, dynamic>> getCategories() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/?endpoint=categories'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeout);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to get categories: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Login user
  static Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/?endpoint=login'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'email': email,
          'password': password,
        }),
      ).timeout(timeout);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to login: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Register user
  static Future<Map<String, dynamic>> register({
    required String name,
    required String email,
    required String password,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/?endpoint=register'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'name': name,
          'email': email,
          'password': password,
        }),
      ).timeout(timeout);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to register: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
}

// Data Models
class Movie {
  final int id;
  final String title;
  final String? titleEn;
  final String description;
  final int year;
  final String duration;
  final String genre;
  final double rating;
  final int ratingCount;
  final String poster;
  final String? director;
  final String? cast;
  final String? country;
  final String? language;
  final String status;

  Movie({
    required this.id,
    required this.title,
    this.titleEn,
    required this.description,
    required this.year,
    required this.duration,
    required this.genre,
    required this.rating,
    required this.ratingCount,
    required this.poster,
    this.director,
    this.cast,
    this.country,
    this.language,
    required this.status,
  });

  factory Movie.fromJson(Map<String, dynamic> json) {
    return Movie(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      titleEn: json['title_en'],
      description: json['description'] ?? '',
      year: json['year'] ?? 0,
      duration: json['duration'] ?? '',
      genre: json['genre'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      ratingCount: json['rating_count'] ?? 0,
      poster: json['poster'] ?? '',
      director: json['director'],
      cast: json['cast'],
      country: json['country'],
      language: json['language'],
      status: json['status'] ?? 'active',
    );
  }
}

class Series {
  final int id;
  final String title;
  final String? titleEn;
  final String description;
  final int year;
  final int seasons;
  final int episodes;
  final int episodeCount;
  final String genre;
  final double rating;
  final int ratingCount;
  final String poster;
  final String? director;
  final String? cast;
  final String? country;
  final String? language;
  final String status;

  Series({
    required this.id,
    required this.title,
    this.titleEn,
    required this.description,
    required this.year,
    required this.seasons,
    required this.episodes,
    required this.episodeCount,
    required this.genre,
    required this.rating,
    required this.ratingCount,
    required this.poster,
    this.director,
    this.cast,
    this.country,
    this.language,
    required this.status,
  });

  factory Series.fromJson(Map<String, dynamic> json) {
    return Series(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      titleEn: json['title_en'],
      description: json['description'] ?? '',
      year: json['year'] ?? 0,
      seasons: json['seasons'] ?? 0,
      episodes: json['episodes'] ?? 0,
      episodeCount: json['episode_count'] ?? 0,
      genre: json['genre'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      ratingCount: json['rating_count'] ?? 0,
      poster: json['poster'] ?? '',
      director: json['director'],
      cast: json['cast'],
      country: json['country'],
      language: json['language'],
      status: json['status'] ?? 'active',
    );
  }
}

class Episode {
  final int id;
  final int seriesId;
  final int season;
  final int episode;
  final String title;
  final String? titleEn;
  final String description;
  final int duration;
  final String? airDate;
  final String? videoUrl;
  final String? thumbnail;
  final String status;

  Episode({
    required this.id,
    required this.seriesId,
    required this.season,
    required this.episode,
    required this.title,
    this.titleEn,
    required this.description,
    required this.duration,
    this.airDate,
    this.videoUrl,
    this.thumbnail,
    required this.status,
  });

  factory Episode.fromJson(Map<String, dynamic> json) {
    return Episode(
      id: json['id'] ?? 0,
      seriesId: json['series_id'] ?? 0,
      season: json['season'] ?? 0,
      episode: json['episode'] ?? 0,
      title: json['title'] ?? '',
      titleEn: json['title_en'],
      description: json['description'] ?? '',
      duration: json['duration'] ?? 0,
      airDate: json['air_date'],
      videoUrl: json['video_url'],
      thumbnail: json['thumbnail'],
      status: json['status'] ?? 'active',
    );
  }
}
